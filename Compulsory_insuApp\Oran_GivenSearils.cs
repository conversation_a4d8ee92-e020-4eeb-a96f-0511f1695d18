//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Compulsory_insuApp
{
    using System;
    using System.Collections.Generic;
    
    public partial class Oran_GivenSearils
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Oran_GivenSearils()
        {
            this.Oran_UsersSearils = new HashSet<Oran_UsersSearils>();
            this.Oran_SerialNums = new HashSet<Oran_SerialNums>();
        }
    
        public System.Guid SerialsGivID { get; set; }
        public Nullable<System.Guid> SrID { get; set; }
        public Nullable<System.Guid> AgencyID { get; set; }
        public Nullable<int> Start { get; set; }
        public Nullable<int> SerEnd { get; set; }
        public Nullable<int> UsedQty { get; set; }
        public Nullable<byte> Status { get; set; }
        public Nullable<System.Guid> InsertedBy { get; set; }
        public Nullable<System.DateTime> InsertDate { get; set; }
        public Nullable<System.DateTime> UpdateDate { get; set; }
        public Nullable<System.Guid> UpdatedBy { get; set; }
    
        public virtual Agency Agency { get; set; }
        public virtual Oran_Seariles Oran_Seariles { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Oran_UsersSearils> Oran_UsersSearils { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Oran_SerialNums> Oran_SerialNums { get; set; }
    }
}
