﻿using Compulsory_insuApp.Models;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Mvc;

using static System.Net.Mime.MediaTypeNames;

namespace Compulsory_insuApp.Controllers
{
    public class a60Controller : Controller
    {
        Insh_AppsDBEntities db = new Insh_AppsDBEntities();
        Insur_class ic = new Insur_class();

        public ActionResult a60000(Guid Atu)
        {
            try
            {
                if (db.AgUsers.Any(c => c.AgUserID == Atu))
                {
                    return PartialView("MidRes_IssurancePage");
                }
                else
                {
                    return View("LoginPage");
                }
            }
            catch (Exception ex)
            {
                return View("LoginPage");
            }
        }
        [HttpPost]
        public ActionResult a600015(Med_ResCl MainObj)
        {
            try
            {
                var User = db.AgUsers.Find(MainObj.InsertedBy);
                if (User == null || User.AgentId != User.AgentId)
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                var sn = db.MidcRes_SerialNums.Where(p => p.SerialNumID == MainObj.pap_no && p.MidcRes_SearStatus.ID == 1 && p.AgentID == MainObj.AgencyID).ToList();
                if (!sn.Any())
                    return Json(new { ErrorCode = 4 });
                var Agen = db.Agency.Find(MainObj.AgencyID);
                MidRes_InsuranceDocument obj = new MidRes_InsuranceDocument();
                obj.Insu_ID = Guid.NewGuid();
                obj.AgencyID = User.AgentId;
                obj.Date_From = MainObj.Date_From;
                obj.Date_To = MainObj.Date_From.AddMonths(MainObj.Duration);
                obj.Duration = MainObj.Duration;
                obj.DealerName = MainObj.DealerName;
                obj.ProfessionID = MainObj.ProfessionID;
                obj.CustomerName = MainObj.CustomerName;
                obj.DealerCustomerReleation = MainObj.DealerCustomerReleation;
                obj.NationalityID = MainObj.NationalityID;
                obj.AddressID = MainObj.AddressID;
                obj.BirthDate = new DateTime(MainObj.BirthDate, 1, 1);
                obj.BirthPlace = MainObj.BirthPlace;
                obj.MaritalStatusID = MainObj.MaritalStatusID;
                obj.AcademicQualificationID = !MainObj.IsSalary ? MainObj.AcademicQualificationID: Guid.Parse("b8ef727f-3261-4e47-9182-17dbe1c0f023") ;
                obj.IsSalary = MainObj.IsSalary;
                obj.Percentage =!MainObj.IsSalary ? 0:(float?) MainObj.Percentage?? 0;
                obj.Salary = !MainObj.IsSalary ? 0:(float?) MainObj.Salary ?? 0;
                float Sattlement = 0;
                if (MainObj.IsSalary)
                {
                    Sattlement = (float)(obj.Salary * (obj.Percentage / 100)) * MainObj.Duration;
                }
                else
                {
                    var qul = db.MidRes_AcademicQualification.Find(obj.AcademicQualificationID);
                    Sattlement =((float?) qul.Price ?? 0) * MainObj.Duration;
                }
                obj.Sattlement = Sattlement;
                obj.Supervision = (obj.Sattlement ?? 0) * 0.005;
                float x = (float)((obj.Sattlement ?? 0) * 0.01);
                obj.Tax = ic.RoudToPoint5(x);
                obj.Version =10;
                obj.Stamp = 0.25;
                obj.Status = 1;
                obj.InsertedBy = MainObj.InsertedBy;
                obj.InsertedDate = DateTime.Now;
                obj.IsDelProg = false;
                obj.Taxtotal = obj.Supervision + obj.Tax + obj.Version + obj.Stamp;
                obj.Total = obj.Sattlement + obj.Supervision + obj.Tax + obj.Version + obj.Stamp;
                db.MidRes_InsuranceDocument.Add(obj);
                db.SaveChanges();
                obj.SN = DateTime.Now.ToUniversalTime().AddHours(2).Date.Year.ToString() + " " + obj.Doc_Num + " - " + Agen.AgNum.ToString() + " " + obj.ID_number;
                db.MidRes_InsuranceDocument.Attach(obj);
                db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                db.SaveChanges();
                var sns = db.MidcRes_SerialNums.Find(obj.Doc_Num);
                sns.Status = 8;
                db.MidcRes_SerialNums.Attach(sns);
                db.Entry(sns).State = System.Data.Entity.EntityState.Modified;
                db.SaveChanges();
                return Json(new { ErrorCode = 0 });
            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }

        [HttpPost]
  

        public ActionResult a60011(Guid UserID ,Guid AgencyID)
        {
            try
            {
                DateTime dt = DateTime.Now.ToUniversalTime().AddHours(2).AddDays(-7);
                var aa = db.MidRes_InsuranceDocument.Where(p => p.Status != 3 && p.InsertedDate >= dt && p.AgencyID == AgencyID).ToList();
                var Obj = aa.Select(c => new
                {
                    ID = c.Insu_ID,
                    CardNo = c.SN,
                    SDate = c.Date_From.Value.ToString("yyyy-MM-dd"),
                    Stime = c.Date_From.Value.ToString("hh-mm-tt"),
                    Sday = c.Date_From.Value.ToString("dddd"),
                    EDate = c.Date_To.Value.ToString("yyyy-MM-dd"),
                    Etime = c.Date_To.Value.ToString("hh-mm-tt"),
                    Eday = c.Date_To.Value.ToString("dddd"),
                    ag_name = db.Agency.Find(c.AgencyID).AgencyName,
                    Addriss = c.Agency.Cities.CityName,
                    c.Duration,
                    c.ProfessionID,
                    CustName = c.CustomerName,
                    CalWay = (c.IsSalary ?? false) ? "مرتب" : "مؤهل علمي",
                    c.DealerName,
                    Deal_relation = c.DealerCustomerReleation,
                    B_Year = c.BirthDate.Value.Year,
                    Insertyear = c.InsertedDate.Value.ToString("yyyy"),
                    Insertmonth = c.InsertedDate.Value.ToString("MM"),
                    Insertday = c.InsertedDate.Value.ToString("dd"),
                    Insertdayow = c.InsertedDate.Value.ToString("dddd"),
                    InsertBy = db.AgUsers.Find(c.InsertedBy).FullName,
                    ins_val = c.Sattlement,
                    TaxTotal = c.Taxtotal,
                    stamp = c.Stamp,
                    Version = c.Version,
                    Tax = c.Tax,
                    c.IsDelProg,
                    tot = c.Total ?? 0,
                }).OrderByDescending(c => c.Insertyear).ToList();

                return Json(new { ErrorCode = 0, Obj });
            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
        public ActionResult a600013(Guid ID, Guid UserID)
        {
            try
            {
                var User = db.AgUsers.Find(UserID);
                if (User == null)
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                MidRes_InsuranceDocument obj = db.MidRes_InsuranceDocument.Find(ID);
                obj.IsDelProg = true;
                obj.UpdatedDate = DateTime.Now.ToUniversalTime().AddHours(2);
                obj.UpdatedBy = UserID;
                db.MidRes_InsuranceDocument.Attach(obj);
                db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                db.SaveChanges();
                NotesTB nObj = new NotesTB();
                nObj.ID = Guid.NewGuid();
                nObj.DocID = ID;
                nObj.Status = 1;
                nObj.InsertBy = UserID;
                nObj.InsertDate = DateTime.Now;
                nObj.SenderType = 3;
                db.NotesTB.Add(nObj);
                db.SaveChanges();

                return Json(new { ErrorCode = 0, });
            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }

        public ActionResult a600012(Guid ID, Guid UserID)
        {
            try
            {
                var User = db.AgUsers.Find(UserID);
                if (User == null || User.AgentId != User.AgentId)
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                var us = db.AgUsers.Find(UserID);
                if (us == null)
                    return Json(new { ErrorCode = 3 });
                var Obj = db.MidRes_InsuranceDocument.Find(ID);
                MedResDoc_PrintCl doccl = new MedResDoc_PrintCl();
                var Doc = doccl.Doc_print(Obj);
                return Json(new { ErrorCode = 0, Doc });

            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }

       

        public JsonResult a60005(Guid AgencyID, Guid UserID)
        {
            try
            {
                var User = db.AgUsers.Find(UserID);
                if (User == null || User.AgentId != AgencyID)
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                var qualifications = db.MidRes_AcademicQualification
                    .Where(q => q.Status == 1)
                    .Select(q => new {
                        ID = new {
                            ID = q.AcademicQualificationID,
                            Value = q.Price
                        },
                        Name = q.AcademicQualificationName
                    })
                    .ToList();
                var MaritalStatusList = db.MidcRes_MaritalStatus.Where(c => c.Status == 1).Select(c => new { 
                 ID = c.MaritalStatusID,
                 Name = c.MaritalStatus,
                }).ToList();
            
                return Json(new { ErrorCode = 0, Data = qualifications , MaritalStatusList  });
            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, Message = "حدث خطأ الرجاء مخاطبة قسم الدعم" });
            }
        }

        public JsonResult a60006(Guid AgencyID, Guid UserID)
        {
            try
            {
                var User = db.AgUsers.Find(UserID);
                if (User == null || User.AgentId != AgencyID)
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                var addresses = db.MidcRes_Address
                    .Where(a => a.Status == 1)
                    .Select(a => new {
                        ID = a.AddressID,
                        Name = a.AddressName
                    })
                    .ToList();

                return Json(new { ErrorCode = 0, Data = addresses }, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, Message = "حدث خطأ الرجاء مخاطبة قسم الدعم" }, JsonRequestBehavior.AllowGet);
            }
        }

        public JsonResult a60007(Guid AgencyID, Guid UserID)
        {
            try
            {
                var User = db.AgUsers.Find(UserID);
                if (User == null || User.AgentId != AgencyID)
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                var nationalities = db.MidcRes_Nationality
                    .Where(n => n.Status == 1)
                    .Select(n => new {
                        ID = n.NationalityID,
                        Name = n.NationalityName
                    })
                    .ToList();

                return Json(new { ErrorCode = 0, Data = nationalities }, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, Message = "حدث خطأ الرجاء مخاطبة قسم الدعم" }, JsonRequestBehavior.AllowGet);
            }
        }

        public JsonResult a60008(Guid AgencyID, Guid UserID)
        {
            try
            {
                var User = db.AgUsers.Find(UserID);
                if (User == null || User.AgentId != AgencyID)
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                var professions = db.MidcRes_Profession
                    .Where(p => p.Status == 1)
                    .Select(p => new {
                        ID = p.ProfessionID,
                        Name = p.ProfessionName,
                        Value =p.Price,
                    })
                    .ToList();

                return Json(new { ErrorCode = 0, Data = professions }, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, Message = "حدث خطأ الرجاء مخاطبة قسم الدعم" }, JsonRequestBehavior.AllowGet);
            }
        }

        public JsonResult a60009(Guid AgencyID, Guid UserID)
        {
            try
            {
                var percentages = db.MidRes_Percentage
                    .Where(p => p.Status == 1)
                    .Select(p => new {
                        Value = p.Percentage,
                        Name = p.Percentage.ToString()
                    })
                    .ToList();
                var HasFreePer = db.AgencyPermission.Where(c => c.Status == 1 && c.AgencyID == AgencyID && c.AgPermissionID == 10).ToList();
                var MangSettings = new
                {
                    InsValue = 10,
                    StampVal = 0.25,
                    IsFreePer = HasFreePer.Count() == 1 ? true :false
                };
                return Json(new { ErrorCode = 0, Data = percentages , MangSettings });
            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, Message = "حدث خطأ الرجاء مخاطبة قسم الدعم" });
            }
        }
    }
}