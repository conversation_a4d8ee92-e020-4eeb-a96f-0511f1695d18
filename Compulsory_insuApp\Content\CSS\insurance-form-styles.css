﻿/* تنظيم المجموعات */
.input-group {
    margin-bottom: 1.5rem !important;
}

/* تحسين محددات الإدخال */
.form-control[type="number"] {
    text-align: center;
}

.form-control[type="date"] {
    text-align: center;
}

/* تحسين المسافات والهوامش */
.row > [class*="col-"] {
    margin-bottom: 1.25rem;
}

/* تنظيم الحقول في مجموعات */
.form-section {
    background: #ffffff;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid #e0e0e0;
}

.form-section-title {
    color: #2196f3;
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e3f2fd;
    display: flex;
    align-items: center;
}

.form-section-title i {
    margin-left: 0.5rem;
    font-size: 1.2rem;
}

/* تحسين عرض التسميات */
label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #424242;
}

label.required::after {
    content: ' *';
    color: #ff9800;
}

/* تنظيم الحقول المرتبطة */
.linked-fields {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
}

/* تحسين عرض الأخطاء */
.validation-message {
    font-size: 0.875rem;
    color: #ff9800;
    margin-top: 0.25rem;
    display: none;
}

.ng-invalid.ng-touched + .validation-message {
    display: block;
}

/* تحسين عرض الحقول المعطلة */
.form-control:disabled,
.form-control[readonly] {
    background-color: #f5f5f5;
    cursor: not-allowed;
}

/* تحسين مظهر الأيقونات في الحقول */
.input-group-text {
    background-color: #f8f9fa;
    border-left: none;
}

.input-group > .form-control {
    border-right: none;
}

/* تحسين مظهر القوائم المنسدلة */
.form-select {
    padding-right: 2rem;
    background-position: left 0.75rem center;
}

/* تحسين مظهر الحقول الرقمية */
input[type="number"] {
    -moz-appearance: textfield;
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* تحسين مظهر التواريخ */
input[type="date"] {
    position: relative;
}

input[type="date"]::-webkit-calendar-picker-indicator {
    background: transparent;
    bottom: 0;
    color: transparent;
    cursor: pointer;
    height: auto;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    width: auto;
}

/* تحسين مظهر المجموعات */
fieldset {
    padding: 1.5rem;
    margin-bottom: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    background: linear-gradient(145deg, #ffffff, #f5f5f5) !important;
    border: 1px solid #e0e0e0;
}

fieldset legend {
    width: auto;
    padding: 0 1rem;
    margin-bottom: 1.5rem;
    background-color: #2196f3;
    color: white;
    border-radius: 4px;
}

/* تحسين مظهر الأزرار */
.btn {
    min-width: 120px;
    padding: 0.5rem 1.5rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-weight: 500;
}

/* تحسين مظهر الرسائل */
.help-text {
    font-size: 0.875rem;
    color: #666;
    margin-top: 0.25rem;
}

/* تحسين مظهر الحقول الإلزامية */
.required-field {
    position: relative;
}

.required-field::after {
    content: '*';
    color: #ff9800;
    position: absolute;
    top: 0;
    right: -10px;
}

/* تحسين مظهر المجموعات المتداخلة */
.nested-group {
    padding-right: 1rem;
    border-right: 2px solid #e3f2fd;
    margin-right: 1rem;
}

/* تحسين مظهر العناوين الفرعية */
.sub-title {
    font-size: 1rem;
    color: #666;
    margin-bottom: 1rem;
    padding-right: 1rem;
    border-right: 3px solid #2196f3;
}

/* تحسين مظهر الحقول المرتبطة */
.dependent-field {
    position: relative;
    padding-right: 1.5rem;
}

.dependent-field::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #e3f2fd;
}

/* تحسين مظهر المجموعات المشروطة */
.conditional-group {
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 6px;
    margin-top: 1rem;
    border: 1px dashed #dee2e6;
}

/* تحسين مظهر التنقل بين الحقول */
.form-control:focus,
.form-select:focus {
    z-index: 1;
    position: relative;
}

/* تحسين مظهر md-autocomplete */
md-autocomplete.md-default-theme, md-autocomplete {
    background: #fff !important;
    height: 38px !important;
}

md-autocomplete md-autocomplete-wrap {
    height: 38px !important;
    box-shadow: none !important;
    border: 1px solid #ced4da !important;
    border-radius: 4px !important;
}

md-autocomplete input:not(.md-input) {
    height: 36px !important;
    line-height: 36px !important;
    padding: 0 12px !important;
}

md-autocomplete.ng-invalid.ng-touched md-autocomplete-wrap {
    border-color: #dc3545 !important;
}

md-autocomplete.ng-valid.ng-touched md-autocomplete-wrap {
    border-color: #198754 !important;
}

.md-virtual-repeat-container.md-autocomplete-suggestions-container {
    box-shadow: 0 4px 6px rgba(0,0,0,0.1) !important;
    border-radius: 4px !important;
    margin-top: 2px !important;
}

.md-virtual-repeat-container.md-not-found {
    margin-top: 2px !important;
    padding: 8px !important;
    text-align: center !important;
    color: #dc3545 !important;
}

md-autocomplete .md-show-clear-button button {
    margin: 0 !important;
    padding: 0 !important;
    width: 24px !important;
    height: 24px !important;
}

/* تحسين مظهر القائمة المنسدلة */
.md-autocomplete-suggestions {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

.md-autocomplete-suggestions li {
    height: 36px !important;
    line-height: 36px !important;
    padding: 0 12px !important;
}

.md-autocomplete-suggestions li:hover {
    background-color: #f8f9fa !important;
}

.md-autocomplete-suggestions li.selected {
    background-color: #e9ecef !important;
}

/* تحسين مظهر الحقل عند التركيز */
md-autocomplete.md-default-theme.md-focused:not([disabled]) md-autocomplete-wrap:not([disabled]),
md-autocomplete.md-focused:not([disabled]) md-autocomplete-wrap:not([disabled]) {
    border-color: #86b7fe !important;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25) !important;
}

/* تحسين مظهر md-autocomplete */
md-autocomplete.md-default-theme[disabled]:not([md-floating-label]), 
md-autocomplete[disabled]:not([md-floating-label]) {
    background-color: #f5f5f5 !important;
}

md-autocomplete.md-default-theme .md-input, 
md-autocomplete .md-input {
    border-color: transparent !important;
}

md-autocomplete.md-default-theme:not([disabled]):not([md-floating-label]):hover,
md-autocomplete:not([disabled]):not([md-floating-label]):hover {
    border-color: #90caf9 !important;
    box-shadow: 0 0 0 0.1rem rgba(33, 150, 243, 0.1) !important;
}

md-autocomplete-wrap.md-default-theme.md-whiteframe-z1,
md-autocomplete-wrap.md-whiteframe-z1 {
    box-shadow: none !important;
}

md-autocomplete md-progress-linear {
    display: none;
}

/* تحسين قائمة الاقتراحات */
.md-virtual-repeat-container.md-autocomplete-suggestions-container {
    box-shadow: 0 4px 6px rgba(0,0,0,0.1) !important;
    border-radius: 0 0 6px 6px !important;
    margin-top: 2px !important;
}

.md-virtual-repeat-container.md-not-found {
    border: 1px solid #e0e0e0 !important;
    border-radius: 6px !important;
    margin-top: 2px !important;
}

/* تحسين مظهر الحقول عند الطباعة */
@media print {
    input, select, textarea {
        border: 1px solid #000 !important;
    }
    
    .form-section {
        break-inside: avoid;
    }
}

/* تحسين مظهر Modal */
.modal-header {
    background: linear-gradient(145deg, #2196f3, #1976d2) !important;
    border-bottom: none;
}

.modal-body {
    background: #ffffff !important;
    padding: 1.5rem;
}

.modal-footer {
    background: #f5f5f5 !important;
    border-top: 1px solid #e0e0e0;
}

.modal-content {
    border: none;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    background-color: #ffffff;
}

/* تحسين مظهر الأرقام */
.badge {
    font-size: 1rem;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
}

.badge-primary { background-color: #2196f3; }
.badge-success { background-color: #4caf50; }
.badge-info { background-color: #03a9f4; }
.badge-warning { background-color: #ff9800; color: #fff; }

/* تحسين مظهر التنبيهات */
.alert {
    border-radius: 8px;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.alert-info { background-color: #e3f2fd; color: #1976d2; }
.alert-success { background-color: #e8f5e9; color: #2e7d32; }
.alert-warning { background-color: #fff3e0; color: #f57c00; }

/* تحسين مظهر لوحة الاحتساب */
.calculation-panel {
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    border-radius: 8px;
    padding: 1.5rem;
    height: auto;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid #dee2e6;
}

.calculation-header {
    color: #2c3e50;
    font-size: 1.2rem;
    font-weight: 600;
    text-align: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.calculation-header i {
    margin-left: 0.5rem;
    color: #2196f3;
}

.calculation-items {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.calc-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.calc-item:hover {
    background: #f8f9fa;
    border-color: #dee2e6;
}

.calc-label {
    color: #495057;
    font-size: 1rem;
    font-weight: 500;
}

.calc-value {
    color: #2c3e50;
    font-family: 'Courier New', monospace;
    font-size: 1.1rem;
    font-weight: 600;
    background: #e9ecef;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    min-width: 120px;
    text-align: center;
    border: 1px solid #dee2e6;
}

.calc-total {
    margin-top: 1.5rem;
    padding: 1rem;
    background: #2196f3;
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(33, 150, 243, 0.2);
}

.total-label {
    color: #fff;
    font-size: 1.2rem;
    font-weight: 600;
}

.total-value {
    color: #fff;
    font-family: 'Courier New', monospace;
    font-size: 1.5rem;
    font-weight: 700;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    min-width: 150px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* تحسين مظهر الأرقام */
.calc-value, .total-value {
    direction: ltr;
    letter-spacing: 1px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* تأثيرات إضافية */
.calc-item:nth-child(odd) {
    background: #f8f9fa;
}

.calc-item:nth-child(even) {
    background: #ffffff;
}

/* تحسين مظهر القيم السالبة والموجبة */
.calc-value.positive {
    color: #28a745;
}

.calc-value.negative {
    color: #dc3545;
}

/* تحسين مظهر التحقق من الصحة */
.form-group {
    position: relative;
    margin-bottom: 1.5rem;
}

.form-control.ng-touched.ng-invalid,
.form-select.ng-touched.ng-invalid,
md-autocomplete.ng-touched.ng-invalid {
    border-color: #dc3545;
    padding-right: 2.25rem;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.ng-touched.ng-valid,
.form-select.ng-touched.ng-valid,
md-autocomplete.ng-touched.ng-valid {
    border-color: #198754;
    padding-right: 2.25rem;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.validation-message {
    position: absolute;
    bottom: -20px;
    right: 0;
    font-size: 0.8rem;
    color: #dc3545;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.ng-touched.ng-invalid ~ .validation-message {
    opacity: 1;
    transform: translateY(0);
}

/* تحسين مظهر الحقول المطلوبة */
.required::after {
    content: ' *';
    color: #dc3545;
    margin-right: 4px;
}

/* تحسين مظهر التركيز */
.form-control:focus,
.form-select:focus,
md-autocomplete.md-focused {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-control.ng-touched.ng-invalid:focus,
.form-select.ng-touched.ng-invalid:focus,
md-autocomplete.ng-touched.ng-invalid.md-focused {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}

.form-control.ng-touched.ng-valid:focus,
.form-select.ng-touched.ng-valid:focus,
md-autocomplete.ng-touched.ng-valid.md-focused {
    border-color: #198754;
    box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
}

/* تحسين مظهر md-autocomplete */
md-autocomplete.ng-touched.ng-invalid md-autocomplete-wrap {
    border-color: #dc3545 !important;
    background-color: #fff !important;
}

md-autocomplete.ng-touched.ng-valid md-autocomplete-wrap {
    border-color: #198754 !important;
    background-color: #fff !important;
}

/* تحسين مظهر رسائل الخطأ */
.field-validation-error {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #dc3545;
}

/* تحسين مظهر الحقول عند التحقق */
.input-validation-error {
    border-color: #dc3545;
    padding-right: 2.25rem;
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* إضافة أيقونات للحقول */
.input-group-text {
    background-color: transparent;
    border-left: 0;
}

.form-control {
    border-right: 0;
}

.input-group:focus-within .input-group-text {
    border-color: #86b7fe;
}

.input-group.is-invalid .input-group-text {
    border-color: #dc3545;
    color: #dc3545;
}

.input-group.is-valid .input-group-text {
    border-color: #198754;
    color: #198754;
}