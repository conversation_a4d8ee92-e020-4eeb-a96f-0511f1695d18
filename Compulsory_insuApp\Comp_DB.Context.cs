﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Compulsory_insuApp
{
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Infrastructure;
    
    public partial class Insh_AppsDBEntities : DbContext
    {
        public Insh_AppsDBEntities()
            : base("name=Insh_AppsDBEntities")
        {
        }
    
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            throw new UnintentionalCodeFirstException();
        }
    
        public virtual DbSet<AgenCat> AgenCat { get; set; }
        public virtual DbSet<AgencyPermission> AgencyPermission { get; set; }
        public virtual DbSet<AgencySystems> AgencySystems { get; set; }
        public virtual DbSet<AgUsers> AgUsers { get; set; }
        public virtual DbSet<Balance> Balance { get; set; }
        public virtual DbSet<CarsBrand> CarsBrand { get; set; }
        public virtual DbSet<Cities> Cities { get; set; }
        public virtual DbSet<Colors> Colors { get; set; }
        public virtual DbSet<CompulsoryInsurenceTB> CompulsoryInsurenceTB { get; set; }
        public virtual DbSet<CompulsoryPriceDetails> CompulsoryPriceDetails { get; set; }
        public virtual DbSet<CompulsoryPriceMaster> CompulsoryPriceMaster { get; set; }
        public virtual DbSet<CoNews> CoNews { get; set; }
        public virtual DbSet<ConMangUsers> ConMangUsers { get; set; }
        public virtual DbSet<ContryMangments> ContryMangments { get; set; }
        public virtual DbSet<Country> Country { get; set; }
        public virtual DbSet<Country_Condition> Country_Condition { get; set; }
        public virtual DbSet<Cus_Companies> Cus_Companies { get; set; }
        public virtual DbSet<DocTypes> DocTypes { get; set; }
        public virtual DbSet<DurationsCat> DurationsCat { get; set; }
        public virtual DbSet<DuratuionTB> DuratuionTB { get; set; }
        public virtual DbSet<GivenSearils> GivenSearils { get; set; }
        public virtual DbSet<MidcRes_Address> MidcRes_Address { get; set; }
        public virtual DbSet<MidcRes_GivenSearils> MidcRes_GivenSearils { get; set; }
        public virtual DbSet<MidcRes_MaritalStatus> MidcRes_MaritalStatus { get; set; }
        public virtual DbSet<MidcRes_Nationality> MidcRes_Nationality { get; set; }
        public virtual DbSet<MidcRes_PrintedSerals> MidcRes_PrintedSerals { get; set; }
        public virtual DbSet<MidcRes_PrinterTB> MidcRes_PrinterTB { get; set; }
        public virtual DbSet<MidcRes_Profession> MidcRes_Profession { get; set; }
        public virtual DbSet<MidcRes_Seariles> MidcRes_Seariles { get; set; }
        public virtual DbSet<MidcRes_SearStatus> MidcRes_SearStatus { get; set; }
        public virtual DbSet<MidcRes_SerialNums> MidcRes_SerialNums { get; set; }
        public virtual DbSet<MidcRes_UsersSearils> MidcRes_UsersSearils { get; set; }
        public virtual DbSet<NotesTB> NotesTB { get; set; }
        public virtual DbSet<Offices> Offices { get; set; }
        public virtual DbSet<oldOwner> oldOwner { get; set; }
        public virtual DbSet<Orang_Country> Orang_Country { get; set; }
        public virtual DbSet<Orange_Insurance_Clause> Orange_Insurance_Clause { get; set; }
        public virtual DbSet<Orange_Insurance_Policy> Orange_Insurance_Policy { get; set; }
        public virtual DbSet<Orange_nots_replays> Orange_nots_replays { get; set; }
        public virtual DbSet<Orange_poli_Nots> Orange_poli_Nots { get; set; }
        public virtual DbSet<OrangeCars> OrangeCars { get; set; }
        public virtual DbSet<OtherServHis> OtherServHis { get; set; }
        public virtual DbSet<OtherSettings> OtherSettings { get; set; }
        public virtual DbSet<PrintedSerals> PrintedSerals { get; set; }
        public virtual DbSet<PrinterTB> PrinterTB { get; set; }
        public virtual DbSet<ResAgencies> ResAgencies { get; set; }
        public virtual DbSet<Seariles> Seariles { get; set; }
        public virtual DbSet<SearStatus> SearStatus { get; set; }
        public virtual DbSet<SerialNums> SerialNums { get; set; }
        public virtual DbSet<Systems> Systems { get; set; }
        public virtual DbSet<Trav_AgesTypes> Trav_AgesTypes { get; set; }
        public virtual DbSet<Trav_Balance> Trav_Balance { get; set; }
        public virtual DbSet<Trav_Cities> Trav_Cities { get; set; }
        public virtual DbSet<Trav_Country> Trav_Country { get; set; }
        public virtual DbSet<Trav_DocReNew> Trav_DocReNew { get; set; }
        public virtual DbSet<Trav_DocReplacment> Trav_DocReplacment { get; set; }
        public virtual DbSet<Trav_DocTax> Trav_DocTax { get; set; }
        public virtual DbSet<Trav_DocumentType> Trav_DocumentType { get; set; }
        public virtual DbSet<Trav_DurationTypes> Trav_DurationTypes { get; set; }
        public virtual DbSet<Trav_GivenSearils> Trav_GivenSearils { get; set; }
        public virtual DbSet<Trav_OtherServPrices> Trav_OtherServPrices { get; set; }
        public virtual DbSet<Trav_PrintedSerals> Trav_PrintedSerals { get; set; }
        public virtual DbSet<Trav_PrinterTB> Trav_PrinterTB { get; set; }
        public virtual DbSet<Trav_Questions> Trav_Questions { get; set; }
        public virtual DbSet<Trav_Seariles> Trav_Seariles { get; set; }
        public virtual DbSet<Trav_SearStatus> Trav_SearStatus { get; set; }
        public virtual DbSet<Trav_SerialNums> Trav_SerialNums { get; set; }
        public virtual DbSet<Trav_UsersSearils> Trav_UsersSearils { get; set; }
        public virtual DbSet<Trav_Virebles> Trav_Virebles { get; set; }
        public virtual DbSet<Trav_Zoon> Trav_Zoon { get; set; }
        public virtual DbSet<UsersSearils> UsersSearils { get; set; }
        public virtual DbSet<VehicleNationality> VehicleNationality { get; set; }
        public virtual DbSet<Virebles> Virebles { get; set; }
        public virtual DbSet<Trav_InsuranceDocument> Trav_InsuranceDocument { get; set; }
        public virtual DbSet<MidRes_AcademicQualification> MidRes_AcademicQualification { get; set; }
        public virtual DbSet<MidRes_Percentage> MidRes_Percentage { get; set; }
        public virtual DbSet<Smart_page_Cus> Smart_page_Cus { get; set; }
        public virtual DbSet<AgPermissionsList> AgPermissionsList { get; set; }
        public virtual DbSet<Agency> Agency { get; set; }
        public virtual DbSet<AgenSysProfit> AgenSysProfit { get; set; }
        public virtual DbSet<MidRes_InsuranceDocument> MidRes_InsuranceDocument { get; set; }
        public virtual DbSet<Oran_GivenSearils> Oran_GivenSearils { get; set; }
        public virtual DbSet<Oran_PrintedSerals> Oran_PrintedSerals { get; set; }
        public virtual DbSet<Oran_Seariles> Oran_Seariles { get; set; }
        public virtual DbSet<Oran_UsersSearils> Oran_UsersSearils { get; set; }
        public virtual DbSet<monthlyAgenFinnce> monthlyAgenFinnce { get; set; }
        public virtual DbSet<EquipmentCategories> EquipmentCategories { get; set; }
        public virtual DbSet<SerialHistory> SerialHistory { get; set; }
        public virtual DbSet<SerialStatuses> SerialStatuses { get; set; }
        public virtual DbSet<StockInventory> StockInventory { get; set; }
        public virtual DbSet<StockReceipts> StockReceipts { get; set; }
        public virtual DbSet<StockRequestDetails> StockRequestDetails { get; set; }
        public virtual DbSet<UnifiedRequests> UnifiedRequests { get; set; }
        public virtual DbSet<AgencyEquipment> AgencyEquipment { get; set; }
        public virtual DbSet<Comp_Agency> Comp_Agency { get; set; }
        public virtual DbSet<Oran_SearStatus> Oran_SearStatus { get; set; }
        public virtual DbSet<RequestTypes> RequestTypes { get; set; }
        public virtual DbSet<Oran_SerialNums> Oran_SerialNums { get; set; }
    }
}
