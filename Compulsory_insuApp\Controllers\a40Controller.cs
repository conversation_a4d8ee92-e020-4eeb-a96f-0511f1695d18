﻿using Compulsory_insuApp.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.Linq;
using System.Security.Cryptography;
using System.Web.Mvc;

namespace Compulsory_insuApp.Controllers
{
    public class a40Controller : Controller
    {
        Insh_AppsDBEntities db = new Insh_AppsDBEntities();
        Insur_class ic = new Insur_class();
        public ActionResult a40000(Guid Atu)
        {
            if (db.AgUsers.Any(c => c.AgUserID == Atu))
            {
                ViewBag.CompID = db.Smart_page_Cus.SingleOrDefault(c => c.IsActive == true).id;
                return PartialView("IssuancePage");
            }
            else
            {
                return View("LoginPage");
            }
        }
        public ActionResult a400061(Guid UserID, Guid AgencyID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                var User = db.AgUsers.Find(UserID);
                if (User == null)
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                DateTime dt = DateTime.Now.ToUniversalTime().AddHours(2).AddDays(-40);
                ToWord tw;
                var Obj = db.CompulsoryInsurenceTB.Where(p => p.Status != 3 && p.InsertDate >= dt && p.AgencyID == AgencyID)
                    .Select(c => new
                    {
                        c.DocType,
                        c.DocTypes.DocTypeDesc,
                        c.Engine_Capacity,
                        c.CarsBrand.CarNaame,
                        c.Colors.ColorNaame,
                        c.Insu_ID,
                        c.CompID,
                        c.Cus_Companies.CompName,
                        c.CusName,
                        c.ColorID,
                        c.InsValue,
                        c.Ins_SN,
                        c.IronBoard_num,
                        c.IsInDeleteProgress,
                        c.TaxTotal,
                        c.TotalWithTax,
                        c.InsertBy,
                        c.InsertDate,
                        IsValid = c.IsActivete,
                        c.DateTo,
                    }).OrderByDescending(c => c.InsertDate).ToList();
                var InsObj = Obj.Select(c => new
                {
                    Ins_SN = c.Ins_SN,
                    DocType_ID = c.DocType,
                    TypeDesc = c.DocTypeDesc,
                    Com_Name = c.CompName,
                    c_name = c.CusName,
                    c.IronBoard_num,
                    ins_ID = c.Insu_ID,
                    IsDelProg = c.IsInDeleteProgress,
                    color = c.ColorNaame,
                    iron_bord = c.IronBoard_num,
                    car_name = c.CarNaame,
                    ins_val = c.InsValue,
                    TaxVal = c.TaxTotal,
                    total_tax = c.TotalWithTax,
                    CanBeRenew = c.IsValid,
                    CanBeExtended = c.IsValid && c.DocType == 1,
                    CanBeChangeOwner = c.DateTo >= DateTime.Now.Date && c.DocType == 1,
                    InsertBy = db.AgUsers.Find(c.InsertBy).AgUserName,
                    InsertDate = c.InsertDate.ToString("yyyy-MM-ddTHH:mm:ss")
                }).ToList();
                return Json(new { ErrorCode = 0, InsObj });

            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
        public ActionResult a400053(Guid AgEncyID, Guid UserID)
        {
            try
            {

                var us = db.AgUsers.Find(UserID);
                if (us == null || us.AgentId != AgEncyID)
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                DateTime dt = DateTime.Now.AddDays(-15);
                var ss = db.oldOwner.Where(c => DbFunctions.TruncateTime(c.CompulsoryInsurenceTB.InsertDate) >= DbFunctions.TruncateTime(dt)
                && c.CompulsoryInsurenceTB.Status == 1).Select(c => new { 
                  c.CompulsoryInsurenceTB.Ins_SN,
                  c.oldDocNum,
                  c.CompulsoryInsurenceTB.CarsBrand.CarNaame,
                  c.CompulsoryInsurenceTB.CusName,
                  c.CompulsoryInsurenceTB.InsertDate,
                  c.CompulsoryInsurenceTB.DocType,
                  c.CompulsoryInsurenceTB.InsertBy,
                }).ToList();
                var OldDocsRep = ss.Select(c => new
                {
                    NewDocSN = c.Ins_SN,
                    carName = c.CarNaame,
                    cusNume=c.CusName,
                    OldDocSn = c.oldDocNum,
                    InsertDate = c.InsertDate.ToString("yyyy-MM-dd"),
                    InsertBy = db.AgUsers.Find(c.InsertBy).FullName,
                    DocType = c.DocType,
                }).ToList();
                return Json(new { ErrorCode = 0, OldDocsRep });

            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
        public ActionResult a400052(a45cl MainObj)
        {

            try
            {
                var User = db.AgUsers.Find(MainObj.InsertBy);
                if (User == null || User.AgentId != MainObj.AgEncyID)
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                var sn = db.SerialNums.Where(p => p.SerialNumID == MainObj.pap_no && p.SearStatus.ID == 1 && p.AgentID == MainObj.AgEncyID).ToList();
                if (!sn.Any())
                    return Json(new { ErrorCode = 4 });
                var Max_date = db.CompulsoryInsurenceTB.Max(c => c.InsertDate);
                if (Max_date.Date > DateTime.Now.Date)
                {
                    return Json(new { ErrorCode = 5, ErorrMessage = "خطأ في التاريخ" });
                }
                if ( db.CompulsoryInsurenceTB.Where(c=>c.Ins_SN == MainObj.OldDocNum && c.Status == 1).Any())
                    return Json(new { ErrorCode = 0, ErorrMessage = "هذه الوثيقة ضمن ثائق المنظومة الجديدة يرجى إتباع خطوات تغير المكلية في المنظومة الجديدة" });
                var Agen = db.Agency.Find(MainObj.AgEncyID);
                float Minp = Agen.IsAllowedDibt == false ? 0 : ((float)Agen.AllowedDibtValue * -1);
                CompulsoryInsurenceTB obj = new CompulsoryInsurenceTB();
                obj.Insu_ID = Guid.NewGuid();
                obj.AgencyID = (Guid)MainObj.AgEncyID;
                obj.DocType = 4;
                obj.InsTypePerDurID = 3;
                obj.CompMasterID = (Guid)MainObj.c_masterID;
                obj.Manfactire_ContryID = (Guid)MainObj.con_ID;
                obj.CompDetalisID = (Guid)MainObj.c_DetailsID;
                obj.CarBarndID = (Guid)MainObj.car_ID;
                obj.CompID = Guid.Parse("F42F6690-4E98-4F07-B700-74EA1487A4F7");
                obj.CusName = MainObj.c_Name ;
                obj.Address = MainObj.c_Address;
                obj.PhoneNum = MainObj.c_phone;
                obj.ColorID = (Guid)MainObj.car_color;
                obj.Chassis_number = MainObj.chass_num;
                obj.Engine_number = MainObj.Eng_num;
                obj.NomOfPassengers = MainObj.NoOfPassen;
                obj.Engine_Capacity = MainObj.eng_cap;
                obj.PayLoad = MainObj.c_load;
                obj.WorckPlaceInsuID = MainObj.city_ID;
                obj.Manufacturing_Year = MainObj.m_year;
                obj.DateFrom = DateTime.Now.ToUniversalTime().AddHours(2).Date.AddDays(1);
                obj.InsValue = 0;
                obj.Pass_price = 0;
                obj.PfroftMargin = 0;
                obj.Tax = 0;
                obj.Tax_insu =2 ;
                obj.Tax_stamp = 0.5;
                obj.Tax_supervision = 0;
                obj.TaxTotal = 2.5;
                obj.TotalWithTax = 2.5;
                Agen.SoldValue = (Agen.SoldValue ?? 0) + (obj.TotalWithTax);  
                float pal =   ((float?) Agen.AddPalance ?? 0) - ((float?)Agen.SoldValue ?? 0);
                if (pal <= Minp)
                {
                    return Json(new { ErrorCode = 5, ErorrMessage = "رصيدك غير كافي لإجراء هذه المعاملة ،قم بمخاطبة مرؤسيك بالخصوص" });
                }
                var tt = db.DuratuionTB.Find(MainObj.Ins_Type);
                obj.Ins_SN = "";
                obj.Durations = (int)(MainObj.E_date - MainObj.S_Date).TotalDays;
                obj.Paper_No = MainObj.pap_no;
                obj.IronBoard_num = MainObj.Ib_num;
                obj.DurationType = tt.Dur_type;
                obj.DateTo = MainObj.E_date;
                obj.Status = 1;
                obj.IsActivete = true;
                obj.InsertBy = MainObj.InsertBy;
                obj.InsertDate = DateTime.Now.ToUniversalTime().AddHours(2);
                db.CompulsoryInsurenceTB.Add(obj);
                db.SaveChanges();
                obj.Ins_SN = DateTime.Now.ToUniversalTime().AddHours(2).Date.Year.ToString() + " " + obj.Paper_No + " 601 " + Agen.AgNum.ToString() + " " + obj.Ins_num;
                db.CompulsoryInsurenceTB.Attach(obj);
                db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                db.SaveChanges();
                Agen.Palance = pal;
                db.Agency.Attach(Agen);
                db.Entry(Agen).State = EntityState.Modified;
                db.SaveChanges();
                oldOwner oo = new oldOwner();
                oo.id = Guid.NewGuid();
                oo.newDocId = obj.Insu_ID;
                oo.oldDocNum = MainObj.OldDocNum;
                db.oldOwner.Add(oo);
                db.SaveChanges();
                var sns = db.SerialNums.Find(obj.Paper_No);
                sns.Status = 12;
                db.SerialNums.Attach(sns);
                db.Entry(sns).State = System.Data.Entity.EntityState.Modified;
                db.SaveChanges();
                Doc_Print doccl = new Doc_Print();
                
                var doc = doccl.printOldDoc(obj);
                return Json(new { ErrorCode = 0, doc });
            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني ", WW = ex.Message });
            }
        }

        public ActionResult a400051(RePlaceCl RePlaceObj)
        {

            try
            {
                var User = db.AgUsers.Find(RePlaceObj.InsertBy);
                if (User == null || User.AgentId != RePlaceObj.AgEncyID)
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                var sn = db.SerialNums.Where(p => p.SerialNumID == RePlaceObj.DocNoTXT && p.SearStatus.ID == 1 && p.AgentID == RePlaceObj.AgEncyID).ToList();
                if (!sn.Any())
                    return Json(new { ErrorCode = 4 });

                var Agen = db.Agency.Find(RePlaceObj.AgEncyID);
                float Minp = Agen.IsAllowedDibt == false ? 0 : ((float)Agen.AllowedDibtValue * -1);
                var OldDoc = db.CompulsoryInsurenceTB.Find(RePlaceObj.OldID);
                db.Entry(OldDoc).State = EntityState.Detached;
                if (OldDoc.DateTo.Date < DateTime.Now)
                    return Json(new { ErrorCode = 5, ErorrMessage = "صلاحية الوثيقة منتهية" });
                CompulsoryInsurenceTB obj = OldDoc;
                obj.Insu_ID = Guid.NewGuid();
                obj.DocType = 3;
                obj.Tax = 0;
                obj.Tax_supervision = 0;
                obj.Tax_insu =2; 
                obj.Tax_stamp = 0.5;
                obj.InsValue = 0;
                obj.TaxTotal = 2.5;
                obj.TotalWithTax = 2.5;
                obj.Paper_No = RePlaceObj.DocNoTXT;
                obj.Ins_SN = DateTime.Now.ToUniversalTime().AddHours(2).Date.Year.ToString() + " " + obj.Paper_No + " 601 " + Agen.AgNum.ToString() + " "
                    + obj.Ins_num + "-" + RePlaceObj.DocNoTXT.ToString();
                obj.Pass_price = 0;
                obj.PfroftMargin = 0;
                Agen.SoldValue = Agen.SoldValue = (Agen.SoldValue ?? 0) + (obj.TotalWithTax);
                float pal = ((float?)Agen.AddPalance ?? 0) - ((float?)Agen.SoldValue ?? 0);
                if (pal <= Minp)
                {
                    return Json(new { ErrorCode = 5, ErorrMessage = "رصيدك غير كافي لإجراء هذه المعاملة ،قم بمخاطبة مرؤسيك بالخصوص" });
                }
                obj.CusName = RePlaceObj.c_Name;
                obj.WorckPlaceInsuID = RePlaceObj.city_ID;
                obj.Address = RePlaceObj.c_address;
                obj.PhoneNum = RePlaceObj.c_phone;
                obj.IronBoard_num = RePlaceObj.Ib_num;
                obj.AgencyID = (Guid)RePlaceObj.AgEncyID;
                obj.InsertBy = RePlaceObj.InsertBy;
                obj.InsertDate = DateTime.Now.ToUniversalTime().AddHours(2);
                db.CompulsoryInsurenceTB.Add(obj);
                db.SaveChanges();
                OldDoc.IsActivete = false;
                db.CompulsoryInsurenceTB.Attach(OldDoc);
                db.Entry(OldDoc).State = System.Data.Entity.EntityState.Modified;
                db.SaveChanges();
                Agen.Palance = pal;
                db.Agency.Attach(Agen);
                db.Entry(Agen).State = EntityState.Modified;
                db.SaveChanges();
                var sns = db.SerialNums.Find(obj.Paper_No);
                sns.Status = 8;
                db.SerialNums.Attach(sns);
                db.Entry(sns).State = System.Data.Entity.EntityState.Modified;
                db.SaveChanges();
                OtherServHis otherServHis = new OtherServHis();
                otherServHis.ID = Guid.NewGuid();
                otherServHis.NewDocID = obj.Insu_ID;
                otherServHis.OldDoc = OldDoc.Insu_ID;
                otherServHis.Type = 3;
                otherServHis.Descr = "تغير مكلية";
                db.OtherServHis.Add(otherServHis);
                db.SaveChanges();
                Doc_Print doccl = new Doc_Print();
                var doc = doccl.print(obj);
                return Json(new { ErrorCode = 0, doc });

            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني ", WW = ex.Message });
            }
        }


        public ActionResult a400050(Guid ID,Guid UserID)
        {
            try
            {

                var us = db.AgUsers.Find(UserID);
                if (us == null)
                    return Json(new { ErrorCode = 3 });
                var Obj = db.CompulsoryInsurenceTB.Find(ID);
                Doc_Print doccl = new Doc_Print();
                var doc = doccl;
                if (Obj.DocType == 4)
                {
                      doc = doccl.printOldDoc(Obj);

                }
                else
                {
                    doc = doccl.print(Obj);

                }
                return Json(new { ErrorCode = 0, doc });

            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
        public ActionResult a400033(RenewCl RenewObj)
        {

            try
            {
                var User = db.AgUsers.Find(RenewObj.InsertBy);
                if (User == null || User.AgentId != RenewObj.AgEncyID )
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                var sn = db.SerialNums.Where(p => p.SerialNumID == RenewObj.DocNoTXT && p.SearStatus.ID == 1 && p.AgentID == RenewObj.AgEncyID).ToList();
                if (!sn.Any())
                    return Json(new { ErrorCode = 4 });

                var Agen = db.Agency.Find(RenewObj.AgEncyID);
                float Minp = Agen.IsAllowedDibt == false ? 0 : ((float)Agen.AllowedDibtValue * -1);
                var OldDoc = db.CompulsoryInsurenceTB.Find(RenewObj.OldID);
                db.Entry(OldDoc).State = EntityState.Detached;
                if (OldDoc.DateTo.Date < DateTime.Now )
                    return Json(new { ErrorCode = 5 , ErorrMessage = "صلاحية الوثيقة منتهية" });
                CompulsoryInsurenceTB obj = OldDoc;
                obj.Insu_ID = Guid.NewGuid();
                obj.DocType = 2;
                obj.DateFrom = DateTime.Now.Date;
                obj.Tax = 0;
                obj.Tax_supervision = 0;
                obj.Tax_insu = 2; 
                obj.Tax_stamp = 0.5;
                obj.InsValue = 0;
                obj.TaxTotal = 2.5;
                obj.TotalWithTax = 2.5;
                obj.Paper_No = RenewObj.DocNoTXT;
                obj.Ins_SN = DateTime.Now.ToUniversalTime().AddHours(2).Date.Year.ToString() + " " + obj.Paper_No + " 601 " + Agen.AgNum.ToString() + " " 
                    + obj.Ins_num + "-" + RenewObj.DocNoTXT.ToString() ;
                 obj.Pass_price = 0;
                 obj.PfroftMargin = 0;
                Agen.SoldValue = (Agen.SoldValue ?? 0) + (obj.TotalWithTax);
                float pal = ((float?)Agen.AddPalance ?? 0) - ((float?)Agen.SoldValue ?? 0);
                if (pal <= Minp)
                {
                    return Json(new { ErrorCode = 5, ErorrMessage = "رصيدك غير كافي لإجراء هذه المعاملة ،قم بمخاطبة مرؤسيك بالخصوص" });
                }
                obj.AgencyID =(Guid) RenewObj.AgEncyID;
                obj.InsertBy = RenewObj.InsertBy;
                obj.InsertDate = DateTime.Now.ToUniversalTime().AddHours(2);
                db.CompulsoryInsurenceTB.Add(obj);
                db.SaveChanges();
                OldDoc.IsActivete = false;
                db.CompulsoryInsurenceTB.Attach(OldDoc);
                db.Entry(OldDoc).State = System.Data.Entity.EntityState.Modified;
                db.SaveChanges();
                Agen.Palance = pal;
                db.Agency.Attach(Agen);
                db.Entry(Agen).State = EntityState.Modified;
                db.SaveChanges();
                var sns = db.SerialNums.Find(obj.Paper_No);
                sns.Status = 8;
                db.SerialNums.Attach(sns);
                db.Entry(sns).State = System.Data.Entity.EntityState.Modified;
                db.SaveChanges();
                OtherServHis otherServHis = new OtherServHis();
                otherServHis.ID = Guid.NewGuid();
                otherServHis.NewDocID = obj.Insu_ID;
                otherServHis.OldDoc =  OldDoc.Insu_ID;
                otherServHis.Type = 2 ;
                otherServHis.Descr = "بدل فاقد";
                db.OtherServHis.Add(otherServHis);  
                db.SaveChanges();
                Doc_Print doccl = new Doc_Print();
                var doc = doccl.print(obj);
                return Json(new { ErrorCode = 0, doc });

            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني ", WW = ex.Message });
            }
        }
        public ActionResult a400017(Guid UserID)
        {
            try
            {
             
                var us = db.AgUsers.Find(UserID);
                if (us == null)
                    return Json(new { ErrorCode = 3 });

                var CompObj1 = db.Comp_Agency.Where(c => c.Status == 1 
                && c.AgencyID == us.AgentId).Select(c => new {
                    ID = new {Key = c.CompID , c.Cus_Companies.IsInsu , c.Cus_Companies.IsTax , c.Cus_Companies.IsSupfees, c.Cus_Companies.IsStamp , VaerValue = c.Cus_Companies.SupfeesValue},
                    Name = c.Cus_Companies.CompName,
                    c.InsertDate,
                    CanModfyName =c.Cus_Companies.CanModfyName?? false,
                   
                }).OrderBy(c=> c.InsertDate).ToList();
                var CompObj = CompObj1.Select(c => new { 
                  c.ID,
                  c.Name,
                   IsDisabed = !c.CanModfyName
                }).ToList();
                return Json(new { ErrorCode = 0, CompObj });
                 
            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
        public ActionResult a400015(int Ins_Type, Guid UserID)
        {
            try
            {

                var User = db.AgUsers.Find(UserID);
                if (User == null)
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                var DurTypes1 = db.DuratuionTB.Where(c => c.Status == 1
                && c.DurationCatID == Ins_Type).ToList();
                var DurTypes = DurTypes1.Select(c => new
                {
                     ID = new
                     {
                         c.ID,
                         DateTo = c.Dur_type == "y" ?  DateTime.Now.ToUniversalTime().AddHours(2).AddDays(1).AddYears((int)c.Duartain ).ToString("yyyy-MM-ddTHH:mm:ss") :
                      c.Dur_type == "m" ?  DateTime.Now.ToUniversalTime().AddHours(2).AddDays(1).AddMonths((int)c.Duartain).ToString("yyyy-MM-ddTHH:mm:ss") :
                      c.Dur_type == "d" ?  DateTime.Now.ToUniversalTime().AddHours(2).AddDays(1).AddDays((int)c.Duartain).ToString("yyyy-MM-ddTHH:mm:ss") : "",
                         Val = (c.IsPersentege ?? false) ?  c.Value / 100 : c.Value ,
                         Dur = c.Duartain ?? 0, 
                         DateFrom =  DateTime.Now.ToUniversalTime().AddHours(2).AddDays(1).ToString("yyyy-MM-ddTHH:mm:ss"),

                     },
                     Name = c.DurationDesc
                }).ToList();
                return Json(new { ErrorCode = 0, DurTypes });

            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
        public ActionResult a45006(Guid ID, Guid UserID ,string DelNote)
        {
            try
            {
                //Insur_class ic = new Insur_class();
                //if (!ic.HasPer(UserID.ToString()))
                //{
                //    return Json(new { ErrorCode = 3 });
                //}
                var User = db.AgUsers.Find(UserID);
                if (User == null)
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                CompulsoryInsurenceTB obj = db.CompulsoryInsurenceTB.Find(ID);
                obj.IsInDeleteProgress = true;
                obj.UpdateDate =  DateTime.Now.ToUniversalTime().AddHours(2);
                obj.UpdateBy = UserID;
                db.CompulsoryInsurenceTB.Attach(obj);
                db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                db.SaveChanges();
                NotesTB nObj = new NotesTB();
                nObj.ID = Guid.NewGuid();
                nObj.DocID = ID;
                nObj.Status = 1;
                nObj.InsertBy= UserID;
                nObj.InsertDate= DateTime.Now;
                nObj.SenderType = 1;
                db.NotesTB.Add(nObj);
                db.SaveChanges();

                return Json(new { ErrorCode = 0, });

            }
            catch (Exception)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
      
        public ActionResult a40007(a45cl MainObj)
        {

            try
            {
                var User = db.AgUsers.Find(MainObj.InsertBy);
                if (User == null || User.AgentId != MainObj.AgEncyID || MainObj.car_ID == null )
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                //var sn = db.SerialNums.Where(p => p.SerialNumID == MainObj.pap_no && p.SearStatus.ID == 1 && p.AgentID == MainObj.AgEncyID).ToList();
                //if (!sn.Any())
                //    return Json(new { ErrorCode = 4 });
                var comp = db.Cus_Companies.Find(MainObj.Com_ID);
                var Agen = db.Agency.Find(MainObj.AgEncyID);
                float Minp = Agen.IsAllowedDibt == false ? 0 : ((float)Agen.AllowedDibtValue * -1);
                CompulsoryInsurenceTB obj = new CompulsoryInsurenceTB();
                obj.Insu_ID = Guid.NewGuid();
                obj.AgencyID = (Guid)MainObj.AgEncyID;
                obj.DocType = 1;
                obj.CompMasterID = (Guid)MainObj.c_masterID;
                obj.Manfactire_ContryID = (Guid)MainObj.con_ID;
                obj.CompDetalisID = (Guid)MainObj.c_DetailsID;
                obj.CarBarndID = (Guid) MainObj.car_ID;
                obj.CompID = (Guid)MainObj.Com_ID;
                obj.CusName = (comp.CanModfyName ?? false) ?  MainObj.c_Name : comp.CompName ;
                obj.Address = MainObj.c_Address;
                obj.PhoneNum = MainObj.c_phone;
                obj.ColorID = (Guid)MainObj.car_color;
                obj.Chassis_number = MainObj.chass_num;
                obj.Engine_number = MainObj.Eng_num;
                obj.NomOfPassengers = MainObj.NoOfPassen;
                obj.Engine_Capacity = MainObj.eng_cap;
                obj.PayLoad = MainObj.c_load;
                obj.WorckPlaceInsuID = MainObj.city_ID;
                obj.Manufacturing_Year = MainObj.m_year;
                obj.DateFrom =  DateTime.Now.ToUniversalTime().AddHours(2).AddDays(1);
                obj.InsTypePerDurID = MainObj.Ins_Type;
                var dt = db.DuratuionTB.Find(obj.InsTypePerDurID);
                var cpd= db.CompulsoryPriceDetails.Find(obj.CompDetalisID);
                var dtVal = (dt.IsPersentege ?? false) ? ((dt.Value ?? 0) / 100) : dt.Value ?? 0; 
                obj.InsValue = ((cpd.PricePerMonth ?? 0) * dtVal) + ((cpd.PassPrice ?? 0) * obj.NomOfPassengers ) ;
                obj.Pass_price = cpd.PassPrice ??0;
                obj.PfroftMargin = ((float)obj.InsValue) * ((float)Agen.ProftMargin / 100);
                obj.Tax = comp.IsTax ? ic.RoudToPoint5((float)(0.01 *((float?) obj.InsValue ?? 0))):0;
                obj.Tax_insu = db.Cus_Companies.Find(MainObj.Com_ID).SupfeesValue ?? 0;
                obj.Tax_stamp = comp.IsStamp ? 0.5 : 0;
                obj.Tax_supervision =comp.IsSupfees ?( (float)(0.005 *((float)obj.InsValue)) ): 0;
                obj.TaxTotal = obj.Tax +  obj.Tax_stamp + obj.Tax_supervision + obj.Tax_insu;
                obj.TotalWithTax = obj.InsValue +obj.TaxTotal;
                var to = obj.TotalWithTax ;
                obj.TotalWithTax = ic.RoudTo1((float)to);
                float def = comp.IsInsu ? (((float)obj.TotalWithTax )- ((float)to)):0;
                obj.Tax_insu = obj.Tax_insu + def;
                obj.TaxTotal = (float) obj.TaxTotal + def;
                Agen.SoldValue = (Agen.SoldValue ?? 0) - (Agen.AgenCatID.ToString() == "DAC8AE95-9111-495E-911E-1DADB1AA3DE2" ? (obj.TotalWithTax )
                : Agen.AgenCatID.ToString() == "F74FCA70-2881-4385-864B-063D451708B9" ? (obj.TotalWithTax) - (obj.PfroftMargin ) : obj.InsValue);
                Agen.Palance = ic.RondedValue((float)((Agen.AddPalance ?? 0) - (Agen.SoldValue ?? 0)));
                float pal = ((float?)Agen.Palance ?? 0) - ((float) obj.TotalWithTax );
                if (pal <= Minp)
                {
                    return Json(new { ErrorCode = 5, ErorrMessage = "رصيدك غير كافي لإجراء هذه المعاملة ،قم بمخاطبة مرؤسيك بالخصوص" });
                }
                var tt = db.DuratuionTB.Find(MainObj.Ins_Type);
                obj.Ins_SN = "";
                obj.Durations = MainObj.Ins_duration;
                obj.Paper_No = MainObj.pap_no;
                obj.IronBoard_num = MainObj.Ib_num;
                obj.DurationType = tt.Dur_type;
                obj.DateTo =  tt.Dur_type == "y" ? obj.DateFrom.Date.AddYears(tt.Duartain ?? 0):
                              tt.Dur_type == "m" ?  obj.DateFrom.Date.AddMonths(tt.Duartain ?? 0):
                              tt.Dur_type == "d" ?  obj.DateFrom.Date.AddDays(tt.Duartain ?? 0)
                              :new DateTime(1960,1,1);
                obj.Status = 1;
                obj.IsActivete = true;
                obj.InsertBy = MainObj.InsertBy;
                obj.InsertDate =  DateTime.Now.ToUniversalTime().AddHours(2);
                db.CompulsoryInsurenceTB.Add(obj);
                db.SaveChanges();
                obj.Ins_SN =  DateTime.Now.ToUniversalTime().AddHours(2).Date.Year.ToString() + " " +   obj.Paper_No +  " 601 "  + Agen.AgNum.ToString() + " " + obj.Ins_num;
                db.CompulsoryInsurenceTB.Attach(obj);
                db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                db.SaveChanges();
                var sns = db.SerialNums.Find(obj.Paper_No);
                sns.Status = 8;
                db.SerialNums.Attach(sns);
                db.Entry(sns).State = System.Data.Entity.EntityState.Modified;
                db.SaveChanges();
                Doc_Print doccl = new Doc_Print();
                var doc =doccl.print(obj);
                return Json(new { ErrorCode = 0  , doc });

            }
            catch (Exception  ex)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " ,WW=ex.Message});
            }
        }
        public ActionResult a40005(Guid c_masterID, Guid UserID)
        {
            try
            {
              
                var User = db.AgUsers.Find(UserID);
                if (User == null )
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                var obj = db.CompulsoryPriceDetails.Where(c => c.Status == 1
                && c.CompMasterID == c_masterID).Select(c => new
                {
                   ID =  new { ID =c.CompDelatilsID, Price = c.PricePerMonth ,
                       MinNoPass = c.NumberOfPassengersFrom , MaxNoPass = c.NumberOfPassengersTo
                     ,MinCap_eng = c.PowerOfEngineFrom , MaxCap_eng = c.PowerOfEngineTo 
                     ,C_load = c.CarLoader ?? 0 ,Pass_count = c.CarPass ?? 0 ,Pass_price = c.PassPrice ??0},
                   Name = c.CompDetalisName,
                }).OrderBy(c=> c.Name).ToList();
                return Json(new { ErrorCode = 0, obj });

            }
            catch (Exception )
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
        public ActionResult a400031( string SearchTxt,  Guid UserID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                var User = db.AgUsers.Find(UserID);
                if (User == null)
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                var Obj = db.CompulsoryInsurenceTB.Where(p => p.Status == 1
                && p.IsInDeleteProgress == false &&
                (p.IronBoard_num == SearchTxt || p.Chassis_number == SearchTxt || p.Ins_num.ToString() == SearchTxt
                || p.Ins_SN.ToString() == SearchTxt || p.Paper_No.ToString() == SearchTxt 
                || SearchTxt.Contains(p.CusName))).Select(c => new
                {
                    c.DocType,
                    c.DocTypes.DocTypeDesc,
                    c.Engine_Capacity,
                    c.CarsBrand.CarNaame,
                    c.Colors.ColorNaame,
                    c.Insu_ID,
                    c.CompID,
                    c.Cus_Companies.CompName,
                    c.CusName,
                    c.ColorID,
                    c.InsValue,
                    c.Ins_SN,
                    c.IronBoard_num,
                    c.IsInDeleteProgress,
                    c.TaxTotal,
                    c.TotalWithTax,
                    c.InsertBy,
                    c.InsertDate,
                    IsValid =c.IsActivete,
                    c.DateTo,
                 }).OrderByDescending(c => c.InsertDate).ToList();
                var SerDocTb = Obj.Select(c => new
                {
                    Ins_SN = c.Ins_SN,
                    DocType_ID = c.DocType,
                    TypeDesc =c.DocTypeDesc,
                    Com_Name = c.CompName,
                    c_name=c.CusName,
                    c.IronBoard_num,
                    ins_ID = c.Insu_ID,
                    IsDelProg = c.IsInDeleteProgress,
                    color = c.ColorNaame,
                    iron_bord = c.IronBoard_num,
                    car_name = c.CarNaame,
                    ins_val = c.InsValue,
                    TaxVal = c.TaxTotal,
                    total_tax = c.TotalWithTax,
                    CanBeRenew = c.IsValid,
                    CanBeExtended = c.IsValid && c.DocType==1,
                    CanBeChangeOwner = c.DateTo >= DateTime.Now.Date && c.DocType == 1,
                    InsertBy = db.AgUsers.Find(c.InsertBy).AgUserName,
                    InsertDate = c.InsertDate.ToString("yyyy-MM-ddTHH:mm:ss")
                }).ToList();
                    

                return Json(new { ErrorCode = 0, SerDocTb });

            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }   
    
        public ActionResult a40004(Guid UserID, Guid AgencyID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                CompInsuTypes CompLi = new CompInsuTypes();
                var User = db.AgUsers.Find(UserID);
                if (User == null || User.AgentId != AgencyID)
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                var ColObj = db.Colors.Where(c => c.Status == 1).Select(c => new {
                    ID = c.ColorID,
                    Name = c.ColorNaame,
                }).OrderBy(c => c.Name).ToList();
                var CarsObj = db.CarsBrand.Where(c => c.Status == 1).Select(c => new {
                    ID = c.CarID,
                    Name = c.CarNaame,
                }).OrderBy(c => c.Name).ToList();
                List<int> Years = new List<int>();
                for (int i = DateTime.Now.ToUniversalTime().AddHours(2).Year - 90; i <= DateTime.Now.ToUniversalTime().AddHours(2).Year + 1; i++)
                    Years.Add(i);
             
 
                var MasterObj = db.CompulsoryPriceMaster.Where(c => c.Status == 1).Select(c => new
                {
                    ID = c.CompMasterID,
                    Name = c.CompMasterName,
                    c.BandNumber,
                }).OrderBy(c => c.BandNumber).ToList();
                var ct_li = db.DurationsCat.Where(c => c.Status == 1).Select(c => new {
                    c.ID,
                    Name = c.Insu_Desc
                }).ToList();
                var contries = db.Country.Where(c => c.Status == 1).Select(c => new {
                    ID = c.CountryID,
                    Name = c.CountryName,
                }).ToList();
                Guid li_id = ic.GetLibyaID();
                var Linc_Cities = db.Cities.Where(c => c.Status == 1 && c.CountryID == li_id).Select(c => new {
                    ID = new { c.CityID, c.CityName },
                    Name = c.CityName,
                }).ToList();
                //var MangSet = new
                //{
                //  InsValue =  db.OtherSettings.Find(1).Val
                //};
                return Json(new { ErrorCode = 0, ColObj, CarsObj, Years, MasterObj, ct_li, contries, Linc_Cities  });

            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
    }
}