//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Compulsory_insuApp
{
    using System;
    using System.Collections.Generic;
    
    public partial class AgencyEquipment
    {
        public System.Guid ID { get; set; }
        public System.Guid AgencyID { get; set; }
        public int CategoryID { get; set; }
        public string ItemName { get; set; }
        public string Brand { get; set; }
        public string Model { get; set; }
        public string SerialNumber { get; set; }
        public int Quantity { get; set; }
        public byte Status { get; set; }
        public string Notes { get; set; }
        public Nullable<System.Guid> InsertedBy { get; set; }
        public Nullable<System.DateTime> InsertedDate { get; set; }
        public Nullable<System.Guid> UpdatedBy { get; set; }
        public Nullable<System.DateTime> UpdatedDate { get; set; }
        public Nullable<double> Price { get; set; }
        public Nullable<byte> CustodyStatus { get; set; }
        public string CustodyNotes { get; set; }
    
        public virtual Agency Agency { get; set; }
        public virtual EquipmentCategories EquipmentCategories { get; set; }
    }
}
