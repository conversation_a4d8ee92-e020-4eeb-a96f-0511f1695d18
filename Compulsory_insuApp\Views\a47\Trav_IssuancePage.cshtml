<link href="~/Content/CSS/bootstrap.rtl.min.css" rel="stylesheet" />

@*<link href="~/Content/InsRPCSS.css?v=1000" media="print" rel="stylesheet" type="text/css" />*@
<link href="~/Content/AgencyPageSt.css" rel="stylesheet" />
<link href="~/Content/InsRPCSS.css" media="print" type="text/css" rel="stylesheet" />
<style>
/* حجم A4 بالبيكسل تقريبي (72dpi): 794px × 1123px */
.a4-print-mode.row.d-print-block.bg-white[page-size="A4"] {
  width: 794px !important;
  min-height: 1123px !important;
  max-width: 794px !important;
  max-height: 1123px !important;
/*  margin: 0 auto !important;*/
  box-sizing: border-box;
  background: #fff !important;
  padding: 0 !important;
  overflow: hidden !important;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center; /* محاذاة عمودية في المنتصف */
  align-items: center;
}
.a4-print-mode .container.bordered {
  width: 750px !important;
  min-height: auto !important;
  margin: 0 auto !important;
  background: #fff !important;
  padding: 0 !important;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.a4-print-mode .table {
  font-size: 11px !important;
  margin-bottom: 0 !important;
  width: 100% !important;
  table-layout: fixed !important;
  word-break: break-word !important;
}
.a4-print-mode .row, 
.a4-print-mode .col-6, 
.a4-print-mode .col-md-6, 
.a4-print-mode .col-xl-6, 
.a4-print-mode .col-12 {
  margin: 0 !important;
  padding: 0 !important;
  box-sizing: border-box !important;
  max-width: 100% !important;
}
.a4-print-mode .d-print-none, .a4-print-mode .d-print-none * {
  display: none !important;
}
.a4-print-mode {
  font-size: 12px !important;
  line-height: 1.2 !important;
}
</style>
<script>
// إضافة كلاس الطباعة عند بدء الطباعة وإزالته بعد الانتهاء
(function() {
  var printSection = document.querySelector('.row.d-print-block.bg-white[page-size="A4"]');
  if (!printSection) return;
  function addPrintMode() {
    printSection.classList.add('a4-print-mode');
  }
  function removePrintMode() {
    printSection.classList.remove('a4-print-mode');
  }
  if (window.matchMedia) {
    window.matchMedia('print').addListener(function(mql) {
      if (mql.matches) { addPrintMode(); }
      else { removePrintMode(); }
    });
  }
  window.onbeforeprint = addPrintMode;
  window.onafterprint = removePrintMode;
})();
</script>
<div class="row justify-content-center  d-print-none " ng-show="ctrl.ShowFlag ==0">

    <div class="card mt-4 ">
        <div class="card-header">
            <div class="row g-2">



                <div class="btn-toolbar col-8" style="border-radius:15px"
                     role="toolbar" aria-label="Toolbar with button groups">
                    <div class="btn-group cairo" dir="ltr" role="group">
                        <button type="button" disabled class="btn btn-primary btn-success border-ridues  cairo "> واجهة إصدار وثيقة تامين المسافرين</button>
                        <button type="button" ng-click="ctrl.BtnHomePage()" class="btn btn-success bi bi-house-fill"> <span class="m-2">الرئيسية</span></button>
                    </div>


                </div>



                <div class="btn-toolbar  col-4 " role="toolbar"
                     aria-label="Toolbar with button groups">
                    <div class="mt-1">
                        <button class="btn btn-secondary m-1  " data-bs-toggle="modal" data-backdrop="static" data-keyboard="false" data-bs-target="#CalDoc" @*ng-if="ctrl.AgPerObj.Trav_Calculation"*@ ng-click="ctrl.CalcuLateDoc()">     إحتساب وثيقة  </button>

                    </div>
                    <div class=" btn-group   col-auto ">
                        <button class="btn btn-primary m-1  " data-bs-toggle="modal" data-bs-target="#NewItem" data-backdrop="static" data-keyboard="false" ng-click="ctrl.AddNew(1)">     اصدار وثيقة جديدة  </button>



                    </div>
                </div>
            </div>
        </div>

        <div class="card-body">
            <div class="row">
                <div class="col-xs-12 col-sm-12 col-md-3   col-lg-3  col-xl-3 col-xxl-3  mt-2">
                    <div class="hstack gap-3">
                        <input ng-model="search.Any" class="form-control me-auto" type="text" placeholder="بحث  ...">
                    </div>
                </div>
                <div class="col-xs-12 col-sm-12 col-md-4   col-lg-4 col-xl-4 mt-2 float-end  ">
                    <button class="btn btn-secondary  " ng-show="ctrl.AdSearchPer==true" ng-click="ctrl.FullSearch()">بحث متقدم  <span class="bi bi-reply"></span>  </button>
                </div>
                <div class="col-xs-12 col-sm-12 col-md-2  col-lg-2  col-xl-2 col-xxl-2   mt-2 float-end">
                </div>
                <div class="col-xs-12 col-sm-12 col-md-3  col-lg-3  col-xl-3 col-xxl-3   mt-2 float-end">
                    <div class="row">
                        <div class="col-lg-6 col-md-6 col-xl-6 col-xxl-6">
                            <button class="btn btn-danger btn-lg col-12 " ng-show="ctrl.AdSearchPer==true && ctrl.SearchFlag" ng-click="ctrl.CloseSreach()">  <span class="bi bi-arrow-repeat"></span>  إعادة تحميل</button>
                        </div>
                        <div class="col-lg-6 col-md-6 col-xl-6 col-xxl-6" ng-if="ctrl.AgPerObj.Trav_ShowAllDocs">
                            <select class="form-select" ng-model="search.users">
                                <option value="">كل المستخدمين</option>
                                <option ng-repeat="x in ctrl.Users " value="{{x.ID}}">{{x.Name}}</option>

                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="table-responsive mt-4">
                <table class="table table-dark table-hover">

                    <tr>
                        <th class="text-center">#</th>
                        <th class="text-center">ر.الوثيقة</th>
                        <th class="text-center"> اسم المؤمن له </th>
                        <th class="text-center ">النطاق</th>
                        <th class="text-center">من</th>
                        <th class="text-center">المدة</th>
                        <th class="text-center">العمر</th>
                        <th class="text-center">الإجمالي</th>
                        <th class="text-center">وقت الإنشاء</th>
                        <th class="text-center">الحالة </th>
                        <th class="text-center" colspan="3">العمليات</th>
                    </tr>
                    <tr ng-repeat="x in ctrl.InsDoc|filter:search.Any |filter:{AgUserName:search.users ||undefined}:true"
                        ng-class="x.IsInDeleteProgress  ? 'bg-warnin':'' "
                        ng-style="x.IsInDeleteProgress ? {'color':'red'}:{'color':'black'}">
                        <td class="text-center text-white">{{$index + 1}}</td>
                        <td class="text-center text-white">{{x.DocNum}}</td>
                        <td class="text-center text-white">{{x.CuName}}</td>
                        <td class="text-center text-white">{{x.ZoonName}}</td>
                        <td class="text-center text-white">{{x.SDate}}</td>
                        <td class="text-center text-white">{{x.Months}}</td>
                        <td class="text-center text-white">{{x.Age}}</td>
                        <td class="text-center text-white">{{x.Total|number:'2'}}</td>
                        <td class="text-center text-white">{{x.InsertDate}}</td>

                        <td class="text-center text-white"
                            ng-style="x.IsInDeleteProgress ? {'color':'red'}:{'color':'black'}">
                            {{x.IsInDeleteProgress ?
                            'قيد الحذف ':' -' }}
                        </td>

                        <td>
                                <button type="button" class="btn btn-secondary col-12" ng-disabled=" x.IsInDeleteProgress "
                                        ng-click="ctrl.a47008(x)">
                                    <span class="bi bi-credit-card-2-front-fill"></span>
                                </button>

                            </td>
                        <td>
                            <button type="button" class="btn btn-danger col-12" ng-disabled="x.IsInDeleteProgress"
                                    ng-click="ctrl.a47005(x)">
                                <span class="bi bi-x-circle"></span>
                            </button>

                        </td>
                        <td>
                            <button type="button" class="btn btn-success col-12" ng-click="ctrl.a47008(x)"
                                    @*ng-disabled=" x.IsInDeleteProgress*@>
                                <span class="bi bi-printer-fill"></span>
                            </button>
                        </td>

                    </tr>
                </table>

            </div>
        </div>
    </div>
</div>


<div class=" row  d-print-none  bg-white h-100 w-100 mt-3 p-4 justify-content-center" page-size="A4" ng-show="ctrl.ShowFlag == 1">
    <div class="row   bg-transparent">
        <div class="d d-print-none col-md-6 col-sm-6  col-xl-6 col-xxl-6 bg-transparent">
            <button type="button" class="btn btn-success justify-content-start col-lg-2 col-md-3 col-xxl-2 col-xl-2 m-1"
                    ng-click="ctrl.print()">
                <span class="bi bi-printer-fill"></span>
            </button>

            <button type="button" class="btn btn-primary justify-content-start col-lg-2 col-md-3 col-xxl-2 col-xl-2 m-1"
                    ng-click="ctrl.ShowFlag = 0">
                رجوع
            </button>
        </div>
    </div>
    <div class="container bordered justify-content-center text-center">
        <table class="table table-bordered " style="margin-top : 6cm !important ; font-size : 10px !important ; font-family:'Times New Roman' !important">
            <tbody>
                <tr>
                    <th>اسم المؤمن له</th>
                    <td>{{ctrl.Doc.CuName}}</td>
                    <th class="text-start">Insured's Name</th>
                </tr>
                <tr>
                    <th>رقم الجواز</th>
                    <td>{{ctrl.Doc.PassportID}}</td>
                    <th class="text-start">Passport No.</th>
                </tr>
                <tr>
                    <th>تاريخ الميلاد</th>
                    <td>{{ctrl.Doc.birthDay}}</td>
                    <th class="text-start">Date of birth</th>
                </tr>
                <tr>
                    <th>الجنس</th>
                    <td>{{ctrl.Doc.Gender}}</td>
                    <th class="text-start">Sex</th>
                </tr>
                <tr>
                    <th>رقم الهاتف</th>
                    <td>{{ctrl.Doc.Cus_Phone}}</td>
                    <th class="text-start">Phone No.</th>
                </tr>
                <tr>
                    <th>(أيام) مدة التأمين</th>
                    <td>{{ctrl.Doc.durationInDays}}  </td>
                    <th class="text-start">Period of Coverage in Days</th>
                </tr>
            </tbody>
        </table>


        <div class="d-flex justify-content-center mt-4 mb-3">
            <div style="width: 60%; font-size: 10px !important; font-family: 'Times New Roman' !important ">
                <div class="d-flex align-items-center mb-2 fw-bold">
                    <div class="flex-grow-1 text-end">العملة د.ل</div>
                    <div class="flex-grow-1 text-end"></div>
                    <div class="flex-grow-1 text-start">Currency L.D</div>
                </div>
                <table class="table table-bordered mb-0">
                    <tbody>
                        <tr>
                            <th>صافي القسط</th>
                            <td>{{ctrl.Doc.DocVal | number:'2'}}</td>
                            <th class="text-start">Net Premium</th>
                        </tr>
                        <tr ng-repeat="x in ctrl.Tax">
                            <th>{{x.VirDesc}}</th>
                            <td>{{x.Value | number:'2'}}</td>
                            <th>{{x.VirDescEn}}</th>
                        </tr>
                      
                        <tr>
                            <th>إجمالي القسط</th>
                            <td>{{ctrl.Doc.Totoal | number:'2'}}</td>
                            <th class="text-start">Total Premium</th>
                        </tr>
                    </tbody>
                </table>

            </div>
        </div>



        <div class="border border-0 p-3 mt-4 text-center" style="width: 100%; font-size: 10px !important; font-family: 'Times New Roman' !important ">
          
            <div class="fw-bold">
                {{ctrl.Doc.zoonTXt}}
            </div>
            <div class="fw-bold" dir="ltr">
                {{ctrl.Doc.zoonTXtEn}}
            </div>
        </div>

        <div class="border border-0 p-0 mt-4" style="width: 100%; font-size: 10px !important; font-family: 'Times New Roman' !important ">
            <table class="table table-bordered mb-0" style="table-layout: fixed;">
                <tr>

                    <td class="text-end fw-bold">
                        <br>

                    </td>

                    <td></td>

                    <td class="text-start fw-bold">
                    </td>
                </tr>
            </table>
        </div>

        <div class="d-flex justify-content-between fw-bold" style="width: 100%; font-size: 10px !important; font-family: 'Times New Roman' !important ">
            <div class="text-end" style="flex: 1;" dir="rtl">

            </div>
            <div class="text-start" style="flex: 1;" dir="ltr">

            </div>
        </div>
        <div class="mt-5" style="width: 100%; margin-bottom: 3cm !important; font-size: 10px !important; font-family: 'Times New Roman' !important ">
            <div class="row d-flex justify-content-between align-items-center px-5 fw-bold mt-5">
                <div class="text-start fw-bold">
                    +216 71104540 - UK 08452171379<br>
                    Hajj KSA 8008973919
                </div>
                <div class="text-end" style="flex: 1;">
                    <div>
                        <br>

                    </div>
                </div>
            </div>
            <div class="text-start" style="flex: 1;">
                <div class="text-start fw-bold">
                    التاريخ / DATE <br />
                    {{ctrl.Doc.ClintDate}}
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-6 small-box text-start border-2" style="line-height: 1.8; font-size: 10px; font-family: 'Times New Roman' !important">
                <div><strong>Travel Insurance Policy No:</strong> {{ctrl.Doc.InsuNumber}}</div>
                <div><strong>Passport No:</strong> {{ctrl.Doc.PassportID}}</div>
                <div><strong>Insured's Name:</strong> {{ctrl.Doc.NameCust}}</div>
                <div>
                    <strong>E-mail:</strong>
                    <span style="color: #007bff;">et.aletay.medical@ip_asealistone.com</span>
                </div>
            </div>
            <div class="col-6 small-box border-2">
                <div style="text-align: center; margin: 20px 0; font-size: 10px !important; font-family: 'Times New Roman' !important ">
                    <div style="background-color: rgb(99, 31, 31); color: white; padding: 5px 10px; border-radius: 5px; display: inline-block; font-weight: bold;">

                    </div>
                </div>
                <div style="display: flex; align-items: center; gap: 10px; text-align: start; font-size: 10px !important">

                    <div style="text-align: left; direction: ltr; font-weight: bold;">

                        <span style="background-color: #f8d7da; color: #721c24; padding: 3px 8px; border: 1px solid #f5c6cb; border-radius: 4px; display: inline-block; margin-right: 8px;">

                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
   


<div class="row d-none d-print-block  bg-white " page-size = "A4" ng-show="ctrl.printflag == 1">
    <div class="row   bg-transparent">
        <div class="d d-print-none col-md-6 col-sm-6  col-xl-6 col-xxl-6 bg-transparent">
            <button type="button" class="btn btn-success float-end col-lg-2 col-md-3 col-xxl-2 col-xl-2 m-1"
                    ng-click="ctrl.print()">
                <span class="bi bi-printer-fill"></span>
            </button>

            <button type="button" class="btn btn-primary float-end col-lg-2 col-md-3 col-xxl-2 col-xl-2 m-1"
                    ng-click="ctrl.ShowFlag = 0">
                رجوع
            </button>
        </div>
    </div>
    <div class="container bordered justify-content-center text-center" @*style="margin-left : 60cm !important"*@>
        <table class="table table-bordered " style="margin-top : 6cm !important ; font-size : 10px !important ; font-family:'Times New Roman' !important">
            <tbody>
                <tr>
                    <th>اسم المؤمن له</th>
                    <td>{{ctrl.Doc.NameCust}}</td>
                    <td style="direction: ltr;">  </td>
                    <th class="text-start">Insured's Name</th>
                </tr>
                <tr>
                    <th>رقم الجواز</th>
                    <td>{{ctrl.Doc.PassportID}}</td>
                    <td style="direction: ltr;">  </td>
                    <th class="text-start">Passport No.</th>
                </tr>
                <tr>
                    <th>تاريخ الميلاد</th>
                    <td>{{ctrl.Doc.BitthDate}}</td>
                    <td style="direction: ltr;">  </td>
                    <th class="text-start">Date of birth</th>
                </tr>
                <tr>
                    <th>الجنس</th>
                    <td>{{ctrl.Doc.Gender == 1 ? 'ذكر' : 'أنثى'}}</td>
                    <td style="direction: ltr;">  </td>
                    <th class="text-start">Sex</th>
                </tr>
                <tr>
                    <th>رقم الهاتف</th>
                    <td>{{ctrl.Doc.Cus_Phone}}</td>
                    <td style="direction: ltr;">  </td>
                    <th class="text-start">Phone No.</th>
                </tr>
                <tr>
                    <th>مدة التأمين</th>
                    <td>{{ctrl.Doc.DurationMonths}}</td>
                    <td style="direction: ltr;">  </td>
                    <th class="text-start">Period of Coverage</th>
                </tr>
            </tbody>
        </table>


        <div class="d-flex justify-content-center mt-4 mb-3">
            <div style="width: 60%; font-size: 10px !important; font-family: 'Times New Roman' !important ">
                <div class="d-flex align-items-center mb-2 fw-bold">
                    <div class="flex-grow-1 text-end">العملة د.ل</div>
                    <div class="flex-grow-1 text-end"></div>
                    <div class="flex-grow-1 text-start">Currency L.D</div>
                </div>
                <table class="table table-bordered mb-0">
                    <tbody>
                        <tr>
                            <th>صافي القسط</th>
                            <td>{{ctrl.Doc.Total}}</td>
                            <th class="text-start">Net Premium</th>
                        </tr>
                        <tr ng-repeat="x in ctrl.Tax">
                            <th>{{x.VirDesc}}</th>
                            <td>{{x.Value | number:'2'}}</td>
                            <th>{{x.VirDescEn}}</th>
                        </tr>
                        <tr>
                            <th>إجمالي القسط</th>
                            <td>{{ctrl.Doc.TotWithTax}}</td>
                            <th class="text-start">Total Premium</th>
                        </tr>
                    </tbody>
                </table>

            </div>
        </div>



        <div class="border border-0 p-3 mt-4 text-center" style="width: 100%; font-size: 10px !important; font-family: 'Times New Roman' !important ">
            <div class="fw-bold mb-2">
                المنطقة (1) : جميع أنحاء العالم ماعدا ليبيا - أمريكا  - كندا - اليابان - أستراليا
            </div>
            <div class="fw-bold">
                Zone (1) : Worldwide except Libya, United States of America, Canada, Japan and Australia
            </div>
        </div>

        <div class="border border-0 p-0 mt-4" style="width: 100%; font-size: 10px !important; font-family: 'Times New Roman' !important ">
            <table class="table table-bordered mb-0" style="table-layout: fixed;">
                <tr>

                    <td class="text-end fw-bold">
                        <br>

                    </td>

                    <td></td>

                    <td class="text-start fw-bold">
                    </td>
                </tr>
            </table>
        </div>

        <div class="d-flex justify-content-between fw-bold" style="width: 100%; font-size: 10px !important; font-family: 'Times New Roman' !important ">
            <div class="text-end" style="flex: 1;" dir="rtl">

            </div>
            <div class="text-start" style="flex: 1;" dir="ltr">

            </div>
        </div>
        <div class="mt-5" style="width: 100%; margin-bottom: 3cm !important; font-size: 10px !important; font-family: 'Times New Roman' !important ">
            <div class="row d-flex justify-content-between align-items-center px-5 fw-bold mt-5">
                <div class="text-start fw-bold">
                    +216 71104540 - UK 08452171379<br>
                    Hajj KSA 8008973919
                </div>
                <div class="text-end" style="flex: 1;">
                    <div>
                        <br>

                    </div>
                </div>
            </div>
            <div class="text-start" style="flex: 1;">
                <div class="text-start fw-bold">
                    التاريخ / DATE <br />
                    {{ctrl.Doc.ClintDate}}
                </div>
            </div>
        </div>
      
        <div class="row mt-4">
            <div class="col-6 small-box text-start border-2" style="line-height: 1.8; font-size: 10px; font-family: 'Times New Roman' !important">
                <div><strong>Travel Insurance Policy No:</strong> {{ctrl.Doc.InsuNumber}}</div>
                <div><strong>Passport No:</strong> {{ctrl.Doc.PassportID}}</div>
                <div><strong>Insured's Name:</strong> {{ctrl.Doc.NameCust}}</div>
                <div>
                    <strong>E-mail:</strong>
                    <span style="color: #007bff;">et.aletay.medical@ip_asealistone.com</span>
                </div>
            </div>
            <div class="col-6 small-box border-2">
                <div style="text-align: center; margin: 20px 0; font-size: 10px !important; font-family: 'Times New Roman' !important ">
                    <div style="background-color: rgb(99, 31, 31); color: white; padding: 5px 10px; border-radius: 5px; display: inline-block; font-weight: bold;">

                    </div>
                </div>
                <div style="display: flex; align-items: center; gap: 10px; text-align: start; font-size: 10px !important">

                    <div style="text-align: left; direction: ltr; font-weight: bold;">

                        <span style="background-color: #f8d7da; color: #721c24; padding: 3px 8px; border: 1px solid #f5c6cb; border-radius: 4px; display: inline-block; margin-right: 8px;">

                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



    <div class="modal fade d-print-none md-dialog-content" id="NewItem" tabindex="-1" data-bs-backdrop="static" aria-labelledby="{{ctrl.Title}}" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ctrl.DlTitle}}</h5>


                    <button type="button" class="btn-close float-start" data-bs-dismiss="modal" aria-label="Close"></button>


                </div>
                <div class="modal-body">

                    <form name="mFrm" class="" autocomplete="on" novalidate>


                        <div class="row mt-1">

                            <div class="col-8">
                                <label for="Zid">النطاق الجغرافي <span class="text-danger">   * </span> </label>
                                <select required class="form-select" name="Zid"
                                        ng-options="x.ID as x.Name for x in ctrl.Objzn " ng-model="ctrl.Zid" ng-change="ctrl.SelectedZoonChnaged()"
                                        ng-style="mFrm.Zid.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}">
                                </select>

                            </div>

                            <div class="col-4">
                                <label for="Gender">الجنس   <span class="text-danger">   * </span> </label>
                                <select class="form-select" name="Gender" ng-required="ctrl.require==1" ng-model="ctrl.MainObj.Gender"
                                        ng-style=" mFrm.Gender.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}">
                                    <option value="1">ذكر</option>
                                    <option value="2">أنثى</option>
                                </select>
                            </div>


                        </div>

                        <div class="row mt-2">
                            <div class="col-6 ">
                                <label for="CuName">اسم المشترك       <span class="text-danger">   * </span> </label>
                                <input ng-required="ctrl.require==1" maxlength="50" minlength="2" name="CuName" type="text"
                                       ng-model="ctrl.MainObj.CuName" class="form-control " ng-style="mFrm.CuName.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}">
                                <span ng-show="mFrm.CuName.$error.minlength " class="text-danger float-end m-2">
                                    اقل عدد حروف
                                    2
                                </span>
                                <span ng-show="mFrm.CuName.$error.maxlength " class="text-danger float-end m-2">
                                    أكثر عدد
                                    حروف 20
                                </span>
                            </div>

                            <div class="col-6 ">
                                <label for="NameCustEN">اسم المشترك    <span class="text-danger"> باللغة الانجليزية مطابق لإثبات الهوية            </span>  <span class="text-danger">   * </span> </label>
                                <input ng-required="ctrl.require==1" maxlength="50" minlength="5" name="NameCustEN" id="NameCustEN" type="text"
                                       ng-model="ctrl.MainObj.NameCustEN" class="form-control " ng-style="mFrm.NameCustEN.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}" />
                                <span ng-show="mFrm.NameCustEN.$error.minlength " class="text-danger float-end m-2">
                                    اقل عدد
                                    حروف 5
                                </span>
                                <span ng-show="mFrm.NameCustEN.$error.maxlength " class="text-danger float-end m-2">
                                    أكثر عدد
                                    حروف 20
                                </span>

                            </div>

                        </div>







                        <div class="row mt-2">
                            <div class="col-4 ">
                                <label for="NatID">رقم جواز السفر  <span class="text-danger">   * </span> </label>
                                <input ng-required="ctrl.require==1" maxlength="50" minlength="5" name="NatID" type="text" lang="en"
                                       ng-model="ctrl.MainObj.PassportID" class="form-control " ng-style=" mFrm.NatID.$valid  ? {'border':'green solid 1px'}:{'border':'red solid 1px '}">
                                <span ng-show="mFrm.NatID.$error.minlength " class="text-danger float-end m-2">
                                    اقل عدد حروف
                                    5
                                </span>
                                <span ng-show="mFrm.NatID.$error.maxlength " class="text-danger float-end m-2">
                                    أكثر عدد حروف
                                    20
                                </span>
                            </div>


                            <div class="col-4">

                                <label for="BDay">سنة الميلاد  <span class="text-danger">   * </span> </label>
                                <input ng-required="ctrl.require==1" name="BDay" type="number" ng-model="ctrl.MainObj.BDay"
                                       ng-click="ctrl.MainObj.BDay " min="1950" max="@DateTime.Now.Year.ToString()"
                                       class="form-control text-center" ng-style=" mFrm.BDay.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}">

                                <span ng-show="mFrm.BDay.$error.min" class="text-danger float-end m-2">
                                    الرجاء ادخال سنة
                                    الميلاد بشكل صحيح
                                </span>
                                <span ng-show="mFrm.BDay.$error.max" class="text-danger float-end m-2">
                                    الرجاء ادخال سنة
                                    الميلاد بشكل صحيح
                                </span>


                            </div>


                            <div class="col-4">
                                <label for="Cus_Phone">رقم هاتف المؤمن  </label>
                                <input pattern="^(?:0|\(?\+33\)?\s?|0033\s?)[1-79](?:[\.\-\s]?\d\d){4}$"
                                       ng-model="ctrl.MainObj.Cus_Phone" name="Cus_Phone" class="form-control" ng-style=" mFrm.Cus_Phone.$valid ?{'border':'green solid 1px'}:{'border':'red solid 1px '}">
                                <span ng-show="mFrm.Cus_Phone.$error.pattern" class="text-danger float-end m-2">
                                    أكتب رقم
                                    الهاتف بشكل صحيح
                                </span>

                            </div>


                        </div>




                        <div class="row mt-2">

                            <div class="col-4">
                                <label for="SDate">إعتبارا من ظهر يوم <span class="text-danger">  *</span>  </label>
                                <input type="date" class="form-control" name="SDate" required disabled ng-model="ctrl.MainObj.SDate"
                                       ng-style=" mFrm.SDate.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}" />
                                <span ng-show="mFrm.SDate.$error.minDate" class="text-danger float-end mt-0">
                                    لا يمكن إدراج
                                    وثيقة بتاريخ رجعي
                                </span>
                                <span ng-show="mFrm.SDate.$dirty && !mFrm.SDate.$error.required && !mFrm.SDate.$valid && !mFrm.SDate.$error.minDate "
                                      class="text-danger float-end mt-0">أدخل التاريخ بشكل صحيح</span>

                            </div>

                            <div class="col-4">
                                <label for="Months">المدة <span class="text-danger">  *</span> </label>

                                <select required ng-model="ctrl.MainObj.Months" class="form-select text-center"
                                        name="Months" ng-style=" mFrm.Months.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}">
                                    <option value="3">
                                        <lable>أشهر</lable>
                                        <lable>3</lable>
                                    </option>
                                    <option value="6">
                                        <lable>أشهر</lable>
                                        <lable>6</lable>
                                    </option>
                                    <option value="12"> سنة</option>
                                    <option value="24"> سنتين  </option>
                                    <option value="36"> ثلاث سنوات </option>
                                </select>
                            </div>

                            <div class="col-4">
                                <label for="InsuNumber"> رقم الورقة  <span class="text-danger">  *</span></label>
                                <input required name="InsuNumber" type="number" ng-model="ctrl.MainObj.InsuNumber"
                                       ng-click="ctrl.IsExistPayperNum = 0" min="1"
                                       class="form-control text-center" ng-style=" mFrm.InsuNumber.$valid && ctrl.IsExistPayperNum == 0 ? {'border':'green solid 1px'}:{'border':'red solid 1px '}">
                                <span ng-show="mFrm.InsuNumber.$error.min" class="text-danger float-end m-2">
                                    اقل عدد يمكن
                                    إدخاله 1
                                </span>
                                <span ng-show="ctrl.IsExistPayperNum == 1" class="text-danger float-end m-2">
                                    الرقم غير
                                    صحيح
                                </span>
                            </div>

                        </div>

                    </form>

                </div>
                <div class="modal-footer justify-content-center" ng-show=" ctrl.DlFalg == 0">
                    <button type="button" class="btn btnWithet btn-success " ng-click="ctrl.a470011()"
                            ng-disabled="mFrm.$invalid  || ctrl.IsExistPayperNum == 2">
                        إصدار  و طباعة
                    </button>

                    <button type="button" class="btn btnWithet btn-secondary " data-bs-dismiss="modal">
                        إلغاء الأمر
                    </button>

                </div>
                <div class="modal-footer  justify-content-center" ng-show=" ctrl.DlFalg == 1">
                    <button type="button" class="btn  btnWithet btn-primary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4"
                            ng-click="ctrl.a00052()" ng-disabled="mFrm.$invalid || ctrl.IsExistPayperNum == 1">
                        تعديل
                    </button>
                    <button type="button" class="btn btnWithet btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4"
                            data-bs-dismiss="modal">
                        إلغاء الأمر
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade d-print-none md-dialog-content" id="Dl_Message" tabindex="-1" data-bs-backdrop="static" aria-hidden="true" aria-labelledby="{{ctrl.Title}}">
        <div class="modal-dialog ">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ctrl.DlTitle}}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-12">
                            <h5 class="m-4 text-danger text-center col-12">{{ctrl.DlMessage}}</h5>
                            <h5 class="m-4 text-danger text-center col-12 mt-2">{{ctrl.DocNum}}</h5>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-success col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4"
                            ng-click="ctrl.a00046()" data-bs-dismiss="modal">
                        نعم
                    </button>
                    <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4"
                            data-bs-dismiss="modal">
                        لا
                    </button>
                </div>

            </div>
        </div>
    </div>


    <div class="modal fade d-print-none md-dialog-content" id="CalDoc" tabindex="-1" data-bs-backdrop="static"
         aria-labelledby="{{ ctrl.Title }}" aria-hidden="true">
        <div class="modal-dialog modal-xl d-print-none">
            <div class="modal-content d-print-none">
                <div class="modal-header">
                    <h5 class="modal-title">{{ ctrl.DlTitle }}</h5>

                    <button type="button" class="btn-close float-start" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body d-print-none" ng-show="ctrl.DlFalg == 0">
                    <form name="FrmCal" class="row g-2" autocomplete="off" novalidate>
                        <div class="col-12 d-flex justify-content-center">
                            <b class="text-start" style="font-size: 25px">بيانات الجهة</b>
                        </div>
                        <div class="col-12">
                            <label for="NameWorkPlace">اسم الجهة </label>
                            <input class="form-control col-6" name="NameWorkPlace" type="text"
                                   ng-model="ctrl.CalObj.NameWorkPlace" />
                        </div>
                        <hr class="mt-2" />
                        <div class="col-12  d-flex justify-content-center">
                            <b class="text-center" style="font-size: 25px">بيانات الوثيقة</b>
                        </div>

                        <div class="col-4">
                            <label for="Zid">النطاق الجغرافي <span class="text-danger">   * </span> </label>
                            <select required class="form-select" name="Zid"
                                    ng-options="x.ID as x.Name for x in ctrl.Objzn " ng-model="ctrl.CalObj.Zid"
                                    ng-style="mFrm.Zid.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}">
                            </select>

                        </div>
                        <div class="col-4">
                            <label for="ByearDay">    السنة </label>

                            <div class="input-group mb-3">

                                <input required name="ByearDay" type="number" ng-model="ctrl.CalObj.BDay"
                                       min="1910" max="@DateTime.Now.Year.ToString()"
                                       class="form-control text-center" ng-style="FrmCal.ByearDay.$valid ? {'border':'green solid 2px'}:{'border':'red solid 2px '}">

                            </div>


                        </div>

                        <div class=" col-4">
                            <label for="BDay"> أختر المدة <span class="text-danger"> *  </span>  </label>
                            <select class="form-select" required ng-model="ctrl.CalObj.Months" name="Months">
                                <option value="3"> 3 أشهر</option>
                                <option value="6">6 اشهر</option>
                                <option value="12">   سنة</option>
                                <option value="24"> سنتين   </option>
                                <option value="36"> ثلاث سنوات </option>
                            </select>
                            <span ng-show="FrmCal.Months.$valid" class="text-success bi-check-lg float-end m-2"></span>
                        </div>
                        <div class=" col-4">

                            <div class="row-cols-12">

                                <label for="Count"> <span class="text-danger"> *  </span>العدد </label>
                                <input required class="form-control col-6 text-center" min="1" max="10000" name="Count" type="number"
                                       ng-model="ctrl.CalObj.Count" />
                                <span ng-show="FrmCal.Count.$valid" class="text-success bi-check-lg float-end m-2"></span>
                            </div>

                        </div>
                        <div class="col-12 ">

                            <button type="button" class="btn btn-primary col-12 mt-4" ng-click="ctrl.a47007()"
                                    ng-disabled="FrmCal.$invalid">
                                إحتساب
                            </button>


                        </div>
                    </form>
                    <div class="row mt-1 d-print-none ">

                        <div class="col-12 d-flex justify-content-center d-print-none">
                            <b class="text-center" style="font-size: 25px"> قائمة الاحتساب </b>
                        </div>

                        <div class="col-12 px-4 d-print-none" ng-hide="ctrl.DocTable.length == 0">
                            <table class="table table-striped table-hover">
                                <thead class="bg-light">
                                    <tr>
                                        <th class="text-center" colspan="3">الوثيقة</th>
                                        <th class="text-center" colspan="6">الضرائب</th>
                                        <th class="text-center" colspan="1">-</th>
                                    </tr>
                                    <tr>
                                        <td class="text-center">رقم</td>

                                        <td class="text-center">الفئة العمرية</td>
                                        <td class="text-center">المدة (أشهر)</td>
                                        <td class="text-center">القيمة</td>
                                        <td class="text-center">الدمغة</td>
                                        <td class="text-center">الإشراف</td>
                                        <td class="text-center">الضريبة</td>
                                        <td class="text-center">الإصدار</td>
                                        <td class="text-center">العدد</td>
                                        <td class="text-center">الإجمالي</td>
                                    </tr>
                                </thead>
                                <tbody style="text-align: center">
                                    <tr ng-repeat="x in ctrl.DocTable ">
                                        <td class="text-center">{{ $index + 1 }}</td>
                                        <td class="text-center">{{ x.AgeDesc }}</td>
                                        <td class="text-center">{{ x.Duration }}</td>
                                        <td class="text-center">{{ x.DocVal | number: "2" }}</td>
                                        <td class="text-center">{{ x.Stamp | number: "2" }}</td>
                                        <td class="text-center">{{ x.Eshraf | number: "2" }}</td>
                                        <td class="text-center">{{ x.Tax | number: "2" }}</td>
                                        <td class="text-center">{{ x.Esdar | number: "2" }}</td>
                                        <td class="text-center">{{ x.Count | number: "" }}</td>
                                        <td class="text-center">{{ x.Totoal | number: "2" }}</td>
                                    </tr>
                                    <tr class="bg-opacity-10">
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>

                                        <td></td>
                                        <td></td>
                                        <td class="text-left bold" colspan="2">المجموع</td>
                                        <td class="text-center bold">
                                            {{ ctrl.Summ.Totoal | number: "2" }}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="modal-footer justify-content-center d-print-none " ng-show="ctrl.DlFalg == 0">
                    <button type="button" class="btn btn-success col-4 float-end" ng-click="ctrl.printTempor()"
                            ng-disabled="ctrl.DocTable.length == 0 ">
                        طباعة
                    </button>
                    <button type="button" class="btn btn-secondary col-3 float-end" data-bs-dismiss="modal">
                        خروج
                    </button>
                </div>
                <div class="modal-footer d-print-none" ng-show="ctrl.DlFalg == 1">
                    <button type="button" class="btn btn-secondary col-12" data-bs-dismiss="modal">
                        موافق
                    </button>
                </div>
            </div>
        </div>
    </div>


    <div class="modal fade" id="fullshow" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">{{ctrl.massge}}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">يرجاء ادخال البيانات من اجل البحث </label>
                            <input type="text" class="form-control" ng-model="ctrl.Search">
                        </div>

                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn  btn-success" ng-click=" ctrl.a460030()">بحث </button>
                    <button type="button" class="btn btn-danger" data-bs-dismiss="modal"> اغلاق </button>

                </div>
            </div>
        </div>
    </div>



    <div class="row d-none d-print-block " ng-show="ctrl.printflag == 4">



        <div class="row   " style="margin-top:25px;">





            <div class="col-6  ">
                <img class="float-start" ng-src="~/Imges/New folder/img.png" style="height:200px; width: 200px " />
            </div>
            <div class="col-6  ">

                <div class="card float-end" style="border:none">
                    <div class="card-img text-center">
                        <img ng-src="~/Imges/img.png" style="height: 150px; width: 200px " />

                    </div>
                    <div class="card-text text-center">
                        <small>مزود الخدمات الطبية  </small>
                    </div>

                </div>


            </div>




        </div>


        <div class="col-12  justify-content-center mt-5 mx-2">


        </div>


        <div class="col-12">
            <h1 class="col-12 text-center">فاتورة مبدئية</h1>
            <h3 class="col-12 text-start mt-3 mb-3">
                السادة  : {{ ctrl.CalObj.NameWorkPlace }}
            </h3>
        </div>
        <div class="col-12   ">
            <p style="font-size:20px ;" class="text-start">
                بعد التحية.........
            </p>
        </div>
        <div class="col-12   ">
            <p style="font-size:20px ; text-align:justify;" class="text-start  ">

                بالاشارة الى طلبكم بشان تكلفة اصدار عدد " {{ctrl.Summ.Count}}" وثائق تأمين صحي للوافدين عليه فان   <br />  <b>   اجمالي القيمة  :  {{  ctrl.Summ.Totoal }} د.ل</b> هي حسب التالي :
            </p>

        </div>

        <div class="col-12 ">

            <table class="table table-responsive table-bordered">
                <thead class="bg-light">

                    <tr>
                        <td class="text-center">ر .م </td>

                        <td class="text-center">الفئة العمرية</td>


                        <td class="text-center">العدد</td>
                        <td class="text-center"> مدة التغطية </td>
                        <td class="text-center">اجمالي قسط التأمين </td>
                    </tr>
                </thead>
                <tbody style="text-align: center">
                    <tr ng-repeat="x in ctrl.DocTable ">
                        <td class="text-center">{{ $index + 1 }}</td>
                        <td class="text-center">{{ x.AgeDesc }}</td>

                        <td class="text-center">{{  x.Count | number: "" }}</td>
                        <td class="text-center">{{ x.Duration }} شهر </td>

                        <td class="text-center">{{ x.Totoal | number: "2" }}</td>
                    </tr>
                    <tr class="">



                        <td class="text-left bold" colspan="1">اجمالي العدد </td>
                        <td class="text-center"> {{ctrl.Summ.Count}}</td>
                        <td class="text-left bold" colspan="2">المجموع</td>
                        <td class="text-center bold">{{ ctrl.Summ.Totoal | number: "2" }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class=" col-12">
            <p class="text-center">  في حالة تكرمكم بالقبول ... فنامل اتمام الاجراءات المالية ليتسنى لنا اصدار وثائق التأمين بحسب ماذكر اعلاه   <br /> شاكرين لكم تواصلكم تعاونكم معنا  </p>
            <br />

            <p class="text-center"> للاستشارات والخدمات الطبية 0918204747 </p>
        </div>
        <div class=" col-12">
            <p class="text-end mx-5 fw-bold">
                التوقيع والختم
            </p>

        </div>


    </div>
