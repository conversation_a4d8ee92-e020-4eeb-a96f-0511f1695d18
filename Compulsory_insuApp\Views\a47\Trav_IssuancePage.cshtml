﻿<link href="~/Content/CSS/bootstrap.rtl.min.css" rel="stylesheet" />

@*<link href="~/Content/InsRPCSS.css?v=1000" media="print" rel="stylesheet" type="text/css" />*@
<link href="~/Content/AgencyPageSt.css" rel="stylesheet" />
<link href="~/Content/InsRPCSS.css" media="print" type="text/css" rel="stylesheet" />
<style>
/* حجم A4 بالبيكسل تقريبي (72dpi): 794px × 1123px */
.a4-print-mode.row.d-print-block.bg-white[page-size="A4"] {
  width: 794px !important;
  min-height: 1123px !important;
  max-width: 794px !important;
  max-height: 1123px !important;
/*  margin: 0 auto !important;*/
  box-sizing: border-box;
  background: #fff !important;
  padding: 0 !important;
  overflow: hidden !important;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center; /* محاذاة عمودية في المنتصف */
  align-items: center;
}
.a4-print-mode .container.bordered {
  width: 750px !important;
  min-height: auto !important;
  margin: 0 auto !important;
  background: #fff !important;
  padding: 0 !important;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.a4-print-mode .table {
  font-size: 11px !important;
  margin-bottom: 0 !important;
  width: 100% !important;
  table-layout: fixed !important;
  word-break: break-word !important;
}
.a4-print-mode .row, 
.a4-print-mode .col-6, 
.a4-print-mode .col-md-6, 
.a4-print-mode .col-xl-6, 
.a4-print-mode .col-12 {
  margin: 0 !important;
  padding: 0 !important;
  box-sizing: border-box !important;
  max-width: 100% !important;
}
.a4-print-mode .d-print-none, .a4-print-mode .d-print-none * {
  display: none !important;
}
.a4-print-mode {
  font-size: 12px !important;
  line-height: 1.2 !important;
}
</style>
<script>
// إضافة كلاس الطباعة عند بدء الطباعة وإزالته بعد الانتهاء
(function() {
  var printSection = document.querySelector('.row.d-print-block.bg-white[page-size="A4"]');
  if (!printSection) return;
  function addPrintMode() {
    printSection.classList.add('a4-print-mode');
  }
  function removePrintMode() {
    printSection.classList.remove('a4-print-mode');
  }
  if (window.matchMedia) {
    window.matchMedia('print').addListener(function(mql) {
      if (mql.matches) { addPrintMode(); }
      else { removePrintMode(); }
    });
  }
  window.onbeforeprint = addPrintMode;
  window.onafterprint = removePrintMode;
})();
</script>
<div class="row justify-content-center  d-print-none " ng-show="ctrl.ShowFlag ==0">

    <div class="card mt-4 ">
        <div class="card-header">
            <div class="row g-2">



                <div class="btn-toolbar col-8" style="border-radius:15px"
                     role="toolbar" aria-label="Toolbar with button groups">
                    <div class="btn-group cairo" dir="ltr" role="group">
                        <button type="button" disabled class="btn btn-primary btn-success border-ridues  cairo "> واجهة إصدار وثيقة تامين المسافرين</button>
                        <button type="button" ng-click="ctrl.BtnHomePage()" class="btn btn-success bi bi-house-fill"> <span class="m-2">الرئيسية</span></button>
                    </div>


                </div>



                <div class="btn-toolbar  col-4 " role="toolbar"
                     aria-label="Toolbar with button groups">
                    <div class="mt-1">
                        <button class="btn btn-secondary m-1  " data-bs-toggle="modal" data-backdrop="static" data-keyboard="false" data-bs-target="#CalDoc" @*ng-if="ctrl.AgPerObj.Trav_Calculation"*@ ng-click="ctrl.CalcuLateDoc()">     إحتساب وثيقة  </button>

                    </div>
                    <div class=" btn-group   col-auto ">
                        <button class="btn btn-primary m-1  " data-bs-toggle="modal" data-bs-target="#NewItem" data-backdrop="static" data-keyboard="false" ng-click="ctrl.AddNew(1)">     اصدار وثيقة جديدة  </button>



                    </div>
                </div>
            </div>
        </div>

        <div class="card-body">
            <div class="row">
                <div class="col-xs-12 col-sm-12 col-md-3   col-lg-3  col-xl-3 col-xxl-3  mt-2">
                    <div class="hstack gap-3">
                        <input ng-model="search.Any" class="form-control me-auto" type="text" placeholder="بحث  ...">
                    </div>
                </div>
                <div class="col-xs-12 col-sm-12 col-md-4   col-lg-4 col-xl-4 mt-2 float-end  ">
                    <button class="btn btn-secondary  " ng-show="ctrl.AdSearchPer==true" ng-click="ctrl.FullSearch()">بحث متقدم  <span class="bi bi-reply"></span>  </button>
                </div>
                <div class="col-xs-12 col-sm-12 col-md-2  col-lg-2  col-xl-2 col-xxl-2   mt-2 float-end">
                </div>
                <div class="col-xs-12 col-sm-12 col-md-3  col-lg-3  col-xl-3 col-xxl-3   mt-2 float-end">
                    <div class="row">
                        <div class="col-lg-6 col-md-6 col-xl-6 col-xxl-6">
                            <button class="btn btn-danger btn-lg col-12 " ng-show="ctrl.AdSearchPer==true && ctrl.SearchFlag" ng-click="ctrl.CloseSreach()">  <span class="bi bi-arrow-repeat"></span>  إعادة تحميل</button>
                        </div>
                        <div class="col-lg-6 col-md-6 col-xl-6 col-xxl-6" ng-if="ctrl.AgPerObj.Trav_ShowAllDocs">
                            <select class="form-select" ng-model="search.users">
                                <option value="">كل المستخدمين</option>
                                <option ng-repeat="x in ctrl.Users " value="{{x.ID}}">{{x.Name}}</option>

                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="table-responsive mt-4">
                <table class="table table-dark table-hover">

                    <tr>
                        <th class="text-center">#</th>
                        <th class="text-center">ر.الوثيقة</th>
                        <th class="text-center"> اسم المؤمن له </th>
                        <th class="text-center ">النطاق</th>
                        <th class="text-center">من</th>
                        <th class="text-center">المدة</th>
                        <th class="text-center">العمر</th>
                        <th class="text-center">الإجمالي</th>
                        <th class="text-center">وقت الإنشاء</th>
                        <th class="text-center">الحالة </th>
                        <th class="text-center" colspan="3">العمليات</th>
                    </tr>
                    <tr ng-repeat="x in ctrl.InsDoc|filter:search.Any |filter:{AgUserName:search.users ||undefined}:true"
                        ng-class="x.IsInDeleteProgress  ? 'bg-warnin':'' "
                        ng-style="x.IsInDeleteProgress ? {'color':'red'}:{'color':'black'}">
                        <td class="text-center text-white">{{$index + 1}}</td>
                        <td class="text-center text-white">{{x.DocNum}}</td>
                        <td class="text-center text-white">{{x.CuName}}</td>
                        <td class="text-center text-white">{{x.ZoonName}}</td>
                        <td class="text-center text-white">{{x.SDate}}</td>
                        <td class="text-center text-white">{{x.Months}}</td>
                        <td class="text-center text-white">{{x.Age}}</td>
                        <td class="text-center text-white">{{x.Total|number:'2'}}</td>
                        <td class="text-center text-white">{{x.InsertDate}}</td>

                        <td class="text-center text-white"
                            ng-style="x.IsInDeleteProgress ? {'color':'red'}:{'color':'black'}">
                            {{x.IsInDeleteProgress ?
                            'قيد الحذف ':' -' }}
                        </td>

                        <td>
                                <button type="button" class="btn btn-secondary col-12" ng-disabled=" x.IsInDeleteProgress "
                                        ng-click="ctrl.a47008(x)">
                                    <span class="bi bi-credit-card-2-front-fill"></span>
                                </button>

                            </td>
                        <td>
                            <button type="button" class="btn btn-danger col-12" ng-disabled="x.IsInDeleteProgress"
                                    ng-click="ctrl.a47005(x)">
                                <span class="bi bi-x-circle"></span>
                            </button>

                        </td>
                        <!--<td>
                            <button type="button" class="btn btn-success col-12" ng-click="ctrl.a47008(x)"-->
                                    @*ng-disabled=" x.IsInDeleteProgress*@<!-->
                                <span class="bi bi-printer-fill"></span>
                            </button>
                        </td>-->

                    </tr>
                </table>

            </div>
        </div>
    </div>
</div>


<div class="travel-insurance-document" ng-show="ctrl.ShowFlag == 1">
    <!-- Enhanced Control Panel -->
    <div class="travel-control-panel d-print-none">
        <div class="control-container-travel">
            <div class="control-left">
                <button class="btn-travel btn-back" ng-click="ctrl.ShowFlag = 0">
                    <div class="btn-icon">
                        <i class="bi bi-arrow-right-circle"></i>
                    </div>
                    <span class="btn-text">العودة للقائمة</span>
                </button>
            </div>
            <div class="control-center">
                <div class="document-status-travel">
                    <div class="status-indicator-travel active"></div>
                    <span class="status-text-travel">وثيقة تأمين سفر جاهزة للطباعة</span>
                </div>
            </div>
            <div class="control-right">
                <button class="btn-travel btn-print" ng-click="ctrl.print()">
                    <div class="btn-icon">
                        <i class="bi bi-printer-fill"></i>
                    </div>
                    <span class="btn-text">طباعة الوثيقة</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Premium Document Header -->
    <div class="travel-document-header">
        <div class="header-background-travel">
            <div class="header-pattern-travel"></div>
            <div class="header-overlay-travel"></div>
        </div>
        <div class="header-content-travel">
            <div class="travel-logo">
                <div class="logo-circle-travel">
                    <div class="logo-inner-travel">
                        <i class="bi bi-airplane"></i>
                    </div>
                </div>
                <div class="logo-rings-travel">
                    <div class="ring-travel ring-1"></div>
                    <div class="ring-travel ring-2"></div>
                </div>
            </div>
            <div class="header-text-travel">
                <h1 class="document-title-travel">وثيقة تأمين السفر</h1>
                <h2 class="company-name-travel">Travel Insurance Policy</h2>
                <div class="document-meta-travel">
                    <div class="meta-item-travel">
                        <span class="meta-label-travel">رقم الوثيقة</span>
                        <span class="meta-value-travel">{{ctrl.Doc.InsDocNum}}</span>
                    </div>
                    <div class="meta-divider-travel"></div>
                    <div class="meta-item-travel">
                        <span class="meta-label-travel">تاريخ الإصدار</span>
                        <span class="meta-value-travel">{{ctrl.Doc.PrintDate}}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Premium Insured Information Section -->
    <div class="travel-info-section">
        <div class="section-header-travel">
            <div class="section-icon-travel">
                <i class="bi bi-person-vcard-fill"></i>
            </div>
            <div class="section-title-container-travel">
                <h3 class="section-title-travel">معلومات المؤمن له</h3>
                <div class="section-subtitle-travel">Insured Person Information</div>
            </div>
            <div class="section-decoration-travel">
                <div class="decoration-line-travel"></div>
                <div class="decoration-dot-travel"></div>
            </div>
        </div>

        <div class="travel-info-grid">
            <div class="travel-info-card">
                <div class="card-header-travel">
                    <div class="card-icon-travel primary">
                        <i class="bi bi-person-fill"></i>
                    </div>
                    <div class="card-labels">
                        <div class="card-title-ar">اسم المؤمن له</div>
                        <div class="card-title-en">Insured's Name</div>
                    </div>
                </div>
                <div class="card-body-travel">
                    <div class="card-value-travel">{{ctrl.Doc.CuName}}</div>
                </div>
            </div>

            <div class="travel-info-card">
                <div class="card-header-travel">
                    <div class="card-icon-travel success">
                        <i class="bi bi-passport"></i>
                    </div>
                    <div class="card-labels">
                        <div class="card-title-ar">رقم الجواز</div>
                        <div class="card-title-en">Passport No.</div>
                    </div>
                </div>
                <div class="card-body-travel">
                    <div class="card-value-travel">{{ctrl.Doc.PassportID}}</div>
                </div>
            </div>

            <div class="travel-info-card">
                <div class="card-header-travel">
                    <div class="card-icon-travel warning">
                        <i class="bi bi-calendar-event"></i>
                    </div>
                    <div class="card-labels">
                        <div class="card-title-ar">تاريخ الميلاد</div>
                        <div class="card-title-en">Date of Birth</div>
                    </div>
                </div>
                <div class="card-body-travel">
                    <div class="card-value-travel">{{ctrl.Doc.birthDay}}</div>
                </div>
            </div>

            <div class="travel-info-card">
                <div class="card-header-travel">
                    <div class="card-icon-travel info">
                        <i class="bi bi-gender-ambiguous"></i>
                    </div>
                    <div class="card-labels">
                        <div class="card-title-ar">الجنس</div>
                        <div class="card-title-en">Gender</div>
                    </div>
                </div>
                <div class="card-body-travel">
                    <div class="card-value-travel">{{ctrl.Doc.Gender}}</div>
                </div>
            </div>

            <div class="travel-info-card">
                <div class="card-header-travel">
                    <div class="card-icon-travel secondary">
                        <i class="bi bi-telephone-fill"></i>
                    </div>
                    <div class="card-labels">
                        <div class="card-title-ar">رقم الهاتف</div>
                        <div class="card-title-en">Phone No.</div>
                    </div>
                </div>
                <div class="card-body-travel">
                    <div class="card-value-travel">{{ctrl.Doc.Cus_Phone}}</div>
                </div>
            </div>

            <div class="travel-info-card">
                <div class="card-header-travel">
                    <div class="card-icon-travel danger">
                        <i class="bi bi-clock-fill"></i>
                    </div>
                    <div class="card-labels">
                        <div class="card-title-ar">مدة التأمين (أيام)</div>
                        <div class="card-title-en">Coverage Period (Days)</div>
                    </div>
                </div>
                <div class="card-body-travel">
                    <div class="card-value-travel">{{ctrl.Doc.durationInDays}}</div>
                </div>
            </div>
        </div>
    </div>


    <!-- Premium Financial Section -->
    <div class="travel-financial-section">
        <div class="section-header-travel">
            <div class="section-icon-travel">
                <i class="bi bi-cash-stack"></i>
            </div>
            <div class="section-title-container-travel">
                <h3 class="section-title-travel">التفاصيل المالية</h3>
                <div class="section-subtitle-travel">Financial Details - العملة د.ل / Currency L.D</div>
            </div>
            <div class="section-decoration-travel">
                <div class="decoration-line-travel"></div>
                <div class="decoration-dot-travel"></div>
            </div>
        </div>

        <div class="financial-breakdown-travel">
            <div class="financial-grid-travel">
                <div class="financial-item-travel primary">
                    <div class="item-header-travel">
                        <div class="item-icon-travel">
                            <i class="bi bi-cash-coin"></i>
                        </div>
                        <div class="item-labels">
                            <h4 class="item-title-ar">صافي القسط</h4>
                            <h4 class="item-title-en">Net Premium</h4>
                        </div>
                    </div>
                    <div class="item-amount-travel">{{ctrl.Doc.Totoal | number:'2'}} د.ل</div>
                    <div class="item-description-travel">القسط الأساسي للتأمين</div>
                </div>

                <div class="financial-item-travel secondary" ng-repeat="x in ctrl.Tax">
                    <div class="item-header-travel">
                        <div class="item-icon-travel">
                            <i class="bi bi-percent"></i>
                        </div>
                        <div class="item-labels">
                            <h4 class="item-title-ar">{{x.VirDesc}}</h4>
                            <h4 class="item-title-en">{{x.VirDescEn}}</h4>
                        </div>
                    </div>
                    <div class="item-amount-travel">{{x.Value | number:'2'}} د.ل</div>
                    <div class="item-description-travel">رسوم وضرائب إضافية</div>
                </div>
            </div>

            <div class="total-summary-travel">
                <div class="summary-content-travel">
                    <div class="summary-icon-travel">
                        <i class="bi bi-calculator"></i>
                    </div>
                    <div class="summary-text-travel">
                        <h3 class="summary-title-ar">إجمالي المبلغ المستحق</h3>
                        <h3 class="summary-title-en">Total Premium Amount</h3>
                        <p class="summary-description-travel">المبلغ الكامل شامل جميع الرسوم والضرائب</p>
                    </div>
                    <div class="summary-amount-travel">
                        <span class="amount-value-travel">{{ctrl.Doc.TotTax | number:'2'}}</span>
                        <span class="amount-currency-travel">دينار ليبي</span>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- Coverage Zone Section -->
    <div class="travel-coverage-section">
        <div class="section-header-travel">
            <div class="section-icon-travel">
                <i class="bi bi-globe-americas"></i>
            </div>
            <div class="section-title-container-travel">
                <h3 class="section-title-travel">منطقة التغطية</h3>
                <div class="section-subtitle-travel">Coverage Zone</div>
            </div>
            <div class="section-decoration-travel">
                <div class="decoration-line-travel"></div>
                <div class="decoration-dot-travel"></div>
            </div>
        </div>

        <div class="coverage-zone-container">
            <div class="zone-display">
                <div class="zone-content">
                    <div class="zone-text-ar">{{ctrl.Doc.zoonTXt}}</div>
                    <div class="zone-text-en" dir="ltr">{{ctrl.Doc.zoonTXtEn}}</div>
                </div>
                <div class="zone-icon">
                    <i class="bi bi-geo-alt-fill"></i>
                </div>
            </div>
        </div>
    </div>
    <!-- Contact Information Section -->
    <div class="travel-contact-section">
        <div class="section-header-travel">
            <div class="section-icon-travel">
                <i class="bi bi-telephone-fill"></i>
            </div>
            <div class="section-title-container-travel">
                <h3 class="section-title-travel">معلومات الاتصال</h3>
                <div class="section-subtitle-travel">Contact Information</div>
            </div>
            <div class="section-decoration-travel">
                <div class="decoration-line-travel"></div>
                <div class="decoration-dot-travel"></div>
            </div>
        </div>

        <div class="contact-grid">
            <div class="contact-card">
                <div class="contact-header">
                    <div class="contact-icon">
                        <i class="bi bi-telephone-plus"></i>
                    </div>
                    <h4 class="contact-title">أرقام الطوارئ</h4>
                    <p class="contact-subtitle">Emergency Numbers</p>
                </div>
                <div class="contact-details">
                    <div class="contact-item">
                        <i class="bi bi-geo-alt"></i>
                        <span>تونس: +216 71104540</span>
                    </div>
                    <div class="contact-item">
                        <i class="bi bi-flag"></i>
                        <span>UK: 08452171379</span>
                    </div>
                    <div class="contact-item">
                        <i class="bi bi-building"></i>
                        <span>Hajj KSA: 8008973919</span>
                    </div>
                </div>
            </div>

            <div class="contact-card">
                <div class="contact-header">
                    <div class="contact-icon">
                        <i class="bi bi-envelope-at"></i>
                    </div>
                    <h4 class="contact-title">معلومات الوثيقة</h4>
                    <p class="contact-subtitle">Policy Information</p>
                </div>
                <div class="contact-details">
                    <div class="contact-item">
                        <i class="bi bi-file-text"></i>
                        <span><strong>Policy No:</strong> {{ctrl.Doc.InsDocNum}}</span>
                    </div>
                    <div class="contact-item">
                        <i class="bi bi-passport"></i>
                        <span><strong>Passport:</strong> {{ctrl.Doc.PassportID}}</span>
                    </div>
                    <div class="contact-item">
                        <i class="bi bi-person"></i>
                        <span><strong>Name:</strong> {{ctrl.Doc.UserName}}</span>
                    </div>
                    <div class="contact-item">
                        <i class="bi bi-envelope"></i>
                        <span><strong>Email:</strong> et.aletay.medical@ip_asealistone.com</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Premium Document Footer -->
    <div class="travel-document-footer">
        <div class="footer-background-travel">
            <div class="footer-pattern-travel"></div>
        </div>
        <div class="footer-content-travel">
            <div class="footer-main-travel">
                <div class="footer-logo-travel">
                    <div class="footer-logo-circle-travel">
                        <i class="bi bi-airplane"></i>
                    </div>
                </div>
                <div class="footer-info-travel">
                    <h4 class="footer-title-travel">الشركة الإتحادية للتأمين</h4>
                    <div class="footer-date">
                        <div class="date-label">التاريخ / Date:</div>
                        <div class="date-value">{{ctrl.Doc.PrintDate}}</div>
                    </div>
                </div>
            </div>

            <div class="footer-legal-travel">
                <div class="legal-text-travel">
                    <p>هذه الوثيقة صادرة عن الشركة الإتحادية للتأمين وتخضع لأحكام وشروط التأمين المعمول بها</p>
                    <p>This policy is issued by Federal Insurance Company and subject to terms and conditions</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Premium CSS for Travel Insurance Document -->
<style>
    /* Main Document Styling */
    .travel-insurance-document {
        font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
        background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 50%, #f0f4f8 100%);
        min-height: 100vh;
        padding: 40px 20px;
        color: #1e293b;
        position: relative;
        overflow-x: hidden;
    }

    .travel-insurance-document::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.05) 0%, transparent 50%);
        pointer-events: none;
        z-index: -1;
    }

    /* Enhanced Control Panel */
    .travel-control-panel {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 25px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        margin-bottom: 40px;
    }

    .control-container-travel {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 30px;
    }

    .btn-travel {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        border: none;
        border-radius: 15px;
        padding: 15px 30px;
        color: white;
        font-weight: 600;
        font-size: 16px;
        display: flex;
        align-items: center;
        gap: 12px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
        text-decoration: none;
        cursor: pointer;
    }

    .btn-back {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        box-shadow: 0 4px 15px rgba(245, 158, 11, 0.4);
    }

    .btn-print {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
    }

    .btn-travel:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(59, 130, 246, 0.6);
    }

    .btn-icon {
        font-size: 18px;
    }

    .document-status-travel {
        display: flex;
        align-items: center;
        gap: 10px;
        background: rgba(16, 185, 129, 0.1);
        padding: 12px 20px;
        border-radius: 25px;
        border: 2px solid rgba(16, 185, 129, 0.2);
    }

    .status-indicator-travel {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: linear-gradient(135deg, #10b981, #059669);
        animation: pulse-travel 2s infinite;
    }

    .status-text-travel {
        color: #059669;
        font-weight: 600;
        font-size: 14px;
    }

     pulse-travel {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }

    /* Premium Document Header */
    .travel-document-header {
        position: relative;
        margin-bottom: 50px;
        border-radius: 25px;
        overflow: hidden;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    }

    .header-background-travel {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    }

    .header-pattern-travel {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image:
            radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
        background-size: 100px 100px;
    }

    .header-overlay-travel {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.1);
    }

    .header-content-travel {
        position: relative;
        z-index: 2;
        padding: 60px 40px;
        display: flex;
        align-items: center;
        gap: 40px;
        color: white;
    }

    .travel-logo {
        position: relative;
        flex-shrink: 0;
    }

    .logo-circle-travel {
        width: 120px;
        height: 120px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(10px);
        border: 3px solid rgba(255, 255, 255, 0.3);
        position: relative;
        z-index: 3;
    }

    .logo-inner-travel {
        font-size: 3.5rem;
        color: white;
    }

    .logo-rings-travel {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .ring-travel {
        position: absolute;
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .ring-1 {
        width: 140px;
        height: 140px;
        animation: rotate-travel 20s linear infinite;
    }

    .ring-2 {
        width: 160px;
        height: 160px;
        animation: rotate-travel 30s linear infinite reverse;
    }

     rotate-travel {
        from { transform: translate(-50%, -50%) rotate(0deg); }
        to { transform: translate(-50%, -50%) rotate(360deg); }
    }

    .header-text-travel {
        flex: 1;
    }

    .document-title-travel {
        font-size: 3rem;
        font-weight: 800;
        margin-bottom: 15px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        line-height: 1.2;
    }

    .company-name-travel {
        font-size: 1.8rem;
        font-weight: 600;
        margin-bottom: 25px;
        opacity: 0.9;
    }

    .document-meta-travel {
        display: flex;
        align-items: center;
        gap: 20px;
        background: rgba(255, 255, 255, 0.1);
        padding: 20px;
        border-radius: 15px;
        backdrop-filter: blur(10px);
    }

    .meta-item-travel {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .meta-label-travel {
        font-size: 0.9rem;
        opacity: 0.8;
        font-weight: 500;
    }

    .meta-value-travel {
        font-size: 1.1rem;
        font-weight: 700;
    }

    .meta-divider-travel {
        width: 2px;
        height: 40px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 1px;
    }

    /* Premium Section Headers */
    .travel-info-section,
    .travel-financial-section,
    .travel-coverage-section,
    .travel-contact-section {
        margin-bottom: 50px;
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(20px);
        border-radius: 25px;
        padding: 40px;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .section-header-travel {
        display: flex;
        align-items: center;
        gap: 20px;
        margin-bottom: 35px;
        padding-bottom: 20px;
        border-bottom: 2px solid rgba(59, 130, 246, 0.1);
        position: relative;
    }

    .section-icon-travel {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
    }

    .section-title-container-travel {
        flex: 1;
    }

    .section-title-travel {
        font-size: 1.8rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 5px;
    }

    .section-subtitle-travel {
        font-size: 1rem;
        color: #64748b;
        font-weight: 500;
    }

    .section-decoration-travel {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .decoration-line-travel {
        width: 60px;
        height: 3px;
        background: linear-gradient(90deg, #3b82f6, #1d4ed8);
        border-radius: 2px;
    }

    .decoration-dot-travel {
        width: 12px;
        height: 12px;
        background: #3b82f6;
        border-radius: 50%;
    }

    /* Travel Info Cards */
    .travel-info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 25px;
    }

    .travel-info-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(226, 232, 240, 0.8);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .travel-info-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    }

    .travel-info-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
    }

    .card-header-travel {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 20px;
    }

    .card-icon-travel {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        color: white;
        flex-shrink: 0;
    }

    .card-icon-travel.primary {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    }

    .card-icon-travel.success {
        background: linear-gradient(135deg, #10b981, #059669);
    }

    .card-icon-travel.warning {
        background: linear-gradient(135deg, #f59e0b, #d97706);
    }

    .card-icon-travel.info {
        background: linear-gradient(135deg, #06b6d4, #0891b2);
    }

    .card-icon-travel.secondary {
        background: linear-gradient(135deg, #6b7280, #4b5563);
    }

    .card-icon-travel.danger {
        background: linear-gradient(135deg, #ef4444, #dc2626);
    }

    .card-labels {
        flex: 1;
    }

    .card-title-ar {
        font-size: 1.1rem;
        font-weight: 600;
        color: #374151;
        margin-bottom: 3px;
    }

    .card-title-en {
        font-size: 0.9rem;
        color: #64748b;
        font-weight: 500;
    }

    .card-body-travel {
        padding-left: 65px;
    }

    .card-value-travel {
        font-size: 1.3rem;
        font-weight: 700;
        color: #1e293b;
        line-height: 1.3;
    }

    /* Financial Section Styling */
    .financial-grid-travel {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 25px;
        margin-bottom: 40px;
    }

    .financial-item-travel {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 25px;
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .financial-item-travel::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #f59e0b, #d97706);
    }

    .financial-item-travel:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
    }

    .item-header-travel {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 15px;
    }

    .item-icon-travel {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #f59e0b, #d97706);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
    }

    .item-labels {
        flex: 1;
    }

    .item-title-ar {
        font-size: 1.1rem;
        font-weight: 600;
        color: #374151;
        margin-bottom: 3px;
    }

    .item-title-en {
        font-size: 0.9rem;
        color: #64748b;
        font-weight: 500;
    }

    .item-amount-travel {
        font-size: 2.2rem;
        font-weight: 800;
        color: #1e293b;
        margin-bottom: 8px;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        letter-spacing: 0.5px;
    }

    .item-description-travel {
        font-size: 0.9rem;
        color: #64748b;
        font-weight: 500;
    }

    .total-summary-travel {
        background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
        border-radius: 25px;
        padding: 40px;
        color: white;
        box-shadow: 0 15px 50px rgba(30, 41, 59, 0.3);
    }

    .summary-content-travel {
        display: flex;
        align-items: center;
        gap: 30px;
    }

    .summary-icon-travel {
        width: 80px;
        height: 80px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        backdrop-filter: blur(10px);
    }

    .summary-text-travel {
        flex: 1;
    }

    .summary-title-ar {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 5px;
    }

    .summary-title-en {
        font-size: 1.4rem;
        font-weight: 600;
        margin-bottom: 8px;
        opacity: 0.9;
    }

    .summary-description-travel {
        font-size: 1rem;
        opacity: 0.8;
        margin: 0;
    }

    .summary-amount-travel {
        text-align: right;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
    }

    .amount-value-travel {
        font-size: 3.5rem;
        font-weight: 900;
        line-height: 1;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        letter-spacing: 1px;
    }

    .amount-currency-travel {
        font-size: 1.4rem;
        opacity: 0.8;
        margin-top: 5px;
        font-weight: 600;
    }

    /* Coverage Zone Section */
    .coverage-zone-container {
        display: flex;
        justify-content: center;
    }

    .zone-display {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(16, 185, 129, 0.3);
        text-align: center;
        min-width: 600px;
        display: flex;
        align-items: center;
        gap: 20px;
    }

    .zone-content {
        flex: 1;
    }

    .zone-text-ar {
        font-size: 1.3rem;
        font-weight: 700;
        margin-bottom: 10px;
    }

    .zone-text-en {
        font-size: 1.1rem;
        font-weight: 600;
        opacity: 0.9;
    }

    .zone-icon {
        font-size: 3rem;
        opacity: 0.8;
    }

    /* Contact Section */
    .contact-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 30px;
    }

    .contact-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(226, 232, 240, 0.8);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .contact-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    }

    .contact-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
    }

    .contact-header {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 25px;
        padding-bottom: 15px;
        border-bottom: 2px solid rgba(59, 130, 246, 0.1);
    }

    .contact-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
    }

    .contact-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 5px;
    }

    .contact-subtitle {
        font-size: 1rem;
        color: #64748b;
        font-weight: 500;
        margin: 0;
    }

    .contact-details {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .contact-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px;
        background: rgba(248, 250, 252, 0.8);
        border-radius: 10px;
        border-left: 4px solid #3b82f6;
        transition: all 0.3s ease;
    }

    .contact-item:hover {
        background: rgba(59, 130, 246, 0.05);
        transform: translateX(5px);
    }

    .contact-item i {
        color: #3b82f6;
        font-size: 1.1rem;
        width: 20px;
    }

    .contact-item span {
        font-weight: 600;
        color: #374151;
    }

    /* Document Footer */
    .travel-document-footer {
        background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
        border-radius: 25px;
        margin-top: 60px;
        position: relative;
        overflow: hidden;
        box-shadow: 0 20px 60px rgba(30, 41, 59, 0.3);
    }

    .footer-background-travel {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
    }

    .footer-pattern-travel {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image:
            radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
        background-size: 60px 60px;
    }

    .footer-content-travel {
        position: relative;
        z-index: 2;
        padding: 50px 40px;
        color: white;
    }

    .footer-main-travel {
        display: flex;
        align-items: center;
        gap: 30px;
        margin-bottom: 40px;
        padding-bottom: 30px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .footer-logo-travel {
        flex-shrink: 0;
    }

    .footer-logo-circle-travel {
        width: 80px;
        height: 80px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        backdrop-filter: blur(10px);
        border: 2px solid rgba(255, 255, 255, 0.2);
    }

    .footer-info-travel {
        flex: 1;
    }

    .footer-title-travel {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 8px;
    }

    .footer-subtitle-travel {
        font-size: 1.1rem;
        opacity: 0.8;
        margin-bottom: 20px;
    }

    .footer-date {
        display: flex;
        align-items: center;
        gap: 10px;
        background: rgba(255, 255, 255, 0.1);
        padding: 10px 15px;
        border-radius: 10px;
        backdrop-filter: blur(5px);
    }

    .date-label {
        font-weight: 600;
        opacity: 0.9;
    }

    .date-value {
        font-weight: 700;
    }

    .footer-legal-travel {
        text-align: center;
        margin-bottom: 40px;
        padding: 25px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 15px;
        backdrop-filter: blur(10px);
    }

    .legal-text-travel p {
        margin-bottom: 10px;
        font-size: 0.95rem;
        opacity: 0.9;
    }

    .footer-signature-travel {
        margin-bottom: 30px;
    }

    .signature-section-travel {
        display: flex;
        justify-content: space-between;
        gap: 40px;
    }

    .signature-box-travel, .stamp-box-travel {
        flex: 1;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 15px;
        padding: 25px;
        text-align: center;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .signature-line-travel {
        width: 200px;
        height: 2px;
        background: rgba(255, 255, 255, 0.3);
        margin: 0 auto 15px;
        border-radius: 1px;
    }

    .signature-label-travel {
        font-size: 0.9rem;
        opacity: 0.8;
    }

    .stamp-area-travel {
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .stamp-border-travel {
        width: 120px;
        height: 60px;
        border: 2px dashed rgba(255, 255, 255, 0.3);
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .stamp-text-travel {
        font-size: 0.8rem;
        opacity: 0.6;
        text-align: center;
        line-height: 1.2;
    }

    /* Responsive Design */
     (max-width: 768px) {
        .travel-insurance-document {
            padding: 20px 15px;
        }

        .header-content-travel {
            flex-direction: column;
            text-align: center;
            gap: 30px;
            padding: 40px 20px;
        }

        .document-title-travel {
            font-size: 2rem;
        }

        .company-name-travel {
            font-size: 1.4rem;
        }

        .document-meta-travel {
            flex-direction: column;
            gap: 15px;
        }

        .control-container-travel {
            flex-direction: column;
            gap: 20px;
        }

        .travel-info-grid {
            grid-template-columns: 1fr;
        }

        .contact-grid {
            grid-template-columns: 1fr;
        }

        .footer-main-travel {
            flex-direction: column;
            text-align: center;
            gap: 20px;
        }

        .signature-section-travel {
            flex-direction: column;
            gap: 20px;
        }
    }

    /* Print Styles */
     print {
        .travel-insurance-document {
            background: white !important;
            padding: 20px !important;
        }

        .travel-insurance-document::before {
            display: none !important;
        }

        .travel-control-panel {
            display: none !important;
        }

        .header-background-travel,
        .header-pattern-travel,
        .header-overlay-travel {
            background: white !important;
        }

        .header-content-travel {
            color: #000 !important;
            background: white !important;
            border: 2px solid #000 !important;
            border-radius: 0 !important;
        }

        .travel-logo,
        .logo-circle-travel,
        .logo-inner-travel {
            color: #000 !important;
            background: white !important;
            border: 1px solid #000 !important;
        }

        .logo-rings-travel,
        .ring-travel {
            display: none !important;
        }

        .document-title-travel,
        .company-name-travel,
        .meta-label-travel,
        .meta-value-travel {
            color: #000 !important;
        }

        .document-meta-travel {
            background: white !important;
            border: 1px solid #000 !important;
        }

        .travel-info-section,
        .travel-financial-section,
        .travel-coverage-section,
        .travel-contact-section {
            background: white !important;
            border: 1px solid #000 !important;
            box-shadow: none !important;
            page-break-inside: avoid;
        }

        .section-icon-travel,
        .card-icon-travel,
        .contact-icon {
            background: white !important;
            color: #000 !important;
            border: 1px solid #000 !important;
        }

        .section-title-travel,
        .section-subtitle-travel,
        .card-title-ar,
        .card-title-en,
        .card-value-travel,
        .contact-title,
        .contact-subtitle {
            color: #000 !important;
        }

        .travel-info-card,
        .contact-card {
            background: white !important;
            border: 1px solid #000 !important;
            box-shadow: none !important;
        }

        .travel-info-card::before,
        .contact-card::before {
            background: #000 !important;
        }

        .zone-display {
            background: white !important;
            color: #000 !important;
            border: 2px solid #000 !important;
        }

        .zone-text-ar,
        .zone-text-en {
            color: #000 !important;
        }

        .travel-document-footer {
            background: white !important;
            color: #000 !important;
            border: 2px solid #000 !important;
            box-shadow: none !important;
        }

        .footer-background-travel,
        .footer-pattern-travel {
            display: none !important;
        }

        .footer-logo-circle-travel,
        .signature-box-travel,
        .stamp-box-travel {
            background: white !important;
            color: #000 !important;
            border: 1px solid #000 !important;
        }

        .footer-title-travel,
        .footer-subtitle-travel,
        .date-label,
        .date-value,
        .legal-text-travel p,
        .signature-label-travel,
        .stamp-text-travel {
            color: #000 !important;
        }

        .signature-line-travel {
            background: #000 !important;
        }

        .stamp-border-travel {
            border-color: #000 !important;
        }

        /* Hide animations and transitions in print */
        * {
            animation: none !important;
            transition: none !important;
            box-shadow: none !important;
            text-shadow: none !important;
        }
    }
</style>



    <div class="row d-none d-print-block  bg-white " page-size = "A4" ng-show="ctrl.printflag == 1" >
    <div class="row   bg-transparent" >
    <div class="d d-print-none col-md-6 col-sm-6  col-xl-6 col-xxl-6 bg-transparent" >
    <button type="button" class="btn btn-success float-end col-lg-2 col-md-3 col-xxl-2 col-xl-2 m-1"
    ng-click="ctrl.print()" >
    <span class="bi bi-printer-fill" > </span >
    </button >

    <button type="button" class="btn btn-primary float-end col-lg-2 col-md-3 col-xxl-2 col-xl-2 m-1"
    ng-click="ctrl.ShowFlag = 0" >
    رجوع
    </button >
    </div >
    </div >
    <div class="container bordered justify-content-center text-center" @*style="margin-left : 60cm !important"*@ >
    <table class="table table-bordered " style="margin-top : 6cm !important ; font-size : 10px !important ; font-family:'Times New Roman' !important" >
    <tbody >
    <tr >
    <th > اسم المؤمن له</th >
    <td> {{ctrl.Doc.CuName}}</td>
                    <td style="direction: ltr;">  </td>
                    <th class="text-start">Insured's Name</th>
                </tr>
                <tr>
                    <th>رقم الجواز</th>
                    <td>{{ctrl.Doc.PassportID}}</td>
                    <td style="direction: ltr;">  </td>
                    <th class="text-start">Passport No.</th>
                </tr>
                <tr>
                    <th>تاريخ الميلاد</th>
                    <td>{{ctrl.Doc.birthDay}}</td>
                    <td style="direction: ltr;">  </td>
                    <th class="text-start">Date of birth</th>
                </tr>
                <tr>
                    <th>الجنس</th>
                    <td>{{ctrl.Doc.Gender == 1 ? 'ذكر' : 'أنثى'}}</td>
                    <td style="direction: ltr;">  </td>
                    <th class="text-start">Sex</th>
                </tr>
                <tr>
                    <th>رقم الهاتف</th>
                    <td>{{ctrl.Doc.Cus_Phone}}</td>
                    <td style="direction: ltr;">  </td>
                    <th class="text-start">Phone No.</th>
                </tr>
                <tr>
                    <th>مدة التأمين</th>
                    <td>{{ctrl.Doc.durationInDays}}</td>
                    <td style="direction: ltr;">  </td>
                    <th class="text-start">Period of Coverage</th>
                </tr>
            </tbody>
        </table>


        <div class="d-flex justify-content-center mt-4 mb-3">
            <div style="width: 60%; font-size: 10px !important; font-family: 'Times New Roman' !important ">
                <div class="d-flex align-items-center mb-2 fw-bold">
                    <div class="flex-grow-1 text-end">العملة د.ل</div>
                    <div class="flex-grow-1 text-end"></div>
                    <div class="flex-grow-1 text-start">Currency L.D</div>
                </div>
                <table class="table table-bordered mb-0">
                    <tbody>
                        <tr>
                            <th>صافي القسط</th>
                            <td>{{ctrl.Doc.Totoal}}</td>
                            <th class="text-start">Net Premium</th>
                        </tr>
                        <tr ng-repeat="x in ctrl.Tax">
                            <th>{{x.VirDesc}}</th>
                            <td>{{x.Value | number:'2'}}</td>
                            <th>{{x.VirDescEn}}</th>
                        </tr>
                        <tr>
                            <th>إجمالي القسط</th>
                            <td>{{ctrl.Doc.TotTax}}</td>
                            <th class="text-start">Total Premium</th>
                        </tr>
                    </tbody>
                </table>

            </div>
        </div>



        <div class="border border-0 p-3 mt-4 text-center" style="width: 100%; font-size: 10px !important; font-family: 'Times New Roman' !important ">
            <div class="fw-bold mb-2">
                المنطقة (1) : جميع أنحاء العالم ماعدا ليبيا - أمريكا  - كندا - اليابان - أستراليا
            </div>
            <div class="fw-bold">
                Zone (1) : Worldwide except Libya, United States of America, Canada, Japan and Australia
            </div>
        </div>

        <div class="border border-0 p-0 mt-4" style="width: 100%; font-size: 10px !important; font-family: 'Times New Roman' !important ">
            <table class="table table-bordered mb-0" style="table-layout: fixed;">
                <tr>

                    <td class="text-end fw-bold">
                        <br>

                    </td>

                    <td></td>

                    <td class="text-start fw-bold">
                    </td>
                </tr>
            </table>
        </div>

        <div class="d-flex justify-content-between fw-bold" style="width: 100%; font-size: 10px !important; font-family: 'Times New Roman' !important ">
            <div class="text-end" style="flex: 1;" dir="rtl">

            </div>
            <div class="text-start" style="flex: 1;" dir="ltr">

            </div>
        </div>
        <div class="mt-5" style="width: 100%; margin-bottom: 3cm !important; font-size: 10px !important; font-family: 'Times New Roman' !important ">
            <div class="row d-flex justify-content-between align-items-center px-5 fw-bold mt-5">
                <div class="text-start fw-bold">
                    +216 71104540 - UK 08452171379<br>
                    Hajj KSA 8008973919
                </div>
                <div class="text-end" style="flex: 1;">
                    <div>
                        <br>

                    </div>
                </div>
            </div>
            <div class="text-start" style="flex: 1;">
                <div class="text-start fw-bold">
                    التاريخ / DATE <br />
                    {{ctrl.Doc.insert_date}}
                </div>
            </div>
        </div>
      
        <div class="row mt-4">
            <div class="col-6 small-box text-start border-2" style="line-height: 1.8; font-size: 10px; font-family: 'Times New Roman' !important">
                <div><strong>Travel Insurance Policy No:</strong> {{ctrl.Doc.InsuNumber}}</div>
                <div><strong>Passport No:</strong> {{ctrl.Doc.PassportID}}</div>
                <div><strong>Insured's Name:</strong> {{ctrl.Doc.NameCust}}</div>
                <div>
                    <strong>E-mail:</strong>
                    <span style="color: #007bff;">et.aletay.medical@ip_asealistone.com</span>
                </div>
            </div>
            <div class="col-6 small-box border-2">
                <div style="text-align: center; margin: 20px 0; font-size: 10px !important; font-family: 'Times New Roman' !important ">
                    <div style="background-color: rgb(99, 31, 31); color: white; padding: 5px 10px; border-radius: 5px; display: inline-block; font-weight: bold;">

                    </div>
                </div>
                <div style="display: flex; align-items: center; gap: 10px; text-align: start; font-size: 10px !important">

                    <div style="text-align: left; direction: ltr; font-weight: bold;">

                        <span style="background-color: #f8d7da; color: #721c24; padding: 3px 8px; border: 1px solid #f5c6cb; border-radius: 4px; display: inline-block; margin-right: 8px;">

                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



    <div class="modal fade d-print-none md-dialog-content" id="NewItem" tabindex="-1" data-bs-backdrop="static" aria-labelledby="{{ctrl.Title}}" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ctrl.DlTitle}}</h5>


                    <button type="button" class="btn-close float-start" data-bs-dismiss="modal" aria-label="Close"></button>


                </div>
                <div class="modal-body">

                    <form name="mFrm" class="" autocomplete="on" novalidate>


                        <div class="row mt-1">

                            <div class="col-8">
                                <label for="Zid">النطاق الجغرافي <span class="text-danger">   * </span> </label>
                                <select required class="form-select" name="Zid"
                                        ng-options="x.ID as x.Name for x in ctrl.Objzn " ng-model="ctrl.Zid" ng-change="ctrl.SelectedZoonChnaged()"
                                        ng-style="mFrm.Zid.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}">
                                </select>

                            </div>

                            <div class="col-4">
                                <label for="Gender">الجنس   <span class="text-danger">   * </span> </label>
                                <select class="form-select" name="Gender" ng-required="ctrl.require==1" ng-model="ctrl.MainObj.Gender"
                                        ng-style=" mFrm.Gender.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}">
                                    <option value="1">ذكر</option>
                                    <option value="2">أنثى</option>
                                </select>
                            </div>


                        </div>

                        <div class="row mt-2">
                            <div class="col-6 ">
                                <label for="CuName">اسم المشترك       <span class="text-danger">   * </span> </label>
                                <input ng-required="ctrl.require==1" maxlength="50" minlength="2" name="CuName" type="text"
                                       ng-model="ctrl.MainObj.CuName" class="form-control " ng-style="mFrm.CuName.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}">
                                <span ng-show="mFrm.CuName.$error.minlength " class="text-danger float-end m-2">
                                    اقل عدد حروف
                                    2
                                </span>
                                <span ng-show="mFrm.CuName.$error.maxlength " class="text-danger float-end m-2">
                                    أكثر عدد
                                    حروف 20
                                </span>
                            </div>

                            <div class="col-6 ">
                                <label for="NameCustEN">اسم المشترك    <span class="text-danger"> باللغة الانجليزية مطابق لإثبات الهوية            </span>  <span class="text-danger">   * </span> </label>
                                <input ng-required="ctrl.require==1" maxlength="50" minlength="5" name="NameCustEN" id="NameCustEN" type="text"
                                       ng-model="ctrl.MainObj.NameCustEN" class="form-control " ng-style="mFrm.NameCustEN.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}" />
                                <span ng-show="mFrm.NameCustEN.$error.minlength " class="text-danger float-end m-2">
                                    اقل عدد
                                    حروف 5
                                </span>
                                <span ng-show="mFrm.NameCustEN.$error.maxlength " class="text-danger float-end m-2">
                                    أكثر عدد
                                    حروف 20
                                </span>

                            </div>

                        </div>







                        <div class="row mt-2">
                            <div class="col-4 ">
                                <label for="NatID">رقم جواز السفر  <span class="text-danger">   * </span> </label>
                                <input ng-required="ctrl.require==1" maxlength="50" minlength="5" name="NatID" type="text" lang="en"
                                       ng-model="ctrl.MainObj.PassportID" class="form-control " ng-style=" mFrm.NatID.$valid  ? {'border':'green solid 1px'}:{'border':'red solid 1px '}">
                                <span ng-show="mFrm.NatID.$error.minlength " class="text-danger float-end m-2">
                                    اقل عدد حروف
                                    5
                                </span>
                                <span ng-show="mFrm.NatID.$error.maxlength " class="text-danger float-end m-2">
                                    أكثر عدد حروف
                                    20
                                </span>
                            </div>


                            <div class="col-4">

                                <label for="BDay">سنة الميلاد  <span class="text-danger">   * </span> </label>
                                <input ng-required="ctrl.require==1" name="BDay" type="number" ng-model="ctrl.MainObj.BDay"
                                       ng-click="ctrl.MainObj.BDay " min="1950" max="@DateTime.Now.Year.ToString()"
                                       class="form-control text-center" ng-style=" mFrm.BDay.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}">

                                <span ng-show="mFrm.BDay.$error.min" class="text-danger float-end m-2">
                                    الرجاء ادخال سنة
                                    الميلاد بشكل صحيح
                                </span>
                                <span ng-show="mFrm.BDay.$error.max" class="text-danger float-end m-2">
                                    الرجاء ادخال سنة
                                    الميلاد بشكل صحيح
                                </span>


                            </div>


                            <div class="col-4">
                                <label for="Cus_Phone">رقم هاتف المؤمن  </label>
                                <input pattern="^(?:0|\(?\+33\)?\s?|0033\s?)[1-79](?:[\.\-\s]?\d\d){4}$"
                                       ng-model="ctrl.MainObj.Cus_Phone" name="Cus_Phone" class="form-control" ng-style=" mFrm.Cus_Phone.$valid ?{'border':'green solid 1px'}:{'border':'red solid 1px '}">
                                <span ng-show="mFrm.Cus_Phone.$error.pattern" class="text-danger float-end m-2">
                                    أكتب رقم
                                    الهاتف بشكل صحيح
                                </span>

                            </div>


                        </div>




                        <div class="row mt-2">

                            <div class="col-4">
                                <label for="SDate">إعتبارا من ظهر يوم <span class="text-danger">  *</span>  </label>
                                <input type="date" class="form-control" name="SDate" required disabled ng-model="ctrl.MainObj.SDate"
                                       ng-style=" mFrm.SDate.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}" />
                                <span ng-show="mFrm.SDate.$error.minDate" class="text-danger float-end mt-0">
                                    لا يمكن إدراج
                                    وثيقة بتاريخ رجعي
                                </span>
                                <span ng-show="mFrm.SDate.$dirty && !mFrm.SDate.$error.required && !mFrm.SDate.$valid && !mFrm.SDate.$error.minDate "
                                      class="text-danger float-end mt-0">أدخل التاريخ بشكل صحيح</span>

                            </div>

                            <div class="col-4">
                                <label for="Months">المدة <span class="text-danger">  *</span> </label>

                                <select required ng-model="ctrl.MainObj.Months" class="form-select text-center"
                                        name="Months" ng-style=" mFrm.Months.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}">
                                    <option value="3">
                                        <lable>أشهر</lable>
                                        <lable>3</lable>
                                    </option>
                                    <option value="6">
                                        <lable>أشهر</lable>
                                        <lable>6</lable>
                                    </option>
                                    <option value="12"> سنة</option>
                                    <option value="24"> سنتين  </option>
                                    <option value="36"> ثلاث سنوات </option>
                                </select>
                            </div>

                            <div class="col-4">
                                <label for="InsuNumber"> رقم الورقة  <span class="text-danger">  *</span></label>
                                <input required name="InsuNumber" type="number" ng-model="ctrl.MainObj.InsuNumber"
                                       ng-click="ctrl.IsExistPayperNum = 0" min="1"
                                       class="form-control text-center" ng-style=" mFrm.InsuNumber.$valid && ctrl.IsExistPayperNum == 0 ? {'border':'green solid 1px'}:{'border':'red solid 1px '}">
                                <span ng-show="mFrm.InsuNumber.$error.min" class="text-danger float-end m-2">
                                    اقل عدد يمكن
                                    إدخاله 1
                                </span>
                                <span ng-show="ctrl.IsExistPayperNum == 1" class="text-danger float-end m-2">
                                    الرقم غير
                                    صحيح
                                </span>
                            </div>

                        </div>

                    </form>

                </div>
                <div class="modal-footer justify-content-center" ng-show=" ctrl.DlFalg == 0">
                    <button type="button" class="btn btnWithet btn-success " ng-click="ctrl.a470011()"
                            ng-disabled="mFrm.$invalid  || ctrl.IsExistPayperNum == 2">
                        إصدار  و طباعة
                    </button>

                    <button type="button" class="btn btnWithet btn-secondary " data-bs-dismiss="modal">
                        إلغاء الأمر
                    </button>

                </div>
                <div class="modal-footer  justify-content-center" ng-show=" ctrl.DlFalg == 1">
                    <button type="button" class="btn  btnWithet btn-primary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4"
                            ng-click="ctrl.a00052()" ng-disabled="mFrm.$invalid || ctrl.IsExistPayperNum == 1">
                        تعديل
                    </button>
                    <button type="button" class="btn btnWithet btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4"
                            data-bs-dismiss="modal">
                        إلغاء الأمر
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade d-print-none md-dialog-content" id="Dl_Message" tabindex="-1" data-bs-backdrop="static" aria-hidden="true" aria-labelledby="{{ctrl.Title}}">
        <div class="modal-dialog ">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ctrl.DlTitle}}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-12">
                            <h5 class="m-4 text-danger text-center col-12">{{ctrl.DlMessage}}</h5>
                            <h5 class="m-4 text-danger text-center col-12 mt-2">{{ctrl.DocNum}}</h5>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-success col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4"
                            ng-click="ctrl.a00046()" data-bs-dismiss="modal">
                        نعم
                    </button>
                    <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4"
                            data-bs-dismiss="modal">
                        لا
                    </button>
                </div>

            </div>
        </div>
    </div>


    <div class="modal fade d-print-none md-dialog-content" id="CalDoc" tabindex="-1" data-bs-backdrop="static"
         aria-labelledby="{{ ctrl.Title }}" aria-hidden="true">
        <div class="modal-dialog modal-xl d-print-none">
            <div class="modal-content d-print-none">
                <div class="modal-header">
                    <h5 class="modal-title">{{ ctrl.DlTitle }}</h5>

                    <button type="button" class="btn-close float-start" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body d-print-none" ng-show="ctrl.DlFalg == 0">
                    <form name="FrmCal" class="row g-2" autocomplete="off" novalidate>
                        <div class="col-12 d-flex justify-content-center">
                            <b class="text-start" style="font-size: 25px">بيانات الجهة</b>
                        </div>
                        <div class="col-12">
                            <label for="NameWorkPlace">اسم الجهة </label>
                            <input class="form-control col-6" name="NameWorkPlace" type="text"
                                   ng-model="ctrl.CalObj.NameWorkPlace" />
                        </div>
                        <hr class="mt-2" />
                        <div class="col-12  d-flex justify-content-center">
                            <b class="text-center" style="font-size: 25px">بيانات الوثيقة</b>
                        </div>

                        <div class="col-4">
                            <label for="Zid">النطاق الجغرافي <span class="text-danger">   * </span> </label>
                            <select required class="form-select" name="Zid"
                                    ng-options="x.ID as x.Name for x in ctrl.Objzn " ng-model="ctrl.CalObj.Zid"
                                    ng-style="mFrm.Zid.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}">
                            </select>

                        </div>
                        <div class="col-4">
                            <label for="ByearDay">    السنة </label>

                            <div class="input-group mb-3">

                                <input required name="ByearDay" type="number" ng-model="ctrl.CalObj.BDay"
                                       min="1910" max="@DateTime.Now.Year.ToString()"
                                       class="form-control text-center" ng-style="FrmCal.ByearDay.$valid ? {'border':'green solid 2px'}:{'border':'red solid 2px '}">

                            </div>


                        </div>

                        <div class=" col-4">
                            <label for="BDay"> أختر المدة <span class="text-danger"> *  </span>  </label>
                            <select class="form-select" required ng-model="ctrl.CalObj.Months" name="Months">
                                <option value="3"> 3 أشهر</option>
                                <option value="6">6 اشهر</option>
                                <option value="12">   سنة</option>
                                <option value="24"> سنتين   </option>
                                <option value="36"> ثلاث سنوات </option>
                            </select>
                            <span ng-show="FrmCal.Months.$valid" class="text-success bi-check-lg float-end m-2"></span>
                        </div>
                        <div class=" col-4">

                            <div class="row-cols-12">

                                <label for="Count"> <span class="text-danger"> *  </span>العدد </label>
                                <input required class="form-control col-6 text-center" min="1" max="10000" name="Count" type="number"
                                       ng-model="ctrl.CalObj.Count" />
                                <span ng-show="FrmCal.Count.$valid" class="text-success bi-check-lg float-end m-2"></span>
                            </div>

                        </div>
                        <div class="col-12 ">

                            <button type="button" class="btn btn-primary col-12 mt-4" ng-click="ctrl.a47007()"
                                    ng-disabled="FrmCal.$invalid">
                                إحتساب
                            </button>


                        </div>
                    </form>
                    <div class="row mt-1 d-print-none ">

                        <div class="col-12 d-flex justify-content-center d-print-none">
                            <b class="text-center" style="font-size: 25px"> قائمة الاحتساب </b>
                        </div>

                        <div class="col-12 px-4 d-print-none" ng-hide="ctrl.DocTable.length == 0">
                            <table class="table table-striped table-hover">
                                <thead class="bg-light">
                                    <tr>
                                        <th class="text-center" colspan="3">الوثيقة</th>
                                        <th class="text-center" colspan="6">الضرائب</th>
                                        <th class="text-center" colspan="1">-</th>
                                    </tr>
                                    <tr>
                                        <td class="text-center">رقم</td>

                                        <td class="text-center">الفئة العمرية</td>
                                        <td class="text-center">المدة (أشهر)</td>
                                        <td class="text-center">القيمة</td>
                                        <td class="text-center">الدمغة</td>
                                        <td class="text-center">الإشراف</td>
                                        <td class="text-center">الضريبة</td>
                                        <td class="text-center">الإصدار</td>
                                        <td class="text-center">العدد</td>
                                        <td class="text-center">الإجمالي</td>
                                    </tr>
                                </thead>
                                <tbody style="text-align: center">
                                    <tr ng-repeat="x in ctrl.DocTable ">
                                        <td class="text-center">{{ $index + 1 }}</td>
                                        <td class="text-center">{{ x.AgeDesc }}</td>
                                        <td class="text-center">{{ x.Duration }}</td>
                                        <td class="text-center">{{ x.DocVal | number: "2" }}</td>
                                        <td class="text-center">{{ x.Stamp | number: "2" }}</td>
                                        <td class="text-center">{{ x.Eshraf | number: "2" }}</td>
                                        <td class="text-center">{{ x.Tax | number: "2" }}</td>
                                        <td class="text-center">{{ x.Esdar | number: "2" }}</td>
                                        <td class="text-center">{{ x.Count | number: "" }}</td>
                                        <td class="text-center">{{ x.Totoal | number: "2" }}</td>
                                    </tr>
                                    <tr class="bg-opacity-10">
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>

                                        <td></td>
                                        <td></td>
                                        <td class="text-left bold" colspan="2">المجموع</td>
                                        <td class="text-center bold">
                                            {{ ctrl.Summ.Totoal | number: "2" }}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="modal-footer justify-content-center d-print-none " ng-show="ctrl.DlFalg == 0">
                    <button type="button" class="btn btn-success col-4 float-end" ng-click="ctrl.printTempor()"
                            ng-disabled="ctrl.DocTable.length == 0 ">
                        طباعة
                    </button>
                    <button type="button" class="btn btn-secondary col-3 float-end" data-bs-dismiss="modal">
                        خروج
                    </button>
                </div>
                <div class="modal-footer d-print-none" ng-show="ctrl.DlFalg == 1">
                    <button type="button" class="btn btn-secondary col-12" data-bs-dismiss="modal">
                        موافق
                    </button>
                </div>
            </div>
        </div>
    </div>


    <div class="modal fade" id="fullshow" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">{{ctrl.massge}}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">يرجاء ادخال البيانات من اجل البحث </label>
                            <input type="text" class="form-control" ng-model="ctrl.Search">
                        </div>

                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn  btn-success" ng-click=" ctrl.a460030()">بحث </button>
                    <button type="button" class="btn btn-danger" data-bs-dismiss="modal"> اغلاق </button>

                </div>
            </div>
        </div>
    </div>



    <div class="row d-none d-print-block " ng-show="ctrl.printflag == 4">



        <div class="row   " style="margin-top:25px;">





            <div class="col-6  ">
                <img class="float-start" ng-src="~/Imges/New folder/img.png" style="height:200px; width: 200px " />
            </div>
            <div class="col-6  ">

                <div class="card float-end" style="border:none">
                    <div class="card-img text-center">
                        <img ng-src="~/Imges/img.png" style="height: 150px; width: 200px " />

                    </div>
                    <div class="card-text text-center">
                        <small>مزود الخدمات الطبية  </small>
                    </div>

                </div>


            </div>




        </div>


        <div class="col-12  justify-content-center mt-5 mx-2">


        </div>


        <div class="col-12">
            <h1 class="col-12 text-center">فاتورة مبدئية</h1>
            <h3 class="col-12 text-start mt-3 mb-3">
                السادة  : {{ ctrl.CalObj.NameWorkPlace }}
            </h3>
        </div>
        <div class="col-12   ">
            <p style="font-size:20px ;" class="text-start">
                بعد التحية.........
            </p>
        </div>
        <div class="col-12   ">
            <p style="font-size:20px ; text-align:justify;" class="text-start  ">

                بالاشارة الى طلبكم بشان تكلفة اصدار عدد " {{ctrl.Summ.Count}}" وثائق تأمين صحي للوافدين عليه فان   <br />  <b>   اجمالي القيمة  :  {{  ctrl.Summ.Totoal }} د.ل</b> هي حسب التالي :
            </p>

        </div>

        <div class="col-12 ">

            <table class="table table-responsive table-bordered">
                <thead class="bg-light">

                    <tr>
                        <td class="text-center">ر .م </td>

                        <td class="text-center">الفئة العمرية</td>


                        <td class="text-center">العدد</td>
                        <td class="text-center"> مدة التغطية </td>
                        <td class="text-center">اجمالي قسط التأمين </td>
                    </tr>
                </thead>
                <tbody style="text-align: center">
                    <tr ng-repeat="x in ctrl.DocTable ">
                        <td class="text-center">{{ $index + 1 }}</td>
                        <td class="text-center">{{ x.AgeDesc }}</td>

                        <td class="text-center">{{  x.Count | number: "" }}</td>
                        <td class="text-center">{{ x.Duration }} شهر </td>

                        <td class="text-center">{{ x.Totoal | number: "2" }}</td>
                    </tr>
                    <tr class="">



                        <td class="text-left bold" colspan="1">اجمالي العدد </td>
                        <td class="text-center"> {{ctrl.Summ.Count}}</td>
                        <td class="text-left bold" colspan="2">المجموع</td>
                        <td class="text-center bold">{{ ctrl.Summ.Totoal | number: "2" }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class=" col-12">
            <p class="text-center">  في حالة تكرمكم بالقبول ... فنامل اتمام الاجراءات المالية ليتسنى لنا اصدار وثائق التأمين بحسب ماذكر اعلاه   <br /> شاكرين لكم تواصلكم تعاونكم معنا  </p>
            <br />

            <p class="text-center"> للاستشارات والخدمات الطبية 0918204747 </p>
        </div>
        <div class=" col-12">
            <p class="text-end mx-5 fw-bold">
                التوقيع والختم
            </p>

        </div>


    </div>
