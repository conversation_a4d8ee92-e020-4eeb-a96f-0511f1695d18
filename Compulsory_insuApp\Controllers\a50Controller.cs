﻿using Compulsory_insuApp.Models;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Mvc;

using static System.Net.Mime.MediaTypeNames;

namespace Compulsory_insuApp.Controllers
{
    public class a50Controller : Controller
    {
        Insh_AppsDBEntities db = new Insh_AppsDBEntities();
        Insur_class ic = new Insur_class();
        OrangApiAuth apiAuth = new OrangApiAuth();

        public ActionResult a50000(Guid Atu)
        {
            if (db.AgUsers.Any(c => c.AgUserID == Atu))
            {
                return PartialView("Orange_IssurancePage");
            }
            else
            {
                return View("LoginPage");
            }
        }
        public async Task<ActionResult> a500010(string CardNo, Guid UserID)
        {
            try
            {
                var us = await db.AgUsers.FindAsync(UserID);
                if (us == null)
                    return Json(new { ErrorCode = 3, ErrorMessage = "User not found." });

                var formData = new Dictionary<string, string>
                {
                    { "user_name", apiAuth.get().user_name },
                    { "pass_word", apiAuth.get().pass_word },
                    { "card_number", CardNo }
                };
                //string url = "https://testenvapi.lifo.ly/api/insurance/create"; // Replace with your API endpoint
                string url = "https://prodapi.lifo.ly/api/insurance/orangecard/printcard"; // Replace with your API endpoint
                using (var client = new HttpClient())
                {
                    var content = new FormUrlEncodedContent(formData);
                    var response = await client.PostAsync(url, content);

                    // Log the response for debugging
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var pdfBytes = await response.Content.ReadAsByteArrayAsync();
                    if (!response.IsSuccessStatusCode)
                    {
                        return Json(new { ErrorCode = 5, ErrorMessage = response.StatusCode });
                    }


                    if (pdfBytes.Length == 0)
                    {
                        return Json(new { ErrorCode = 6, ErrorMessage = "PDF generation failed." });
                    }
                    return File(pdfBytes, "application/pdf", $"{CardNo}-OrangeCard.pdf");
                }
            }
            catch(Exception ex)
            {
                return Json(new { ErrorCode = 5, ErrorMessage = "PDF generation failed." });
            }
        }

        public ActionResult a50008(Guid ID, string Note ,Guid UserID)
        {
            try
            {

                var us = db.AgUsers.Find(UserID);
                if (us == null)
                    return Json(new { ErrorCode = 3 });
                Orange_Insurance_Policy Obj = db.Orange_Insurance_Policy.Find(ID);
                Obj.IsDelProg = true;
                Obj.UpdateBy = UserID;
                Obj.UpdateDate = DateTime.Now;
                db.Orange_Insurance_Policy.Attach(Obj);
                db.Entry(Obj).State = EntityState.Modified;
                db.SaveChanges();
                Orange_poli_Nots noObj = new Orange_poli_Nots();
                noObj.ID = Guid.NewGuid();
                noObj.NoteTxt = Note;
                noObj.UserID = UserID;
                noObj.UserName = us.AgUserName;
                noObj.insertDate = DateTime.Now;
                db.Orange_poli_Nots.Add(noObj);
                db.SaveChanges();
                return Json(new { ErrorCode = 0 });

            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }





        public ActionResult a500060(Guid UserID, Guid AgencyID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                var User = db.AgUsers.Find(UserID);
                if (User == null || User.AgentId != AgencyID)
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                DateTime dt = DateTime.Now.ToUniversalTime().AddHours(2).AddDays(-7);
                var aa = db.Orange_Insurance_Policy.Where(p => p.Status != 3 && p.Insurance_Date >= dt && p.AgencyID == AgencyID).ToList();
                var Obj = aa.Select(c => new
                {
                    c.ID,
                    CardNo = c.sr_Card_No,
                    Insh_S_Date = c.insurance_day_from.Value.ToString("yyyy-MM-dd"),
                    Insh_E_Date = c.insurance_day_to.Value.ToString("yyyy-MM-dd"),
                    c.DurInDays,
                    CarName = c.OrangeCars.carName,
                    CusName = c.Insurance_Name,
                    Adrees = c.Insurance_Location,
                    Plate_No = c.Plate_Number,
                    Phone = c.Insurance_Phone,
                    InsertDate = c.Insurance_Date.Value.ToString("yyyy-MM-dd hh:mm ss"),
                    InsertBy = db.AgUsers.Find(c.InsertBy).FullName,
                    ins_val = c.Insurance_Installment,
                    TaxTotal = c.TaxTotal,
                    c.IsDelProg,
                    tot = c.Insurance_Total
                }).OrderByDescending(c => c.InsertDate).ToList();
                return Json(new { ErrorCode = 0, Obj });

            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }


        public async Task<ActionResult> a50007(Orange_InsCl MainObj)
        {

            try
            {
                var User = db.AgUsers.Find(MainObj.UsID);
                if (User == null || User.AgentId != MainObj.AgEncyID)
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                Insur_class ic = new Insur_class();
                var sn = db.Oran_SerialNums.Where(p => p.SerialNumber == MainObj.pap_no && p.StatusID == 1 && p.Oran_GivenSearils.AgencyID == MainObj.AgEncyID).ToList();
                if (!sn.Any())
                    return Json(new { ErrorCode = 4 });
                var Agen = db.Agency.Find(MainObj.AgEncyID);
                float Minp = Agen.IsAllowedDibt == false ? 0 : ((float)Agen.AllowedDibtValue * -1);
                var carobj = db.OrangeCars.Find(MainObj.carID);
                var Caluobj = db.Orange_Insurance_Clause.Find(MainObj.ins_clause_ID);
                var conObj = db.Orang_Country.Find(MainObj.Visited_Cout_ID);
                var vNatID = db.VehicleNationality.Find(MainObj.Car_Nationality_ID);
                var vContID = db.Orang_Country.Find(MainObj.Visited_Cout_ID);
                Orange_Insurance_Policy obj = new Orange_Insurance_Policy();
                obj.ID = Guid.NewGuid();
                DateTime dts;
                if (MainObj.Insh_S_Date.Date < DateTime.Now.Date)
                    return Json(new { ErrorCode = 4, ErorrMessage = "تاريخ غير مقبول" });
                else if (MainObj.Insh_S_Date.Date == DateTime.Now.Date)
                {
                    dts = new DateTime(MainObj.Insh_S_Date.Date.Year, MainObj.Insh_S_Date.Date.Month, MainObj.Insh_S_Date.Date.Day,
                        DateTime.Now.Hour, DateTime.Now.Minute, DateTime.Now.Second);
                }
                else
                {
                    dts = new DateTime(MainObj.Insh_S_Date.Date.Year, MainObj.Insh_S_Date.Date.Month, MainObj.Insh_S_Date.Date.Day, 0, 0, 0);
                }

                obj.AgencyID = (Guid)MainObj.AgEncyID;
                obj.insurance_day_from = dts;
                obj.insurance_day_to = dts.AddDays(MainObj.DurInDays);
                obj.Insurance_Date = DateTime.Now;
                obj.DurInDays = MainObj.DurInDays;
                obj.Insurance_Name = MainObj.CusName;
                obj.Insurance_Location = MainObj.Adrees;
                obj.Insurance_Phone = MainObj.Phone_num;
                obj.Car_Symbol = carobj.car_symbol;
                obj.InsuranceClauseID = MainObj.ins_clause_ID;
                obj.Insurance_Clause_symbol = Caluobj.Type;
                obj.Chassis_Number = MainObj.chassis_no;
                obj.CarID = MainObj.carID;
                obj.Plate_Number = MainObj.Plate_No;
                obj.motor_number = MainObj.motor_no;
                obj.Car_Made_Date = MainObj.CarMade_date;
                obj.Country_Symbol = conObj.CurncyName;
                obj.Visited_Country_ID = (Guid)MainObj.Visited_Cout_ID;
                obj.Vehicle_Nationality_ID = (Guid)MainObj.Car_Nationality_ID;
                obj.insurance_installment_daily = Caluobj.Type == "CV" ? 8 : Caluobj.Type == "PV" ? 7 : 0;
                obj.ContValue = conObj.CurncyName == "TUN,DZA" ? 3 : 0;
                obj.Insurance_Installment = ((obj.insurance_installment_daily ?? 0) + obj.ContValue) * (obj.DurInDays ?? 0);
                obj.Insurance_Supervision = (obj.Insurance_Installment ?? 0) * 0.005;
                float x = (float)((obj.Insurance_Installment ?? 0) * 0.01);
                obj.Insurance_Tax = ic.RoudToPoint5(x);
                obj.Insurance_Version = 10;
                obj.Insurance_Stamp = 0.25;
                float tot = (float)obj.Insurance_Installment + (float)obj.Insurance_Supervision + (float)obj.Insurance_Tax + (float)obj.Insurance_Stamp;
                obj.Insurance_Total = tot;
                obj.Insurance_Version = obj.Insurance_Version;
                obj.TaxTotal = (float)obj.Insurance_Tax + (float)obj.Insurance_Stamp + (float)obj.Insurance_Supervision + (float)obj.Insurance_Version;
                obj.IsAccepted = false;
                obj.InsertBy = MainObj.UsID;
                obj.Ins_num = MainObj.pap_no;
                obj.Status = 1;
                obj.insurance_country_number = conObj.CountryID == Guid.Parse("F42F6690-4E98-4F07-B700-74EA1487A4F7") ? 2 : 1;
                obj.AccptedText = MainObj.ReqText;
                db.Orange_Insurance_Policy.Add(obj);
                var formData = new Dictionary<string, string>
                {
                    { "user_name" ,apiAuth.get().user_name},
                    { "pass_word" ,apiAuth.get().pass_word },
                    { "insurance_day_from" , obj.insurance_day_from.Value.ToString("yyyy-MM-dd") },
                    { "insurance_name" , obj.Insurance_Name },
                    { "insurance_location" , obj.Insurance_Location },
                    { "vehicle_nationalities_id" , vNatID.AlternetKy.ToString() },
                    { "insurance_phone", obj.Insurance_Phone },
                    { "chassis_number", obj.Chassis_Number },
                    { "insurance_clauses_id", Caluobj.AlternetKy.ToString() },
                    { "car_made_date", obj.Car_Made_Date.ToString() },
                    { "plate_number" , obj.Plate_Number },
                    { "cars_id" , carobj.AlternetKy.ToString() },
                     {"motor_number", obj.motor_number },
                    { "insurance_country_number", obj.insurance_country_number.ToString() },
                    { "countries_id", vContID.AlterntKey.ToString() },
                    { "insurance_days_number", obj.DurInDays.ToString() },

                };
                string url = "https://prodapi.lifo.ly/api/insurance/create"; // Replace with your API endpoint
                //string url = "https://testenvapi.lifo.ly/api/insurance/create"; // Replace with your API endpoint

                using (var client = new HttpClient())
                {

                    var content = new FormUrlEncodedContent(formData);
                    var response = await client.PostAsync(url, content);
                    var responseString = await response.Content.ReadAsStringAsync();
                    if (!response.IsSuccessStatusCode)
                        return Json(new { ErrorCode = 5, ErorrMessage = response.StatusCode });
                    var jsonResponse = JObject.Parse(responseString);

                    if ((bool)jsonResponse["status"])
                    {
                        obj.IsAccepted = true;
                        obj.sr_Card_No = jsonResponse["policyNumber"].ToString();
                        db.SaveChanges();
                        //OrangDoc_Cl orangDoc_Cl = new OrangDoc_Cl();
                        //var Doc = orangDoc_Cl.OrangDoc_print(obj);
                        return Json(new { ErrorCode = 0, CardNo= obj.sr_Card_No });
                    }
                    else
                    {

                        return Json(new { ErrorCode = 5, ErorrMessage = jsonResponse["message"].ToString() });
                    }

                }


                // Orange_InsCl doccl = new Orange_InsCl();
                // var doc =doccl.(obj);


            }
            catch (Exception  ex)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " ,WW=ex.Message});
            }
        }

     
        public ActionResult a50004(Guid UserID, Guid AgencyID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                CompInsuTypes CompLi = new CompInsuTypes();
                var User = db.AgUsers.Find(UserID);
                if (User == null || User.AgentId != AgencyID)
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                var LyCarNatId = Guid.Parse("83140edd-1a17-4f1d-8305-9ecfbbae8bd4"); 
                var CarsObj = db.OrangeCars.Where(c => c.Status == 1).Select(c => new {
                    ID = c.carId,
                    Name = c.carName,
                }).OrderBy(c => c.Name).ToList();
                var CarsNatObj = db.VehicleNationality.Where(c => c.Status == 1 && c.vehicle_nationality_id ==LyCarNatId).Select(c => new {
                    ID = c.vehicle_nationality_id,
                    Name = c.vehicle_nationality_name,
                }).OrderBy(c => c.Name).ToList();
           
                var VistedConObj = db.Orang_Country.Where(c => c.Status == 1).Select(c => new {
                    ID = new {Key=c.CountryID ,Value = c.CurncyName == "TUN,DZA" ? 3: 0 ,Count= c.CurncyName == "TUN,DZA" ? 2 : 1 },
                    Name = c.CountryName,
                }).OrderBy(c => c.Name).ToList();
                var CluObj = db.Orange_Insurance_Clause.Where(c => c.Status == 1).Select(c => new {
                    ID = new { key=c.ID, Value = c.Type == "CV" ? 8 : c.Type == "PV" ? 7:0  , c.Type},
                    Name = c.Slug,
                }).OrderBy(c => c.Name).ToList();
                List<int> Years = new List<int>();
                for (int i = DateTime.Now.ToUniversalTime().AddHours(2).Year - 90; i <= DateTime.Now.ToUniversalTime().AddHours(2).Year + 1; i++)
                    Years.Add(i);


                return Json(new { ErrorCode = 0,  CarsObj, Years , CarsNatObj ,VistedConObj   , CluObj });

            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
    }
}