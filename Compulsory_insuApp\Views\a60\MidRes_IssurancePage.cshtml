@{
    ViewBag.Title = "MidRes_IssurancePage";
}

<link href="~/Content/CSS/insurance-form-styles.css?v=1.0.0.1" rel="stylesheet" />
<link href="~/Content/CSS/bootstrap.rtl.min.css?v=1.0.0.1" rel="stylesheet" />
<link href="~/Content/InssunaceStyle.css?v=1.0.0.2" rel="stylesheet" media="screen" />
<link href="~/Content/PrintRPt.css?v=1.0.0.4" rel="stylesheet" media="print" />
<link href="~/Content/Cairo.css" rel="stylesheet" />
<link href="~/Content/CSS/all.min.css" rel="stylesheet" />


<div class="row justify-content-center d-print-none  " style="background-color:white">

    <div class="col-lg-12   col-md-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12" ng-if="ctrl.ShowFlag == 0">
        <div class="card  navglassColor ">
            <div class="card-header" >

                <div class="btn-group cairo" dir="ltr" role="group">
                    <button type="button" disabled class="btn btn-primary btn-danger border-ridues  cairo "> واجهة إصدار وثيقة تامين برتقالية</button>
                    <button type="button" ng-click="ctrl.BtnHomePage()" class="btn btn-success bi bi-house-fill"><span class="m-2">الرئيسية</span></button>
                </div>
                <div class="col-auto float-end">
                    <button type="button"
                            class="btn btn-danger border-ridues  cairo " 
                            data-bs-toggle="modal" data-bs-target="#NewItem" ng-click="ctrl.clearMainObj()">
                        إصدار وثيقة جديدة
                    </button>
                </div>
            </div>
        </div>

        <div class=" gradientModelBackg " style="background-color:aliceblue">


            <div class="row  ">
                <div class="col-sm-12 col-xs-12  offset-1 col-md-5 col-lg-5 col-xl-5 col-xxl-5">

                    <input class="inputStyle cairo " type="text" ng-model="search" placeholder="   بحث...   ">

                </div>
                <div class="col-sm-12 col-xs-12 col-md-3 col-lg-3 col-xl-3 col-xxl-3  ">

                    

                </div>


            </div>

            <div class="row  mt-4 " style="height:80vh; padding:25px;overflow-y:scroll">
                <div class="table-responsive cairo ">
                    <table class="table     table-hover">
                        <tr class="  text-white" style="background-color:red">
                            <th class="text-center">#</th>
                            <th class="text-center">رقم الوثيقة</th>
                            <th class="text-center"> العميل </th>
                            <th class="text-center">  الوكيل </th>
                            <th class="text-center">العلاقة </th>
                            <th class="text-center"> تامين من يوم </th>
                            <th class="text-center"> تامين الى يوم </th>
                            <th class="text-center">  القسط  </th>
                            <th class="text-center">  الضرائب  </th>
                            <th class="text-center">  الإجمالي  </th>
                            <th class="text-center"> المصدر  </th>
                            <th class="text-center"> التاريخ  </th>
                            <th class="text-center" style="margin:0" colspan="3">العمليات</th>
                        </tr>
                        <tr class="tableStaylebody p-0 text-black" ng-repeat="x in ctrl.InsObj |filter:search ">
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{$index + 1}}</td>

                            <td class="text-center " dir="ltr" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">
                                {{x.CardNo}}
                            </td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.CustName}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.DealerName}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.Deal_relation}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.SDate}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.EDate}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.ins_val| number:'2'}}</td>

                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.TaxTotal | number:'2'}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.tot | number:'2'}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.InsertBy}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.InsertDate |date : "yyyy-MM-dd  a hh:mm" }}</td>

                            <td ng-hide="x.IsDelProg">
                                <button title="معاينة الوثيقة" type="button" class="btn btn-dark" ng-click="ctrl.previewDoc(x)">
                                    <i class="bi bi-eye-fill"></i>
                                </button>
                            </td>

                            <td ng-hide="x.IsDelProg">
                                <button title="طباعة الوثيقة" type="button" class="btn btn-primary" ng-click="ctrl.printDoc(x)">
                                    <i class="bi bi-printer-fill"></i>
                                </button>
                            </td>

                            <td ng-hide="x.IsDelProg">
                                <button title="حذف الوثيقة" type="button" class="btn btn-danger" ng-click="ctrl.BtnStatus(x)">
                                    <i class="bi bi-trash-fill" aria-hidden="true"></i>
                                </button>
                            </td>

                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="col-12 d-print-none" ng-if="ctrl.ShowFlag == 1">
        <button class="btn btn-primary m-3" ng-click="ctrl.ShowFlag = 0">رجوع</button>

        <table class="table mt-4">
            <tr>
                <td class="border border-1 border-dark margan-top" style="height:4cm !important;width:8cm!important;border:medium ;border-color:black ; font-family:'Times New Roman'; font-size:16px !important ">
                    <br> المكتب الموحد    :   المكتب الموحد الليبي
                    <br> العنوان : شارع جمال القاسمي بجانب جامع إمبارك باب بن غشير
                    <br> صندوق البريد : ميدان الجزائر 4784
                    <br> +الهاتف : 218213632518
                    <br> +الفاكس: 218213602571
                    <br> البريد الإلكتروني : <EMAIL>


                </td>
                <td class="col-auto justify-content-center">
                
                    <qrcode data="{{ctrl.var}}" size="200" href="http://example.com" style="margin-right:80px;"></qrcode>
                    <div class="border border-2 text-center ">
                        <label>رمز التحقق من صحة الوثيقة </label>
                        <p class="border-1 border-dark text-center">
                            " {{ctrl.Doc.CardNo}}"
                        </p>
                    </div>
                </td>
                <td class="border border-1 border-dark" style="height: 4cm !important; width: 8cm !important; border-color: black; font-family: 'Times New Roman'; font-size: 16px !important">
                    <br> الشركة المصدرة للبطاقة    :   الشركة الإتحادية للتأمين
                    <br> العنوان : شارع الزاوية - طرابلس - ليبيا.
                    <br> صندوق البريد :
                    <br> الهاتف : 00218217100022
                    <br> الفاكس:
                    <br> البريد الإلكتروني :
                </td>
            </tr>

        </table>
        <table class="table mt-2">
            <tr>
                <td class="border border-1 border-dark mt-2 text-center" style="width: 8.5cm !important; border: medium; border-color: black; border-left: unset !important">
                    اسم المؤمن : {{ctrl.Doc.Cus_Name}}
                </td>
                <td class="col-auto border-dark border border-1 mt-2 text-center" style="border-left: unset !important; border-right: unset !important; border-top: unset !important ">
                    العنوان : {{ctrl.Doc.Cus_adress}}
                </td>
                <td class="border border-1 border-dark mt-2 text-center" style="width:8.5cm!important;border:medium ;border-color:black;border-right:unset!important">
                    الهاتف :  {{ctrl.Doc.Cus_Phone}}
                </td>
            </tr>
        </table>
        <table class="table mt-2">
            <tr>
                <td class=" col-3 border border-1 border-dark mt-2 text-center" style="width: 4cm !important; border: medium">
                    نوع المركبة :
                </td>
                <td class=" col-3 border border-1 border-dark mt-2 text-center" style="width: 0.5cm !important; border: medium">
                    :
                </td>
                <td class=" col-3 border border-1 border-dark mt-2 text-center" style="width: 7.5cm !important; border: medium">
                    {{ctrl.Doc.Via_Type}}
                </td>
                <td class=" col-3 border border-1 border-dark mt-2 text-center" style="width: 4cm !important; border: medium">
                    جنسية المركبة :
                </td>
                <td class=" col-3 border border-1 border-dark mt-2 text-center" style="width: 0.5cm !important; border: medium">
                    :
                </td>
                <td class=" col-3 border border-1 border-dark mt-2 text-center" style="width: 7.5cm !important; border: medium">
                    {{ctrl.Doc.Via_nat}}
                </td>
            </tr>
            <tr>
                <td class="border-dark border border-1 mt-2 text-center" style="width: 4cm !important; border: medium">
                    سنة الصنع :
                </td>
                <td class=" col-3 border border-1 border-dark mt-2 text-center" style="width: 0.5cm !important; border: medium">
                    :
                </td>
                <td class="border-dark border border-1 mt-2 text-center" style="width: 7.5cm !important; border: medium">
                    {{ctrl.Doc.Via_Made_Year}}
                </td>
                <td class="border-dark border border-1 mt-2 text-center" style="width: 4cm !important; border: medium">
                    رقم الهيكل (الشاسية) :
                </td>
                <td class=" col-3 border border-1 border-dark mt-2 text-center" style="width: 0.5cm !important; border: medium">
                    :
                </td>
                <td class="border-dark border border-1 mt-2 text-center" style="width: 7.5cm !important; border: medium">
                    {{ctrl.Doc.Chas_no}}
                </td>
            </tr>
            <tr>
                <td class="border border-1 border-dark mt-2 text-center" style="width:4cm!important;border:medium ;border-color:black">
                    رقم اللوحة :
                </td>
                <td class="border border-1 border-dark mt-2 text-center" style="width:0.5cm!important;border:medium ;border-color:black">
                    :
                </td>
                <td class="border border-1 border-dark mt-2 text-center" style="width:7.5cm!important;border:medium ;border-color:black">
                    {{ctrl.Doc.Pa_no}}
                </td>
                <td class="border border-1 border-dark mt-2 text-center" style="width:4cm!important;border:medium ;border-color:black">
                    رقم المحرك :
                </td>
                <td class="border border-1 border-dark mt-2 text-center" style="width:0.5cm!important;border:medium ;border-color:black">
                    :
                </td>
                <td class="border border-1 border-dark mt-2 text-center" style="width:7.5cm!important;border:medium ;border-color:black">
                    {{ctrl.Doc.Motor_No}}
                </td>
            </tr>
            <tr>
                <td class="border border-1 border-dark mt-2 text-center" style="width:4cm!important;border:medium ;border-color:black">
                    الغرض من الإستعمال :
                </td>
                <td class="border border-1 border-dark mt-2 text-center" style="width:0.5cm!important;border:medium ;border-color:black">
                    :
                </td>
                <td class="border border-1 border-dark mt-2 text-center" style="width:7.5cm!important;border:medium ;border-color:black">
                    {{ctrl.Doc.UseType}}
                </td>
                <td class="border border-1 border-dark mt-2 text-center" style="width:4cm!important;border:medium ;border-color:black">
                </td>
                <td class="border border-1 border-dark mt-2 text-center" style="width:0.5cm!important;border:medium ;border-color:black">
                    :
                </td>
                <td class="border border-1 border-dark mt-2 text-center" style="width:7.5cm!important;border:medium ;border-color:black">
                </td>
            </tr>
        </table>
        <table class="table mt-2" style="height:1.5cm">
            <tr>
                <td class="border border-1 border-dark mt-2 text-center" style="width: 4.5cm !important; border: medium; border-color: black; border-left-color:white !important">
                    سريان التأمين :
                </td>

                <td class="border border-1 border-dark mt-2 text-center" style="width: 4.5cm !important; border: medium; border-color: black; border-left-color:white !important">
                    <div class="text-black" style="text-align:inherit">
                        من الساعة : {{ctrl.Doc.F_Time}}
                    </div>
                    <div class="text">
                        إلى الساعة : {{ctrl.Doc.To_Time}}
                    </div>
                </td>
                <td class="border border-1 border-dark mt-2 text-center" style="width: 4.5cm !important; border: medium; border-color: black; border-left-color: white !important">
                    <div class="text-black" style="text-align:inherit">
                        من يوم : {{ctrl.Doc.F_date}}
                    </div>
                    <div class="text">
                        إلى يوم : {{ctrl.Doc.To_Date}}
                    </div>
                </td>
                <td class="border border-1 border-dark mt-2 text-center" style="width: 4.5cm !important; border: medium; border-color: black">
                    <div class="text-black" style="text-align:inherit">
                        الموافق : {{ctrl.Doc.F_DayOfweek}}
                    </div>
                    <div class="text">
                        الموافق : {{ctrl.Doc.To_DayOfweek}}
                    </div>
                </td>
            </tr>

        </table>
        <table class="table mt-1 pt-2 text-center" style="width:18cm">
            <tr>
                <td class="border border-1 border-white mt-2 text-center" style="width: 3.5cm !important; border: medium; border-color: black; border-left-color: white !important">
                    تحرر في يوم: {{ctrl.Doc.Ins_Day}}
                </td>
                <td class="border border-1 border-white mt-2 text-center" style="width: 3cm !important; border: medium; border-color: black; border-left-color: white !important">
                    الموافق : {{ctrl.Doc.Ins_DayOfWeek}}
                </td>
                <td class="border border-1 border-white mt-2 text-center" style="width: 3cm !important; border: medium; border-color: black; border-left-color: white !important">
                    من شهر : {{ctrl.Doc.Ins_ArMonthName}}
                </td>
                <td class="border border-1 border-white mt-2 text-center" style="width: 3cm !important; border: medium; border-color: black; border-left-color: white !important">
                    سنة : {{ctrl.Doc.Ins_Year}}
                </td>
            </tr>

        </table>
    </div>
</div>






<div class="modal fade cairo d-print-none" id="NewItem" tabindex="-1" data-bs-backdrop="static" aria-labelledby="{{ctrl.Title}}" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content gradentModelGray">
            <div class="modal-header" style="background-color:red">
                <h5 class="modal-title text-white" id="exampleModalLabel">إضافة وثيقة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body cairo " style="background-color:red">
                <form name="mForm" autocomplete="off" novalidate ng-submit="ctrl.saveMainObj()" ng-form-commit>
                    <div class="row">
                        <div class="col-8">
                            <!-- بيانات التأمين الأساسية -->
                            <fieldset class="form-section">
                                <legend class="section-title">
                                    <i class="bi bi-shield-fill"></i>
                                    بيانات التأمين الأساسية
                                </legend>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group" ng-class="{'has-error': mForm.Date_From.$invalid && (mForm.Date_From.$dirty || mForm.Date_From.$touched)}">
                                            <label class="required" for="Date_From">تاريخ البداية</label>
                                            <div class="input-group" ng-class="{'is-invalid': mForm.Date_From.$invalid && (mForm.Date_From.$dirty || mForm.Date_From.$touched), 'is-valid': mForm.Date_From.$valid && (mForm.Date_From.$dirty || mForm.Date_From.$touched)}">
                                                <span class="input-group-text"><i class="bi bi-calendar-date"></i></span>
                                                <input type="date" id="Date_From" name="Date_From" class="form-control" 
                                                       ng-model="ctrl.MainObj.Date_From" 
                                                       ng-change="ctrl.CalEndDate()" 
                                                       required>
                                            </div>
                                            <div class="validation-message" ng-show="mForm.Date_From.$error.required && (mForm.Date_From.$dirty || mForm.Date_From.$touched)">
                                                يجب إدخال تاريخ البداية
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group" ng-class="{'has-error': mForm.Duration.$invalid && (mForm.Duration.$dirty || mForm.Duration.$touched)}">
                                            <label class="required" for="Duration">المدة</label>
                                            <div class="input-group" ng-class="{'is-invalid': mForm.Duration.$invalid && (mForm.Duration.$dirty || mForm.Duration.$touched), 'is-valid': mForm.Duration.$valid && (mForm.Duration.$dirty || mForm.Duration.$touched)}">
                                                <span class="input-group-text"><i class="bi bi-clock"></i></span>
                                                <input type="number" id="Duration" name="Duration" class="form-control" 
                                                       ng-model="ctrl.MainObj.Duration" 
                                                       ng-change="ctrl.CalEndDate()" 
                                                       required min="1">
                                            </div>
                                            <div class="validation-message" ng-show="mForm.Duration.$error.required && (mForm.Duration.$dirty || mForm.Duration.$touched)">
                                                يجب إدخال المدة
                                            </div>
                                            <div class="validation-message" ng-show="mForm.Duration.$error.min && (mForm.Duration.$dirty || mForm.Duration.$touched)">
                                                يجب أن تكون المدة أكبر من صفر
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="Date_To">تاريخ النهاية</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-calendar-check"></i></span>
                                                <input type="date" id="Date_To" class="form-control" 
                                                       ng-model="ctrl.MainObj.Date_To" disabled>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>

                            <!-- بيانات العميل -->
                            <fieldset class="form-section">
                                <legend class="section-title">
                                    <i class="bi bi-person-fill"></i>
                                    بيانات العميل
                                </legend>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group" ng-class="{'has-error': mForm.CustomerName.$invalid && (mForm.CustomerName.$dirty || mForm.CustomerName.$touched)}">
                                            <label class="required" for="CustomerName">اسم العميل</label>
                                            <div class="input-group" ng-class="{'is-invalid': mForm.CustomerName.$invalid && (mForm.CustomerName.$dirty || mForm.CustomerName.$touched), 'is-valid': mForm.CustomerName.$valid && (mForm.CustomerName.$dirty || mForm.CustomerName.$touched)}">
                                                <span class="input-group-text"><i class="bi bi-person"></i></span>
                                                <input type="text" id="CustomerName" name="CustomerName" class="form-control" 
                                                       ng-model="ctrl.MainObj.CustomerName" 
                                                       required 
                                                       ng-minlength="3">
                                            </div>
                                            <div class="validation-message" ng-show="mForm.CustomerName.$error.required && (mForm.CustomerName.$dirty || mForm.CustomerName.$touched)">
                                                يجب إدخال اسم العميل
                                            </div>
                                            <div class="validation-message" ng-show="mForm.CustomerName.$error.minlength && (mForm.CustomerName.$dirty || mForm.CustomerName.$touched)">
                                                يجب أن يكون اسم العميل 3 أحرف على الأقل
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="required" for="DealerName">اسم الوكيل</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-person-badge"></i></span>
                                                <input type="text" id="DealerName" name="DealerName" class="form-control" 
                                                       ng-model="ctrl.MainObj.DealerName" required>
                                            </div>
                                            <div class="validation-message" ng-show="mForm.DealerName.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="required" for="DealerCustomerReleation">علاقة الوكيل بالعميل</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-people"></i></span>
                                                <input type="text" id="DealerCustomerReleation" name="DealerCustomerReleation" 
                                                       class="form-control" ng-model="ctrl.MainObj.DealerCustomerReleation" required>
                                            </div>
                                            <div class="validation-message" ng-show="mForm.DealerCustomerReleation.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>
                                    <!-- حقل الجنسية -->
                                    <div class="col-md-6">
                                        <div class="form-group" ng-class="{'has-error': mForm.NationalityID.$invalid && (mForm.NationalityID.$dirty || mForm.NationalityID.$touched)}">
                                            <label class="required" for="NationalityID">الجنسية</label>
                                            <md-autocomplete id="NationalityID"
                                                             md-selected-item="ctrl.NationalityID"
                                                             md-require-match="true"
                                                             md-search-text="ctrl.TxtselectedNationality"
                                                             md-items="item in ctrl.searchNationalities(ctrl.TxtselectedNationality)"
                                                             md-item-text="item.Name"
                                                             md-min-length="0"
                                                             md-selected-item-change="ctrl.SelectedNationalityChange()"
                                                             placeholder="اختر  عنوان"
                                                             ng-required="!ctrl.MainObj.IsSalary">
                                                <md-item-template>
                                                    <span>{{item.Name}}</span>
                                                </md-item-template>
                                            </md-autocomplete>
                                            <div class="validation-message" ng-show="mForm.NationalityID.$error.required && (mForm.NationalityID.$dirty || mForm.NationalityID.$touched)">
                                                يجب اختيار الجنسية
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>

                            <!-- البيانات الشخصية -->
                            <fieldset class="form-section">
                                <legend class="section-title">
                                    <i class="bi bi-person-vcard"></i>
                                    البيانات الشخصية
                                </legend>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="required" for="BirthDate">سنة الميلاد</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-calendar2-event"></i></span>
                                                <input type="number" id="BirthDate" name="BirthDate" class="form-control" 
                                                       ng-model="ctrl.MainObj.BirthDate" required min="1900" max="2100" step="1">
                                            </div>
                                            <div class="validation-message" ng-show="mForm.BirthDate.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="required" for="BirthPlace">مكان الميلاد</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-geo-alt"></i></span>
                                                <input type="text" id="BirthPlace" name="BirthPlace" class="form-control" 
                                                       ng-model="ctrl.MainObj.BirthPlace" required>
                                            </div>
                                            <div class="validation-message" ng-show="mForm.BirthPlace.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="required" for="AddressID">العنوان</label>
                                  
                                            <md-autocomplete id="AddressID"
                                                             md-selected-item="ctrl.AddressID"
                                                             md-require-match="true"
                                                             md-search-text="ctrl.TxtSearchAddresses"
                                                             md-items="item in ctrl.searchAddresses(ctrl.TxtSearchAddresses)"
                                                             md-item-text="item.Name"
                                                             md-min-length="0"
                                                             md-selected-item-change="ctrl.SelectedAddressesChange()"
                                                             placeholder="اختر  عنوان"
                                                             ng-required="!ctrl.MainObj.IsSalary">
                                                <md-item-template>
                                                    <span>{{item.Name}}</span>
                                                </md-item-template>
                                            </md-autocomplete>
                                            <div class="validation-message" ng-show="mForm.AddressID.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="required" for="MaritalStatusID">الحالة الاجتماعية</label>
                          
                                            <md-autocomplete id="MaritalStatusID"
                                                             md-selected-item="ctrl.MaritalStatusID"
                                                             md-require-match="true"
                                                             md-search-text="ctrl.TXtsearchMaritalStatuses"
                                                             md-items="item in ctrl.MaritalStatuseslist(ctrl.TXtsearchMaritalStatuses)"
                                                             md-item-text="item.Name"
                                                             md-min-length="0"
                                                             md-selected-item-change="ctrl.SelectedMaritalStatusesChange()"
                                                             placeholder="اختر الحالة الاجتماعية"
                                                             ng-required="!ctrl.MainObj.IsSalary">
                                                <md-item-template>
                                                    <span>{{item.Name}}</span>
                                                </md-item-template>
                                            </md-autocomplete>
                                            <div class="validation-message" ng-show="mForm.ProfessionID.$error.required">هذا الحقل مطلوب</div>
                                            <div class="validation-message" ng-show="mForm.MaritalStatusID.$error.required && (mForm.MaritalStatusID.$dirty || mForm.MaritalStatusID.$touched)">
                                                يجب اختيار الحالة الاجتماعية
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>

                            <!-- البيانات المهنية والمالية -->
                            <fieldset class="form-section">
                                <legend class="section-title">
                                    <i class="bi bi-briefcase-fill"></i>
                                    البيانات المهنية والمالية
                                </legend>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="required" for="IsSalary">طريقة احتساب القسط</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-calculator"></i></span>
                                                <select id="IsSalary" name="IsSalary" class="form-select" 
                                                        ng-model="ctrl.IsSalary" 
                                                        ng-change="ctrl.SelectedIsSalaryChanged()" required>
                                                    <option value="1">مرتب</option>
                                                    <option value="0">مؤهل علمي</option>
                                                </select>
                                            </div>
                                            <div class="validation-message" ng-show="mForm.IsSalary.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="required" for="ProfessionID">المهنة</label>
                                            <md-autocomplete id="ProfessionID"
                                                md-selected-item="ctrl.ProfessionID"
                                                md-require-match="true"
                                                md-search-text="ctrl.TxtSearchProffistion"
                                                md-items="item in ctrl.searchProfessions(ctrl.TxtSearchProffistion)"
                                                md-item-text="item.Name"
                                                md-min-length="0"
                                                md-selected-item-change="ctrl.SelectedProffistionChange()"
                                                placeholder="اختر المهنة"
                                                required>
                                                <md-item-template>
                                                    <span>{{item.Name}}</span>
                                                </md-item-template>
                                            </md-autocomplete>
                                            <div class="validation-message" ng-show="mForm.ProfessionID.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- حقول مشروطة بناءً على طريقة الاحتساب -->
                                <div class="row mt-3" ng-if="ctrl.MainObj.IsSalary">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="required" for="Salary">قيمة المرتب</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-cash"></i></span>
                                                <input type="number" id="Salary" name="Salary" class="form-control" 
                                                       ng-model="ctrl.MainObj.Salary" 
                                                       ng-change="ctrl.St_CalIns()" required>
                                            </div>
                                            <div class="validation-message" ng-show="mForm.Salary.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>

                                    <div class="col-md-6" ng-if="ctrl.MainObj.IsFreePer">
                                        <div class="form-group">
                                            <label class="required" for="Percentage">النسبة المئوية</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-percent"></i></span>
                                                <input   type="number" id="Percentage" name="Percentage" class="form-control" 
                                                       ng-model="ctrl.MainObj.Percentage" 
                                                       ng-change="ctrl.St_CalIns()" ng-required="ctrl.MainObj.IsFreePer">
                                            </div>
                                            <div class="validation-message" ng-show="mForm.Percentage.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>

                                    <div class="col-md-6" ng-if="!ctrl.MainObj.IsFreePer">
                                        <div class="form-group">
                                            <label class="required" for="PerValue">النسبة</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-percent"></i></span>
                                                <select id="PerValue" name="PerValue" class="form-select"  
                                                        ng-options="x.Value as x.Name for x in ctrl.PercentageList"
                                                        ng-model="ctrl.PerValue" 
                                                        ng-change="ctrl.SelectedPerValueChanged()" ng-required="!ctrl.MainObj.IsFreePer">
                                                </select>
                                            </div>
                                            <div class="validation-message" ng-show="mForm.PerValue.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mt-3" ng-if="!ctrl.MainObj.IsSalary">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="required" for="AcademicQualificationID">المؤهل العلمي</label>
                                      
                                            <md-autocomplete id="AcademicQualificationID"
                                                             md-selected-item="ctrl.AcademicQualificationID"
                                                             md-require-match="true"
                                                             md-search-text="ctrl.TxtSearchAcademicQualification"
                                                             md-items="item in ctrl.searchAcademicQualifications(ctrl.TxtSearchAcademicQualification)"
                                                             md-item-text="item.Name"
                                                             md-min-length="0"
                                                             md-selected-item-change="ctrl.SelectedAcademicQualificationChange()"
                                                             placeholder="اختر  المؤهل العلمي"
                                                             ng-required="!ctrl.MainObj.IsSalary">
                                                <md-item-template>
                                                    <span>{{item.Name}}</span>
                                                </md-item-template>
                                            </md-autocomplete>
                                            <div class="validation-message" ng-show="mForm.AcademicQualificationID.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>

                            <!-- بيانات المطبوعة -->
                            <fieldset class="form-section">
                                <legend class="section-title">
                                    <i class="bi bi-file-text"></i>
                                    بيانات المطبوعة
                                </legend>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="required" for="pap_no">رقم المطبوعة</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-upc"></i></span>
                                                <input type="number" id="pap_no" name="pap_no" class="form-control text-center" 
                                                       ng-model="ctrl.MainObj.pap_no" 
                                                       ng-click="ctrl.IsExistPayperNum = 0"
                                                       min="0" required>
                                            </div>
                                            <div class="validation-message" ng-show="mForm.pap_no.$error.required">هذا الحقل مطلوب</div>
                                            <div class="text-danger mt-2" ng-if="ctrl.IsExistPayperNum == 1">
                                                الرقم غير صحيح
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>
                        </div>

                        <!-- جانب احتساب القسط -->
                        <div class="col-4">
                            <div class="calculation-panel">
                                <div class="calculation-header">
                                    <i class="bi bi-calculator-fill"></i>
                                    احتساب القسط
                                </div>
                                
                                <div class="calculation-items">
                                    <div class="calc-item">
                                        <div class="calc-label">صافي القسط</div>
                                        <div class="calc-value">{{ctrl.MainObj.installment | number:'2'}}</div>
                                    </div>

                                    <div class="calc-item">
                                        <div class="calc-label">الدمغة</div>
                                        <div class="calc-value">{{ctrl.MainObj.Stamp | number:'2'}}</div>
                                    </div>

                                    <div class="calc-item">
                                        <div class="calc-label">الضريبة</div>
                                        <div class="calc-value">{{ctrl.MainObj.Tax | number:'2'}}</div>
                                    </div>

                                    <div class="calc-item">
                                        <div class="calc-label">رسوم إشراف</div>
                                        <div class="calc-value">{{ctrl.MainObj.Supervision | number:'2'}}</div>
                                    </div>

                                    <div class="calc-item">
                                        <div class="calc-label">م.الإصدار</div>
                                        <div class="calc-value">{{ctrl.MainObj.Version | number:'2'}}</div>
                                    </div>

                                    <div class="calc-total">
                                        <div class="total-label">المجموع</div>
                                        <div class="total-value">{{ctrl.MainObj.Total | number:'2'}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer justify-content-center" style="background-color:red">
                <button type="button" class="btn btn-success col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" ng-disabled="mForm.$invalid" ng-click="ctrl.a600015()">
                    حفظ
                </button>
                <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">إلغاء الأمر</button>
            </div>
            @*<div class="modal-footer justify-content-center" ng-if="ctrl.showhFlag==2">
                    <button type="button" class="btn btn-success col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" ng-disabled="mForm.$invalid" ng-click="ctrl.a40008()">
                        حفظ كجديد
                    </button>
                    <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">إلغاء الأمر</button>
                </div>*@
        </div>
    </div>
</div>



<!--<div class="modal fade cairo d-print-none" id="NewItem" tabindex="-1" data-bs-backdrop="static" aria-labelledby="{{ctrl.Title}}" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary">
                <h5 class="modal-title text-white">
                    <i class="bi bi-plus-circle-fill me-2"></i>
                    إضافة وثيقة جديدة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                <form name="mForm" autocomplete="off" novalidate>
                    <div class="row">-->
                        <!-- Main Insurance Data Section -->
                        <!--<div class="col-lg-9 col-md-8">-->
                            <!-- Insurance Details Card -->
                            <!--<div class="card shadow-sm mb-4">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">
                                        <i class="bi bi-shield-fill me-2"></i>
                                        بيانات التأمين
                                    </h6>
                        </div>
                                <div class="card-body">
                                    <div class="row g-3">-->
                                        <!-- Date Fields -->
                                        <!--<div class="col-md-4">
                                            <label class="form-label">تاريخ البداية <span class="text-danger" ng-show="mForm.Date_From.$error.required">*</span></label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-calendar-date"></i></span>
                                                <input type="date" name="Date_From" class="form-control" ng-change="ctrl.CalEndDate()" ng-model="ctrl.MainObj.Date_From" required>
                        </div>
                    </div>
                                        <div class="col-md-4">
                                            <label class="form-label">المدة <span class="text-danger" ng-show="mForm.Duration.$error.required">*</span></label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-clock"></i></span>
                                                <input name="Duration" type="number" class="form-control" ng-change="ctrl.CalEndDate()" ng-model="ctrl.MainObj.Duration" required>
                </div>
                </div>
                                        <div class="col-md-4">
                                            <label class="form-label">تاريخ النهاية</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-calendar-check"></i></span>
                                                <input disabled type="date" class="form-control" ng-model="ctrl.MainObj.Date_To">
        </div>
    </div>
</div>
    </div>
                            </div>-->

                            <!-- Customer Information Card -->
                            <!--<div class="card shadow-sm mb-4">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">
                                        <i class="bi bi-person-fill me-2"></i>
                                        بيانات العميل
                                    </h6>
    </div>
                                <div class="card-body">
                                    <div class="row g-3">-->
                                        <!-- Customer Basic Info -->
                                        <!--<div class="col-md-6">
                                            <label class="form-label">اسم العميل <span class="text-danger" ng-show="mForm.CustomerName.$error.required">*</span></label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-person"></i></span>
                                                <input type="text" class="form-control" ng-model="ctrl.MainObj.CustomerName" required>
    </div>
                </div>
                                        <div class="col-md-6">
                                            <label class="form-label">اسم الوكيل <span class="text-danger" ng-show="mForm.DealerName.$error.required">*</span></label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-person-badge"></i></span>
                                                <input type="text" class="form-control" ng-model="ctrl.MainObj.DealerName" required>
                                            </div>
                                        </div>-->

                                        <!-- Personal Information -->
                                        <!--<div class="col-md-4">
                                            <label class="form-label">الجنسية <span class="text-danger" ng-show="mForm.NationalityID.$error.required">*</span></label>
                                            <md-autocomplete class="modern-autocomplete"
                                                md-require-match="true"
                                                md-search-text="ctrl.searchNationality"
                                                md-selected-item="ctrl.selectedNationality"
                                                md-items="item in ctrl.searchNationalities(ctrl.searchNationality)"
                                                md-item-text="item.Name"
                                                md-min-length="0"
                                                placeholder="ابحث عن الجنسية..."
                                                ng-model="ctrl.MainObj.NationalityID">
                                                <md-item-template>
                                                    <span md-highlight-text="ctrl.searchNationality">{{item.Name}}</span>
                                                </md-item-template>
                                            </md-autocomplete>
                </div>
                                        <div class="col-md-4">
                                            <label class="form-label">سنة الميلاد <span class="text-danger" ng-show="mForm.BirthDate.$error.required">*</span></label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-calendar2-event"></i></span>
                                                <input type="number" class="form-control" ng-model="ctrl.MainObj.BirthDate" required min="1900" max="2100" step="1">
                </div>
                </div>
                                        <div class="col-md-4">
                                            <label class="form-label">مكان الميلاد <span class="text-danger" ng-show="mForm.BirthPlace.$error.required">*</span></label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-geo-alt"></i></span>
                                                <input type="text" class="form-control" ng-model="ctrl.MainObj.BirthPlace" required>
                </div>
                                        </div>-->

                                        <!-- Additional Information -->
                                        <!--<div class="col-md-6">
                                            <label class="form-label">العنوان <span class="text-danger" ng-show="mForm.AddressID.$error.required">*</span></label>
                                            <md-autocomplete class="modern-autocomplete"
                                                md-require-match="true"
                                                md-search-text="ctrl.TxtSearchAddresses"
                                                md-items="item in ctrl.searchAddresses(ctrl.TxtSearchAddresses)"
                                                md-item-text="item.Name"
                                                md-min-length="0"
                                                placeholder="ابحث عن العنوان..."
                                                md-selected-item="ctrl.MainObj.AddressID">
                                                <md-item-template>
                                                    <span md-highlight-text="ctrl.searchAddress">{{item.Name}}</span>
                                                </md-item-template>
                                            </md-autocomplete>
                </div>
                                        <div class="col-md-6">
                                            <label class="form-label">الحالة الاجتماعية <span class="text-danger" ng-show="mForm.MaritalStatusID.$error.required">*</span></label>
                                            <md-autocomplete class="modern-autocomplete"
                                                md-require-match="true"
                                                md-search-text="ctrl.TxtSearchMaritalStatuses"
                                                md-items="item in ctrl.searchMaritalStatuses(ctrl.TxtSearchMaritalStatuses)"
                                                md-item-text="item.Name"
                                                md-min-length="0"
                                                placeholder="ابحث عن الحالة الاجتماعية..."
                                                md-selected-item="ctrl.MainObj.MaritalStatusID">
                                                <md-item-template>
                                                    <span md-highlight-text="ctrl.searchMaritalStatus">{{item.Name}}</span>
                                                </md-item-template>
                                            </md-autocomplete>
                </div>
                                    </div>
                                </div>
                            </div>-->

                            <!-- Professional Information Card -->
                            <!--<div class="card shadow-sm mb-4">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">
                                        <i class="bi bi-briefcase-fill me-2"></i>
                                        المعلومات المهنية
                                    </h6>
    </div>
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label class="form-label">طريقة احتساب القسط <span class="text-danger" ng-show="mForm.IsSalary.$error.required">*</span></label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-calculator"></i></span>
                                                <select class="form-select" name="IsSalary" ng-change="ctrl.SelectedIsSalaryChanged()" ng-model="ctrl.IsSalary" required>
                                                    <option value="1">مرتب</option>
                                                    <option value="0">مؤهل علمي</option>
                                                </select>
                </div>
                </div>
                                        <div class="col-md-6">
                                            <label class="form-label">المهنة <span class="text-danger" ng-show="mForm.ProfessionID.$error.required">*</span></label>
                                            <md-autocomplete class="modern-autocomplete"
                                                md-selected-item="ctrl.MainObj.ProfessionID"
                                                md-require-match="true"
                                                md-search-text="ctrl.TxtSearchProffistion"
                                                md-items="item in ctrl.searchProfessions(ctrl.TxtSearchProffistion)"
                                                md-item-text="item.Name"
                                                md-min-length="0"
                                                md-selected-item-change="ctrl.SelectedProffistionChange(item)"
                                                placeholder="اختر المهنة"
                                                required>
                                                <md-item-template>
                                                    <span>{{item.Name}}</span>
                                                </md-item-template>
                                            </md-autocomplete>
                                        </div>-->

                                        <!-- Conditional Fields Based on IsSalary -->
                                        <!--<div class="col-md-6" ng-if="ctrl.MainObj.IsSalary">
                                            <label class="form-label">قيمة المرتب <span class="text-danger" ng-show="mForm.Salary.$error.required">*</span></label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-cash"></i></span>
                                                <input type="number" class="form-control" ng-change="ctrl.St_CalIns()" ng-model="ctrl.MainObj.Salary" required>
                </div>
                </div>
                                        <div class="col-md-6" ng-if="ctrl.MainObj.IsSalary && ctrl.MainObj.IsFreePer">
                                            <label class="form-label">النسبة المئوية <span class="text-danger" ng-show="mForm.Percentage.$error.required">*</span></label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-percent"></i></span>
                                                <input type="number" class="form-control" ng-change="ctrl.St_CalIns()" ng-model="ctrl.MainObj.Percentage" required>
                </div>
                </div>
                                        <div class="col-md-6" ng-if="!ctrl.MainObj.IsSalary">
                                            <label class="form-label">المؤهل العلمي <span class="text-danger" ng-show="mForm.AcademicQualificationID.$error.required">*</span></label>
                                            <md-autocomplete class="modern-autocomplete"
                                                md-selected-item="ctrl.AcademicQualificationID"
                                                md-require-match="true"
                                                md-search-text="ctrl.TxtSearchAcademicQualification"
                                                md-items="item in ctrl.searchAcademicQualifications(ctrl.TxtSearchAcademicQualification)"
                                                md-item-text="item.Name"
                                                md-min-length="0"
                                                placeholder="اختر المؤهل العلمي"
                                                required>
                                                <md-item-template>
                                                    <span>{{item.Name}}</span>
                                                </md-item-template>
                                            </md-autocomplete>
                </div>
                </div>
                </div>
                </div>
                        </div>-->

                        <!-- Premium Calculation Section -->
                        <!--<div class="col-lg-3 col-md-4">
                            <div class="card shadow-sm sticky-top" style="top: 1rem;">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">
                                        <i class="bi bi-calculator-fill me-2"></i>
                                        احتساب القسط
                                    </h6>
                </div>
                                <div class="card-body p-0">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <span>صافي القسط</span>
                                            <span class="badge bg-primary rounded-pill">{{ctrl.MainObj.installment | number:'2'}}</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <span>الدمغة</span>
                                            <span class="badge bg-info rounded-pill">{{ctrl.MainObj.Stamp | number:'2'}}</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <span>الضريبة</span>
                                            <span class="badge bg-warning rounded-pill">{{ctrl.MainObj.Tax | number:'2'}}</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <span>رسوم إشراف</span>
                                            <span class="badge bg-info rounded-pill">{{ctrl.MainObj.Supervision | number:'2'}}</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <span>م.الإصدار</span>
                                            <span class="badge bg-secondary rounded-pill">{{ctrl.MainObj.Version | number:'2'}}</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center bg-light">
                                            <span class="fw-bold">المجموع</span>
                                            <span class="badge bg-success rounded-pill fs-5">{{ctrl.MainObj.Total | number:'2'}}</span>
                                        </li>
                                    </ul>
                </div>
                </div>
                </div>
    </div>
                </form>
    </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-2"></i>إلغاء
                </button>
                <button type="button" class="btn btn-success" ng-disabled="mForm.$invalid" ng-click="ctrl.saveMainObj()">
                    <i class="bi bi-check-circle me-2"></i>حفظ
                </button>
    </div>
</div>
    </div>
</div>-->




