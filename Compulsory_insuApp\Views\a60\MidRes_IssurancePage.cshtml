﻿@{
    ViewBag.Title = "MidRes_IssurancePage";
}

<link href="~/Content/CSS/insurance-form-styles.css?v=1.0.0.2" rel="stylesheet" />
<link href="~/Content/CSS/bootstrap.rtl.min.css?v=1.0.0.1" rel="stylesheet" />
<link href="~/Content/InssunaceStyle.css?v=1.0.0.2" rel="stylesheet" media="screen" />
<link href="~/Content/PrintRPt.css?v=1.0.0.4" rel="stylesheet" media="print" />
<link href="~/Content/Cairo.css" rel="stylesheet" />
<link href="~/Content/CSS/all.min.css" rel="stylesheet" />


<div class="row justify-content-center d-print-none  " style="background-color:white">

    <div class="col-lg-12   col-md-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12" ng-if="ctrl.ShowFlag == 0">
        <div class="card  navglassColor ">
            <div class="card-header">

                <div class="btn-group cairo" dir="ltr" role="group">
                    <button type="button" disabled class="btn btn-primary btn-danger border-ridues  cairo "> واجهة إصدار وثيقة تامين برتقالية</button>
                    <button type="button" ng-click="ctrl.BtnHomePage()" class="btn btn-success bi bi-house-fill"><span class="m-2">الرئيسية</span></button>
                </div>
                <div class="col-auto float-end">
                    <button type="button"
                            class="btn btn-danger border-ridues  cairo "
                            data-bs-toggle="modal" data-bs-target="#NewItem" ng-click="ctrl.clearMainObj()">
                        إصدار وثيقة جديدة
                    </button>
                </div>
            </div>
        </div>

        <div class=" gradientModelBackg " style="background-color:aliceblue">


            <div class="row  ">
                <div class="col-sm-12 col-xs-12  offset-1 col-md-5 col-lg-5 col-xl-5 col-xxl-5">

                    <input class="inputStyle cairo " type="text" ng-model="search" placeholder="   بحث...   ">

                </div>
                <div class="col-sm-12 col-xs-12 col-md-3 col-lg-3 col-xl-3 col-xxl-3  ">



                </div>


            </div>

            <div class="row  mt-4 " style="height:80vh; padding:25px;overflow-y:scroll">
                <div class="table-responsive cairo ">
                    <table class="table     table-hover">
                        <tr class="  text-white" style="background-color:red">
                            <th class="text-center">#</th>
                            <th class="text-center">رقم الوثيقة</th>
                            <th class="text-center"> العميل </th>
                            <th class="text-center">  الوكيل </th>
                            <th class="text-center">العلاقة </th>
                            <th class="text-center"> تامين من يوم </th>
                            <th class="text-center"> تامين الى يوم </th>
                            <th class="text-center">  القسط  </th>
                            <th class="text-center">  الضرائب  </th>
                            <th class="text-center">  الإجمالي  </th>
                            <th class="text-center"> المصدر  </th>
                            <th class="text-center"> التاريخ  </th>
                            <th class="text-center" style="margin:0" colspan="3">العمليات</th>
                        </tr>
                        <tr class="tableStaylebody p-0 text-black" ng-repeat="x in ctrl.InsObj |filter:search ">
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{$index + 1}}</td>

                            <td class="text-center " dir="ltr" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">
                                {{x.CardNo}}
                            </td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.CustName}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.DealerName}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.Deal_relation}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.SDate}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.EDate}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.ins_val| number:'2'}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.TaxTotal | number:'2'}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.tot | number:'2'}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.InsertBy}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.InsertDate |date : "yyyy-MM-dd  a hh:mm" }}</td>
                            <td ng-hide="x.IsDelProg">
                                <button title="معاينة الوثيقة" type="button" class="btn btn-dark" ng-click="ctrl.prview(x)">
                                    <i class="bi bi-eye-fill"></i>
                                </button>
                            </td>
                            @*<td ng-hide="x.IsDelProg">
                                <button title="طباعة الوثيقة" type="button" class="btn btn-primary" ng-click="ctrl.PrintDoc(x)">
                                    <i class="bi bi-printer-fill"></i>
                                </button>
                            </td>*@
                            <td ng-hide="x.IsDelProg">
                                <button title="حذف الوثيقة" type="button" class="btn btn-danger" ng-click="ctrl.BtnStatus(x)">
                                    <i class="bi bi-trash-fill" aria-hidden="true"></i>
                                </button>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="col-12 insurance-document-premium" ng-if="ctrl.ShowFlag == 1">
        <!-- Enhanced Print Controls -->
        <div class="control-panel d-print-none mb-5">
            <div class="control-container">
                <div class="control-left">
                    <button class="btn-premium btn-back" ng-click="ctrl.ShowFlag = 0">
                        <div class="btn-icon">
                            <i class="bi bi-arrow-right-circle"></i>
                        </div>
                        <span class="btn-text">العودة للقائمة</span>
                    </button>
                </div>
                <div class="control-center">
                    <div class="document-status">
                        <div class="status-indicator active"></div>
                        <span class="status-text">وثيقة جاهزة للطباعة</span>
                    </div>
                </div>
                <div class="control-right">
                    <button class="btn-premium btn-print" ng-click="ctrl.PervPrint()">
                        <div class="btn-icon">
                            <i class="bi bi-printer-fill"></i>
                        </div>
                        <span class="btn-text">طباعة الوثيقة</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Premium Document Header -->
        <div class="document-header-premium">
            <div class="header-background">
                <div class="header-pattern"></div>
                <div class="header-overlay"></div>
            </div>
            <div class="header-content">
                <div class="company-logo">
                    <div class="logo-circle">
                        <div class="logo-inner">
                            <i class="bi bi-shield-check"></i>
                        </div>
                    </div>
                    <div class="logo-rings">
                        <div class="ring ring-1"></div>
                        <div class="ring ring-2"></div>
                        <div class="ring ring-3"></div>
                    </div>
                </div>
                <div class="header-text">
                    <h1 class="document-title">وثيقة تأمين مسؤولية طبية</h1>
                    <h2 class="company-name">الشركة الإتحادية للتأمين</h2>
                    <div class="document-meta">
                        <div class="meta-item">
                            <span class="meta-label">رقم الوثيقة</span>
                            <span class="meta-value">{{ctrl.printObj.CardNo}}</span>
                        </div>
                        <div class="meta-divider"></div>
                        <div class="meta-item">
                            <span class="meta-label">تاريخ الإصدار</span>
                            <span class="meta-value">{{ctrl.printObj.ins_dom}}/{{ctrl.printObj.ins_month}}/{{ctrl.printObj.ins_year}}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Premium Office Information Section -->
        <div class="info-section office-section">
            <div class="section-header-premium">
                <div class="section-icon">
                    <i class="bi bi-building-fill"></i>
                </div>
                <div class="section-title-container">
                    <h3 class="section-title">معلومات المكتب والوثيقة</h3>
                    <div class="section-subtitle">بيانات الجهة المصدرة للوثيقة</div>
                </div>
                <div class="section-decoration">
                    <div class="decoration-line"></div>
                    <div class="decoration-dot"></div>
                </div>
            </div>
            <div class="cards-container">
                <div class="info-card-premium">
                    <div class="card-header-premium">
                        <div class="card-icon-premium primary">
                            <i class="bi bi-file-earmark-text"></i>
                        </div>
                        <div class="card-title">رقم الوثيقة</div>
                    </div>
                    <div class="card-body-premium">
                        <div class="card-value">{{ctrl.printObj.CardNo}}</div>
                        <div class="card-description">الرقم المرجعي للوثيقة</div>
                    </div>
                </div>

                <div class="info-card-premium">
                    <div class="card-header-premium">
                        <div class="card-icon-premium success">
                            <i class="bi bi-building"></i>
                        </div>
                        <div class="card-title">اسم الفرع / المكتب</div>
                    </div>
                    <div class="card-body-premium">
                        <div class="card-value">{{ctrl.printObj.ag_name}}</div>
                        <div class="card-description">الجهة المصدرة للوثيقة</div>
                    </div>
                </div>

                <div class="info-card-premium full-width">
                    <div class="card-header-premium">
                        <div class="card-icon-premium warning">
                            <i class="bi bi-geo-alt-fill"></i>
                        </div>
                        <div class="card-title">عنوان المكتب / الفرع المصدر للوثيقة</div>
                    </div>
                    <div class="card-body-premium">
                        <div class="card-value">{{ctrl.printObj.ag_adress}}</div>
                        <div class="card-description">العنوان الكامل للمكتب المصدر</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Premium Contractor Information Section -->
        <div class="info-section contractor-section">
            <div class="section-header-premium">
                <div class="section-icon">
                    <i class="bi bi-person-badge-fill"></i>
                </div>
                <div class="section-title-container">
                    <h3 class="section-title">معلومات المتعاقد</h3>
                    <div class="section-subtitle">بيانات الشخص المتعاقد مع الشركة</div>
                </div>
                <div class="section-decoration">
                    <div class="decoration-line"></div>
                    <div class="decoration-dot"></div>
                </div>
            </div>
            <div class="cards-container">
                <div class="info-card-premium">
                    <div class="card-header-premium">
                        <div class="card-icon-premium primary">
                            <i class="bi bi-person-fill"></i>
                        </div>
                        <div class="card-title">اسم المتعاقد</div>
                    </div>
                    <div class="card-body-premium">
                        <div class="card-value">{{ctrl.printObj.Dela_Name}}</div>
                        <div class="card-description">الاسم الكامل للمتعاقد</div>
                    </div>
                </div>

                <div class="info-card-premium">
                    <div class="card-header-premium">
                        <div class="card-icon-premium info">
                            <i class="bi bi-house-fill"></i>
                        </div>
                        <div class="card-title">العنوان</div>
                    </div>
                    <div class="card-body-premium">
                        <div class="card-value">{{ctrl.printObj.Cus_adress}}</div>
                        <div class="card-description">عنوان إقامة المتعاقد</div>
                    </div>
                </div>

                <div class="info-card-premium full-width">
                    <div class="card-header-premium">
                        <div class="card-icon-premium secondary">
                            <i class="bi bi-people-fill"></i>
                        </div>
                        <div class="card-title">صلة المتعاقد بالمؤمن له</div>
                    </div>
                    <div class="card-body-premium">
                        <div class="card-value">{{ctrl.printObj.Dela_cus_relation}}</div>
                        <div class="card-description">طبيعة العلاقة بين المتعاقد والمؤمن له</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Premium Insured Person Information Section -->
        <div class="info-section insured-section">
            <div class="section-header-premium">
                <div class="section-icon">
                    <i class="bi bi-person-circle"></i>
                </div>
                <div class="section-title-container">
                    <h3 class="section-title">معلومات المؤمن له</h3>
                    <div class="section-subtitle">البيانات الشخصية والمهنية للمؤمن له</div>
                </div>
                <div class="section-decoration">
                    <div class="decoration-line"></div>
                    <div class="decoration-dot"></div>
                </div>
            </div>
            <div class="personal-info-grid">
                <div class="personal-card main-info">
                    <div class="personal-header">
                        <div class="personal-avatar">
                            <i class="bi bi-person-fill"></i>
                        </div>
                        <div class="personal-title">
                            <h4>البيانات الأساسية</h4>
                            <p>المعلومات الشخصية الرئيسية</p>
                        </div>
                    </div>
                    <div class="personal-details">
                        <div class="detail-item">
                            <div class="detail-icon">
                                <i class="bi bi-person-badge"></i>
                            </div>
                            <div class="detail-content">
                                <span class="detail-label">اسم المؤمن له</span>
                                <span class="detail-value">{{ctrl.printObj.Cus_Name}}</span>
                            </div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-icon">
                                <i class="bi bi-calendar-event"></i>
                            </div>
                            <div class="detail-content">
                                <span class="detail-label">سنة الميلاد</span>
                                <span class="detail-value">{{ctrl.printObj.BirthDate}}</span>
                            </div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-icon">
                                <i class="bi bi-geo-alt"></i>
                            </div>
                            <div class="detail-content">
                                <span class="detail-label">مكان الميلاد</span>
                                <span class="detail-value">{{ctrl.printObj.BirthPlace}}</span>
                            </div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-icon">
                                <i class="bi bi-flag"></i>
                            </div>
                            <div class="detail-content">
                                <span class="detail-label">الجنسية</span>
                                <span class="detail-value">{{ctrl.printObj.Nationality}}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="personal-card social-info">
                    <div class="personal-header">
                        <div class="personal-avatar">
                            <i class="bi bi-people-fill"></i>
                        </div>
                        <div class="personal-title">
                            <h4>البيانات الاجتماعية والمهنية</h4>
                            <p>المعلومات المهنية والاجتماعية</p>
                        </div>
                    </div>
                    <div class="personal-details">
                        <div class="detail-item">
                            <div class="detail-icon">
                                <i class="bi bi-heart"></i>
                            </div>
                            <div class="detail-content">
                                <span class="detail-label">الحالة الاجتماعية</span>
                                <span class="detail-value">{{ctrl.printObj.MaritalStatus}}</span>
                            </div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-icon">
                                <i class="bi bi-briefcase"></i>
                            </div>
                            <div class="detail-content">
                                <span class="detail-label">المهنة</span>
                                <span class="detail-value">{{ctrl.printObj.Profession}}</span>
                            </div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-icon">
                                <i class="bi bi-mortarboard"></i>
                            </div>
                            <div class="detail-content">
                                <span class="detail-label">المؤهل العلمي</span>
                                <span class="detail-value">{{ctrl.printObj.AcademicQualification}}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Premium Insurance Coverage Period Section -->
        <div class="coverage-section-premium">
            <div class="section-header-premium">
                <div class="section-icon">
                    <i class="bi bi-calendar-check-fill"></i>
                </div>
                <div class="section-title-container">
                    <h3 class="section-title">فترة سريان التأمين</h3>
                    <div class="section-subtitle">المدة الزمنية لتغطية التأمين</div>
                </div>
                <div class="section-decoration">
                    <div class="decoration-line"></div>
                    <div class="decoration-dot"></div>
                </div>
            </div>

            <div class="timeline-premium">
                <div class="timeline-track">
                    <div class="track-line"></div>
                    <div class="track-progress"></div>
                </div>

                <div class="timeline-points">
                    <div class="timeline-point start-point">
                        <div class="point-circle">
                            <div class="point-inner">
                                <i class="bi bi-play-fill"></i>
                            </div>
                        </div>
                        <div class="point-content">
                            <div class="point-header">
                                <h4 class="point-title">بداية التأمين</h4>
                                <div class="point-badge start">فعال</div>
                            </div>
                            <div class="point-details">
                                <div class="detail-group">
                                    <div class="detail-item-premium">
                                        <i class="bi bi-calendar3"></i>
                                        <span class="detail-text">{{ctrl.printObj.Sdate}}</span>
                                    </div>
                                    <div class="detail-item-premium">
                                        <i class="bi bi-clock"></i>
                                        <span class="detail-text">{{ctrl.printObj.Stime}}</span>
                                    </div>
                                    <div class="detail-item-premium">
                                        <i class="bi bi-calendar-week"></i>
                                        <span class="detail-text">{{ctrl.printObj.Sday}}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-point end-point">
                        <div class="point-circle">
                            <div class="point-inner">
                                <i class="bi bi-stop-fill"></i>
                            </div>
                        </div>
                        <div class="point-content">
                            <div class="point-header">
                                <h4 class="point-title">نهاية التأمين</h4>
                                <div class="point-badge end">منتهي</div>
                            </div>
                            <div class="point-details">
                                <div class="detail-group">
                                    <div class="detail-item-premium">
                                        <i class="bi bi-calendar3"></i>
                                        <span class="detail-text">{{ctrl.printObj.Edate}}</span>
                                    </div>
                                    <div class="detail-item-premium">
                                        <i class="bi bi-clock"></i>
                                        <span class="detail-text">{{ctrl.printObj.Etime}}</span>
                                    </div>
                                    <div class="detail-item-premium">
                                        <i class="bi bi-calendar-week"></i>
                                        <span class="detail-text">{{ctrl.printObj.Eday}}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="coverage-status-premium">
                    <div class="status-container">
                        <div class="status-icon">
                            <i class="bi bi-shield-check"></i>
                        </div>
                        <div class="status-content">
                            <h5 class="status-title">حالة الوثيقة</h5>
                            <p class="status-description">وثيقة سارية المفعول ومعتمدة</p>
                        </div>
                        <div class="status-indicator">
                            <div class="indicator-dot active"></div>
                            <span class="indicator-text">نشطة</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Premium Document Issue Information -->
        <div class="issue-section-premium">
            <div class="section-header-premium">
                <div class="section-icon">
                    <i class="bi bi-file-earmark-text-fill"></i>
                </div>
                <div class="section-title-container">
                    <h3 class="section-title">معلومات الإصدار</h3>
                    <div class="section-subtitle">تاريخ ووقت إصدار الوثيقة</div>
                </div>
                <div class="section-decoration">
                    <div class="decoration-line"></div>
                    <div class="decoration-dot"></div>
                </div>
            </div>
            <div class="issue-date-container">
                <div class="date-display">
                    <div class="date-header">
                        <i class="bi bi-calendar-event"></i>
                        <span>تاريخ الإصدار</span>
                    </div>
                    <div class="date-content">
                        <div class="date-item">
                            <span class="date-label">اليوم</span>
                            <span class="date-value">{{ctrl.printObj.ins_day}}</span>
                        </div>
                        <div class="date-separator">-</div>
                        <div class="date-item">
                            <span class="date-label">التاريخ</span>
                            <span class="date-value">{{ctrl.printObj.ins_dom}}</span>
                        </div>
                        <div class="date-separator">-</div>
                        <div class="date-item">
                            <span class="date-label">الشهر</span>
                            <span class="date-value">{{ctrl.printObj.ins_month}}</span>
                        </div>
                        <div class="date-separator">-</div>
                        <div class="date-item">
                            <span class="date-label">السنة</span>
                            <span class="date-value">{{ctrl.printObj.ins_year}}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Premium Financial Information -->
        <div class="financial-section-premium">
            <div class="section-header-premium">
                <div class="section-icon">
                    <i class="bi bi-cash-stack"></i>
                </div>
                <div class="section-title-container">
                    <h3 class="section-title">التفاصيل المالية</h3>
                    <div class="section-subtitle">تفصيل الأقساط والرسوم والضرائب</div>
                </div>
                <div class="section-decoration">
                    <div class="decoration-line"></div>
                    <div class="decoration-dot"></div>
                </div>
            </div>

            <div class="financial-breakdown">
                <div class="breakdown-grid">
                    <div class="financial-item primary">
                        <div class="item-header">
                            <div class="item-icon">
                                <i class="bi bi-cash-coin"></i>
                            </div>
                            <h4 class="item-title">قيمة التأمين</h4>
                        </div>
                        <div class="item-amount">{{ctrl.printObj.Insat}} د.ل</div>
                        <div class="item-description">القسط الأساسي للتأمين</div>
                    </div>

                    <div class="financial-item secondary">
                        <div class="item-header">
                            <div class="item-icon">
                                <i class="bi bi-percent"></i>
                            </div>
                            <h4 class="item-title">الضريبة</h4>
                        </div>
                        <div class="item-amount">{{ctrl.printObj.Tax}} د.ل</div>
                        <div class="item-description">ضريبة القيمة المضافة</div>
                    </div>

                    <div class="financial-item tertiary">
                        <div class="item-header">
                            <div class="item-icon">
                                <i class="bi bi-stamp"></i>
                            </div>
                            <h4 class="item-title">الدمغة</h4>
                        </div>
                        <div class="item-amount">{{ctrl.printObj.Stamp}} د.ل</div>
                        <div class="item-description">رسم الدمغة الحكومية</div>
                    </div>

                    <div class="financial-item quaternary">
                        <div class="item-header">
                            <div class="item-icon">
                                <i class="bi bi-eye"></i>
                            </div>
                            <h4 class="item-title">رسوم الإشراف</h4>
                        </div>
                        <div class="item-amount">{{ctrl.printObj.Supervision}} د.ل</div>
                        <div class="item-description">رسوم الإشراف والرقابة</div>
                    </div>

                    <div class="financial-item quinary">
                        <div class="item-header">
                            <div class="item-icon">
                                <i class="bi bi-file-earmark"></i>
                            </div>
                            <h4 class="item-title">مصاريف الإصدار</h4>
                        </div>
                        <div class="item-amount">{{ctrl.printObj.Virsion}} د.ل</div>
                        <div class="item-description">رسوم إصدار الوثيقة</div>
                    </div>

                    <div class="financial-item total">
                        <div class="item-header">
                            <div class="item-icon">
                                <i class="bi bi-calculator"></i>
                            </div>
                            <h4 class="item-title">إجمالي الضرائب</h4>
                        </div>
                        <div class="item-amount">{{ctrl.printObj.Taxtotal}} د.ل</div>
                        <div class="item-description">مجموع الضرائب والرسوم</div>
                    </div>
                </div>

                <div class="total-summary">
                    <div class="summary-content">
                        <div class="summary-icon">
                            <i class="bi bi-cash-stack"></i>
                        </div>
                        <div class="summary-text">
                            <h3 class="summary-title">إجمالي المبلغ المستحق</h3>
                            <p class="summary-description">المبلغ الكامل شامل جميع الرسوم والضرائب</p>
                        </div>
                        <div class="summary-amount">
                            <span class="amount-value">{{ctrl.printObj.Total}}</span>
                            <span class="amount-currency">دينار ليبي</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Premium Document Footer -->
        <div class="document-footer-premium">
            <div class="footer-background">
                <div class="footer-pattern"></div>
            </div>
            <div class="footer-content-premium">
                <div class="footer-main">
                    <div class="footer-logo">
                        <div class="footer-logo-circle">
                            <i class="bi bi-shield-check"></i>
                        </div>
                    </div>
                    <div class="footer-info">
                        <h4 class="footer-title">الشركة الإتحادية للتأمين</h4>
                        <p class="footer-subtitle">شركة رائدة في مجال التأمين الطبي</p>
                        <div class="footer-details">
                            <div class="detail-row">
                                <i class="bi bi-geo-alt"></i>
                                <span>طرابلس - ليبيا</span>
                            </div>
                            <div class="detail-row">
                                <i class="bi bi-telephone"></i>
                                <span>+218 21 123 4567</span>
                            </div>
                            <div class="detail-row">
                                <i class="bi bi-envelope"></i>
                                <span><EMAIL></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="footer-legal">
                    <div class="legal-text">
                        <p>هذه الوثيقة صادرة عن الشركة الإتحادية للتأمين وتخضع لأحكام وشروط التأمين المعمول بها</p>
                        <p>جميع الحقوق محفوظة © 2024 الشركة الإتحادية للتأمين</p>
                    </div>
                </div>

                <div class="footer-signature">
                    <div class="signature-section">
                        <div class="signature-box">
                            <div class="signature-line"></div>
                            <div class="signature-label">توقيع المسؤول</div>
                            <div class="signature-date">التاريخ: ___________</div>
                        </div>
                        <div class="stamp-box">
                            <div class="stamp-area">
                                <div class="stamp-border">
                                    <div class="stamp-text">ختم الشركة</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="footer-qr">
                    <div class="qr-section">
                        <div class="qr-placeholder">
                            <i class="bi bi-qr-code"></i>
                        </div>
                        <div class="qr-text">
                            <p>امسح الكود للتحقق من صحة الوثيقة</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Ultra Premium CSS for Professional Insurance Document -->
    <style>
        /* Main Document Styling */
        .insurance-document-premium {
            font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 50%, #f1f5f9 100%);
            min-height: 100vh;
            padding: 40px 20px;
            color: #1e293b;
            position: relative;
            overflow-x: hidden;
        }

        .insurance-document-premium::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        /* Enhanced Control Panel */
        .control-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 40px;
        }

        .control-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 30px;
        }

        .btn-premium {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 15px;
            padding: 15px 30px;
            color: white;
            font-weight: 600;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            text-decoration: none;
            cursor: pointer;
        }

        .btn-back {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            box-shadow: 0 4px 15px rgba(245, 87, 108, 0.4);
        }

        .btn-print {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
        }

        .btn-premium:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }

        .btn-icon {
            font-size: 18px;
        }

        .document-status {
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(16, 185, 129, 0.1);
            padding: 12px 20px;
            border-radius: 25px;
            border: 2px solid rgba(16, 185, 129, 0.2);
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: linear-gradient(135deg, #10b981, #059669);
            animation: pulse 2s infinite;
        }

        .status-text {
            color: #059669;
            font-weight: 600;
            font-size: 14px;
        }

         pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Premium Document Header */
        .document-header-premium {
            position: relative;
            margin-bottom: 50px;
            border-radius: 25px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }

        .header-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .header-pattern {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            background-size: 100px 100px;
        }

        .header-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.1);
        }

        .header-content {
            position: relative;
            z-index: 2;
            padding: 60px 40px;
            display: flex;
            align-items: center;
            gap: 40px;
            color: white;
        }

        .company-logo {
            position: relative;
            flex-shrink: 0;
        }

        .logo-circle {
            width: 120px;
            height: 120px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
            border: 3px solid rgba(255, 255, 255, 0.3);
            position: relative;
            z-index: 3;
        }

        .logo-inner {
            font-size: 3.5rem;
            color: white;
        }

        .logo-rings {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .ring {
            position: absolute;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .ring-1 {
            width: 140px;
            height: 140px;
            animation: rotate 20s linear infinite;
        }

        .ring-2 {
            width: 160px;
            height: 160px;
            animation: rotate 30s linear infinite reverse;
        }

        .ring-3 {
            width: 180px;
            height: 180px;
            animation: rotate 40s linear infinite;
        }

         rotate {
            from { transform: translate(-50%, -50%) rotate(0deg); }
            to { transform: translate(-50%, -50%) rotate(360deg); }
        }

        .header-text {
            flex: 1;
        }

        .document-title {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            line-height: 1.2;
        }

        .company-name {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 25px;
            opacity: 0.9;
        }

        .document-meta {
            display: flex;
            align-items: center;
            gap: 20px;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .meta-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .meta-label {
            font-size: 0.9rem;
            opacity: 0.8;
            font-weight: 500;
        }

        .meta-value {
            font-size: 1.1rem;
            font-weight: 700;
        }

        .meta-divider {
            width: 2px;
            height: 40px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 1px;
        }

        /* Premium Section Headers */
        .info-section {
            margin-bottom: 50px;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .section-header-premium {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 35px;
            padding-bottom: 20px;
            border-bottom: 2px solid rgba(102, 126, 234, 0.1);
            position: relative;
        }

        .section-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .section-title-container {
            flex: 1;
        }

        .section-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 5px;
        }

        .section-subtitle {
            font-size: 1rem;
            color: #64748b;
            font-weight: 500;
        }

        .section-decoration {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .decoration-line {
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .decoration-dot {
            width: 12px;
            height: 12px;
            background: #667eea;
            border-radius: 50%;
        }

        /* Premium Cards */
        .cards-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
        }

        .info-card-premium {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(226, 232, 240, 0.8);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .info-card-premium::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .info-card-premium:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
        }

        .info-card-premium.full-width {
            grid-column: 1 / -1;
        }

        .card-header-premium {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .card-icon-premium {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
        }

        .card-icon-premium.primary {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        }

        .card-icon-premium.success {
            background: linear-gradient(135deg, #10b981, #059669);
        }

        .card-icon-premium.warning {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }

        .card-icon-premium.info {
            background: linear-gradient(135deg, #06b6d4, #0891b2);
        }

        .card-icon-premium.secondary {
            background: linear-gradient(135deg, #6b7280, #4b5563);
        }

        .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #374151;
        }

        .card-body-premium {
            padding-left: 65px;
        }

        .card-value {
            font-size: 1.3rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .card-description {
            font-size: 0.9rem;
            color: #64748b;
            font-weight: 500;
        }

        /* Personal Information Grid */
        .personal-info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .personal-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 20px;
            padding: 35px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(226, 232, 240, 0.8);
            position: relative;
            overflow: hidden;
        }

        .personal-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .personal-header {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid rgba(102, 126, 234, 0.1);
        }

        .personal-avatar {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
        }

        .personal-title h4 {
            font-size: 1.3rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 5px;
        }

        .personal-title p {
            font-size: 0.95rem;
            color: #64748b;
            margin: 0;
        }

        .personal-details {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .detail-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            background: rgba(248, 250, 252, 0.8);
            border-radius: 12px;
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }

        .detail-item:hover {
            background: rgba(102, 126, 234, 0.05);
            transform: translateX(5px);
        }

        .detail-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
            flex-shrink: 0;
        }

        .detail-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 3px;
        }

        .detail-label {
            font-size: 0.85rem;
            color: #64748b;
            font-weight: 600;
        }

        .detail-value {
            font-size: 1.1rem;
            font-weight: 700;
            color: #1e293b;
        }

        /* Premium Timeline */
        .coverage-section-premium {
            margin-bottom: 50px;
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(5, 150, 105, 0.05) 100%);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 10px 40px rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.1);
        }

        .timeline-premium {
            position: relative;
        }

        .timeline-track {
            position: relative;
            height: 6px;
            margin: 40px 0;
        }

        .track-line {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100%;
            background: rgba(203, 213, 225, 0.5);
            border-radius: 3px;
        }

        .track-progress {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, #10b981, #059669);
            border-radius: 3px;
            animation: progressFill 3s ease-in-out;
        }

         progressFill {
            from { width: 0%; }
            to { width: 100%; }
        }

        .timeline-points {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 40px;
        }

        .timeline-point {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .point-circle {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
            margin-bottom: 25px;
            position: relative;
            z-index: 2;
        }

        .end-point .point-circle {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
        }

        .point-inner {
            color: white;
            font-size: 1.8rem;
        }

        .point-content {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            width: 100%;
        }

        .point-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .point-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #1e293b;
        }

        .point-badge {
            padding: 6px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            color: white;
        }

        .point-badge.start {
            background: linear-gradient(135deg, #10b981, #059669);
        }

        .point-badge.end {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }

        .detail-group {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .detail-item-premium {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            background: rgba(248, 250, 252, 0.8);
            border-radius: 10px;
        }

        .detail-item-premium i {
            color: #667eea;
            font-size: 1rem;
        }

        .detail-text {
            font-weight: 600;
            color: #374151;
        }

        .coverage-status-premium {
            margin-top: 40px;
            text-align: center;
        }

        .status-container {
            display: inline-flex;
            align-items: center;
            gap: 20px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            padding: 25px 40px;
            border-radius: 25px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .status-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #10b981, #059669);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        .status-content h5 {
            font-size: 1.2rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 5px;
        }

        .status-content p {
            font-size: 0.95rem;
            color: #64748b;
            margin: 0;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .indicator-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #10b981;
            animation: pulse 2s infinite;
        }

        .indicator-text {
            font-weight: 600;
            color: #059669;
        }

        /* Issue Section Premium */
        .issue-section-premium {
            margin-bottom: 50px;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .issue-date-container {
            display: flex;
            justify-content: center;
        }

        .date-display {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
            text-align: center;
            min-width: 600px;
        }

        .date-header {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .date-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .date-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .date-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .date-value {
            font-size: 1.3rem;
            font-weight: 700;
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 15px;
            border-radius: 10px;
            backdrop-filter: blur(5px);
        }

        .date-separator {
            font-size: 1.5rem;
            font-weight: 700;
            opacity: 0.6;
        }

        /* Financial Section Premium */
        .financial-section-premium {
            margin-bottom: 50px;
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, rgba(217, 119, 6, 0.05) 100%);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 10px 40px rgba(245, 158, 11, 0.1);
            border: 1px solid rgba(245, 158, 11, 0.1);
        }

        .financial-breakdown {
            margin-top: 30px;
        }

        .breakdown-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .financial-item {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .financial-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #f59e0b, #d97706);
        }

        .financial-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
        }

        .financial-item.total {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .financial-item.total::before {
            background: linear-gradient(90deg, #ffffff, #f8fafc);
        }

        .item-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .item-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #f59e0b, #d97706);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .financial-item.total .item-icon {
            background: rgba(255, 255, 255, 0.2);
        }

        .item-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #374151;
        }

        .financial-item.total .item-title {
            color: white;
        }

        .item-amount {
            font-size: 1.4rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .financial-item.total .item-amount {
            color: white;
            font-size: 1.6rem;
        }

        .item-description {
            font-size: 0.9rem;
            color: #64748b;
            font-weight: 500;
        }

        .financial-item.total .item-description {
            color: rgba(255, 255, 255, 0.8);
        }

        .total-summary {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            border-radius: 25px;
            padding: 40px;
            color: white;
            box-shadow: 0 15px 50px rgba(30, 41, 59, 0.3);
        }

        .summary-content {
            display: flex;
            align-items: center;
            gap: 30px;
        }

        .summary-icon {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            backdrop-filter: blur(10px);
        }

        .summary-text {
            flex: 1;
        }

        .summary-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .summary-description {
            font-size: 1rem;
            opacity: 0.8;
            margin: 0;
        }

        .summary-amount {
            text-align: right;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
        }

        .amount-value {
            font-size: 3rem;
            font-weight: 800;
            line-height: 1;
        }

        .amount-currency {
            font-size: 1.2rem;
            opacity: 0.8;
            margin-top: 5px;
        }

        /* Premium Document Footer */
        .document-footer-premium {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            border-radius: 25px;
            margin-top: 60px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(30, 41, 59, 0.3);
        }

        .footer-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }

        .footer-pattern {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            background-size: 60px 60px;
        }

        .footer-content-premium {
            position: relative;
            z-index: 2;
            padding: 50px 40px;
            color: white;
        }

        .footer-main {
            display: flex;
            align-items: center;
            gap: 30px;
            margin-bottom: 40px;
            padding-bottom: 30px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .footer-logo {
            flex-shrink: 0;
        }

        .footer-logo-circle {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .footer-info {
            flex: 1;
        }

        .footer-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .footer-subtitle {
            font-size: 1.1rem;
            opacity: 0.8;
            margin-bottom: 20px;
        }

        .footer-details {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .detail-row {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.95rem;
        }

        .detail-row i {
            width: 20px;
            opacity: 0.8;
        }

        .footer-legal {
            text-align: center;
            margin-bottom: 40px;
            padding: 25px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .legal-text p {
            margin-bottom: 10px;
            font-size: 0.95rem;
            opacity: 0.9;
        }

        .footer-signature {
            margin-bottom: 30px;
        }

        .signature-section {
            display: flex;
            justify-content: space-between;
            gap: 40px;
        }

        .signature-box, .stamp-box {
            flex: 1;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .signature-line {
            width: 200px;
            height: 2px;
            background: rgba(255, 255, 255, 0.3);
            margin: 0 auto 15px;
            border-radius: 1px;
        }

        .signature-label, .signature-date {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-bottom: 8px;
        }

        .stamp-area {
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .stamp-border {
            width: 120px;
            height: 60px;
            border: 2px dashed rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .stamp-text {
            font-size: 0.8rem;
            opacity: 0.6;
        }

        .footer-qr {
            text-align: center;
        }

        .qr-section {
            display: inline-flex;
            align-items: center;
            gap: 20px;
            background: rgba(255, 255, 255, 0.05);
            padding: 20px 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .qr-placeholder {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .qr-text p {
            margin: 0;
            font-size: 0.9rem;
            opacity: 0.8;
        }

        /* Responsive Design */
         (max-width: 768px) {
            .insurance-document-premium {
                padding: 20px 15px;
            }

            .header-content {
                flex-direction: column;
                text-align: center;
                gap: 30px;
                padding: 40px 20px;
            }

            .document-title {
                font-size: 2rem;
            }

            .company-name {
                font-size: 1.4rem;
            }

            .document-meta {
                flex-direction: column;
                gap: 15px;
            }

            .control-container {
                flex-direction: column;
                gap: 20px;
            }

            .cards-container {
                grid-template-columns: 1fr;
            }

            .personal-info-grid {
                grid-template-columns: 1fr;
            }

            .timeline-points {
                flex-direction: column;
                gap: 30px;
            }

            .breakdown-grid {
                grid-template-columns: 1fr;
            }

            .summary-content {
                flex-direction: column;
                text-align: center;
                gap: 20px;
            }

            .footer-main {
                flex-direction: column;
                text-align: center;
                gap: 20px;
            }

            .signature-section {
                flex-direction: column;
                gap: 20px;
            }
        }

        /* Print Styles */
         print {
            .insurance-document-premium {
                background: white !important;
                padding: 20px !important;
            }

            .insurance-document-premium::before {
                display: none !important;
            }

            .control-panel {
                display: none !important;
            }

            .header-background,
            .header-pattern,
            .header-overlay {
                background: white !important;
            }

            .header-content {
                color: #000 !important;
                background: white !important;
                border: 2px solid #000 !important;
                border-radius: 0 !important;
            }

            .company-logo,
            .logo-circle,
            .logo-inner {
                color: #000 !important;
                background: white !important;
                border: 1px solid #000 !important;
            }

            .logo-rings,
            .ring {
                display: none !important;
            }

            .document-title,
            .company-name,
            .meta-label,
            .meta-value {
                color: #000 !important;
            }

            .document-meta {
                background: white !important;
                border: 1px solid #000 !important;
            }

            .info-section,
            .coverage-section-premium,
            .issue-section-premium,
            .financial-section-premium {
                background: white !important;
                border: 1px solid #000 !important;
                box-shadow: none !important;
                page-break-inside: avoid;
            }

            .section-icon,
            .card-icon-premium,
            .personal-avatar,
            .detail-icon,
            .point-circle,
            .status-icon,
            .item-icon {
                background: white !important;
                color: #000 !important;
                border: 1px solid #000 !important;
            }

            .section-title,
            .section-subtitle,
            .card-title,
            .card-value,
            .card-description,
            .personal-title h4,
            .personal-title p,
            .detail-label,
            .detail-value,
            .point-title,
            .detail-text,
            .status-content h5,
            .status-content p,
            .item-title,
            .item-amount,
            .item-description {
                color: #000 !important;
            }

            .info-card-premium,
            .personal-card,
            .point-content,
            .status-container,
            .financial-item,
            .date-display {
                background: white !important;
                border: 1px solid #000 !important;
                box-shadow: none !important;
            }

            .info-card-premium::before,
            .personal-card::before,
            .financial-item::before {
                background: #000 !important;
            }

            .track-line,
            .track-progress {
                background: #000 !important;
            }

            .point-badge {
                background: white !important;
                color: #000 !important;
                border: 1px solid #000 !important;
            }

            .total-summary {
                background: white !important;
                color: #000 !important;
                border: 2px solid #000 !important;
            }

            .summary-icon {
                background: white !important;
                color: #000 !important;
                border: 1px solid #000 !important;
            }

            .summary-title,
            .summary-description,
            .amount-value,
            .amount-currency {
                color: #000 !important;
            }

            .document-footer-premium {
                background: white !important;
                color: #000 !important;
                border: 2px solid #000 !important;
                box-shadow: none !important;
            }

            .footer-background,
            .footer-pattern {
                display: none !important;
            }

            .footer-logo-circle,
            .signature-box,
            .stamp-box,
            .qr-section {
                background: white !important;
                color: #000 !important;
                border: 1px solid #000 !important;
            }

            .footer-title,
            .footer-subtitle,
            .detail-row,
            .legal-text p,
            .signature-label,
            .signature-date,
            .stamp-text,
            .qr-text p {
                color: #000 !important;
            }

            .signature-line {
                background: #000 !important;
            }

            .stamp-border {
                border-color: #000 !important;
            }

            /* Hide animations and transitions in print */
            * {
                animation: none !important;
                transition: none !important;
                box-shadow: none !important;
                text-shadow: none !important;
            }
        }
    </style>



</div>

 
<div class="d-print-block d-none" @*ng-if="ctrl.ShowFlag == 1"*@>
    <div style="height:11cm !important"></div>
    <div class="col-sm-12 print-content" >
        <table style="width: 100%; border-collapse: collapse; font-size: 8px; margin-right: 2.8cm !important ">
            <tr>
                <td style="padding: 1px; border: none; ">رقم الوثيقة: {{ctrl.printObj.CardNo}}</td>
                <td style="padding: 1px; border: none;">المكتب: {{ctrl.printObj.ag_name}}</td>
            </tr>
            <tr>
                <td style="padding: 1px; border: none;">عنوان المكتب: {{ctrl.printObj.ag_adress}}</td>
                <td style="padding: 1px; border: none;">المتعاقد: {{ctrl.printObj.Dela_Name}}</td>
            </tr>
            <tr>
                <td colspan="2" style="padding: 1px; border: none;">صلة المتعاقد: {{ctrl.printObj.Dela_cus_relation}}</td>
            </tr>
            <tr>
                <td style="padding: 1px; border: none;">المؤمن له: {{ctrl.printObj.Cus_Name}}</td>
                <td style="padding: 1px; border: none;">الجنسية: {{ctrl.printObj.Nationality}}</td>
            </tr>
            <tr>
                <td style="padding: 1px; border: none;">الميلاد: {{ctrl.printObj.BirthDate}}/{{ctrl.printObj.BirthPlace}}</td>
                <td style="padding: 1px; border: none;">الحالة: {{ctrl.printObj.MaritalStatus}}</td>
            </tr>
            <tr>
                <td style="padding: 1px; border: none;">المهنة: {{ctrl.printObj.Profession}}</td>
                <td style="padding: 1px; border: none;">العنوان: {{ctrl.printObj.Cus_adress}}</td>
            </tr>
            <tr>
                <td style="padding: 1px; border: none;">المدة: {{ctrl.printObj.dur}}</td>
                <td style="padding: 1px; border: none;">من {{ctrl.printObj.Sdate}} إلى {{ctrl.printObj.Edate}}</td>
            </tr>
        </table>
    </div>
    <div style="height: 6cm !important"></div>
    <div class="col-sm-12 print-content text-center" style="text-align:center !important">
        <table class="justify-content-center" style="width: 70%; border-collapse: collapse; font-size: 8px; margin-right : 2.8cm !important ">
            <tr>
                <td style="padding: 1px;"> صافي القسط</td>
                <td style="padding: 1px;">رسوم الضريبة </td>
                <td style="padding: 1px;"> رسم الدمغة</td>
                <td style="padding: 1px;"> الإشراف والرقابة</td>
                <td style="padding: 1px;">  الإصدار</td>
                <td style="padding: 1px;"> الإجمالي </td>
            </tr>
            <tr>
                <td style="padding: 1px;"> {{ctrl.printObj.Insat}}</td>
                <td style="padding: 1px;">  {{ctrl.printObj.Tax}}</td>
                <td style="padding: 1px;">  {{ctrl.printObj.Virsion}}</td>
                <td style="padding: 1px;">  {{ctrl.printObj.Supervision}}</td>
                <td style="padding: 1px;">  {{ctrl.printObj.Stamp}}</td>
                <td style="padding: 1px;">  {{ctrl.printObj.Total}}</td>

            </tr>
            </table>

    </div>
    <div class="col-12 print-content text-center" >
        <div class="row">
            <div class="col date-item">
                <span class="date-label">تاريخ الإصدار</span>
                <span class="date-label">{{ctrl.printObj.full_date}}</span>
            </div>
            <div class="col date-item">
                <span class="date-label">ساعة الإصدار</span>
                <span class="date-label">{{ctrl.printObj.ins_Time}}</span>
            </div>
        </div>
    </div>
</div>
    



<div class="modal fade cairo d-print-none" id="NewItem" tabindex="-1" data-bs-backdrop="static" aria-labelledby="{{ctrl.Title}}" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content gradentModelGray">
            <div class="modal-header" style="background-color:red">
                <h5 class="modal-title text-white" id="exampleModalLabel">إضافة وثيقة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body cairo " style="background-color:red">
                <form name="mForm" autocomplete="off" novalidate ng-submit="ctrl.saveMainObj()" ng-form-commit>
                    <div class="row">
                        <div class="col-8">
                            <!-- بيانات التأمين الأساسية -->
                            <fieldset class="form-section">
                                <legend class="section-title">
                                    <i class="bi bi-shield-fill"></i>
                                    بيانات التأمين الأساسية
                                </legend>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group" ng-class="{'has-error': mForm.Date_From.$invalid && (mForm.Date_From.$dirty || mForm.Date_From.$touched)}">
                                            <label class="required" for="Date_From">تاريخ البداية</label>
                                            <div class="input-group" ng-class="{'is-invalid': mForm.Date_From.$invalid && (mForm.Date_From.$dirty || mForm.Date_From.$touched), 'is-valid': mForm.Date_From.$valid && (mForm.Date_From.$dirty || mForm.Date_From.$touched)}">
                                                <span class="input-group-text"><i class="bi bi-calendar-date"></i></span>
                                                <input type="date" id="Date_From" name="Date_From" class="form-control"
                                                       ng-model="ctrl.MainObj.Date_From"
                                                       ng-change="ctrl.CalEndDate()"
                                                       required>
                                            </div>
                                            <div class="validation-message" ng-show="mForm.Date_From.$error.required && (mForm.Date_From.$dirty || mForm.Date_From.$touched)">
                                                يجب إدخال تاريخ البداية
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group" ng-class="{'has-error': mForm.Duration.$invalid && (mForm.Duration.$dirty || mForm.Duration.$touched)}">
                                            <label class="required" for="Duration">المدة</label>
                                            <div class="input-group" ng-class="{'is-invalid': mForm.Duration.$invalid && (mForm.Duration.$dirty || mForm.Duration.$touched), 'is-valid': mForm.Duration.$valid && (mForm.Duration.$dirty || mForm.Duration.$touched)}">
                                                <span class="input-group-text"><i class="bi bi-clock"></i></span>
                                                <input type="number" id="Duration" name="Duration" class="form-control"
                                                       ng-model="ctrl.MainObj.Duration"
                                                       ng-change="ctrl.CalEndDate()"
                                                       required min="1">
                                            </div>
                                            <div class="validation-message" ng-show="mForm.Duration.$error.required && (mForm.Duration.$dirty || mForm.Duration.$touched)">
                                                يجب إدخال المدة
                                            </div>
                                            <div class="validation-message" ng-show="mForm.Duration.$error.min && (mForm.Duration.$dirty || mForm.Duration.$touched)">
                                                يجب أن تكون المدة أكبر من صفر
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="Date_To">تاريخ النهاية</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-calendar-check"></i></span>
                                                <input type="date" id="Date_To" class="form-control"
                                                       ng-model="ctrl.MainObj.Date_To" disabled>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>

                            <!-- بيانات العميل -->
                            <fieldset class="form-section">
                                <legend class="section-title">
                                    <i class="bi bi-person-fill"></i>
                                    بيانات العميل
                                </legend>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group" ng-class="{'has-error': mForm.CustomerName.$invalid && (mForm.CustomerName.$dirty || mForm.CustomerName.$touched)}">
                                            <label class="required" for="CustomerName">اسم العميل</label>
                                            <div class="input-group" ng-class="{'is-invalid': mForm.CustomerName.$invalid && (mForm.CustomerName.$dirty || mForm.CustomerName.$touched), 'is-valid': mForm.CustomerName.$valid && (mForm.CustomerName.$dirty || mForm.CustomerName.$touched)}">
                                                <span class="input-group-text"><i class="bi bi-person"></i></span>
                                                <input type="text" id="CustomerName" name="CustomerName" class="form-control"
                                                       ng-model="ctrl.MainObj.CustomerName"
                                                       required
                                                       ng-minlength="3">
                                            </div>
                                            <div class="validation-message" ng-show="mForm.CustomerName.$error.required && (mForm.CustomerName.$dirty || mForm.CustomerName.$touched)">
                                                يجب إدخال اسم العميل
                                            </div>
                                            <div class="validation-message" ng-show="mForm.CustomerName.$error.minlength && (mForm.CustomerName.$dirty || mForm.CustomerName.$touched)">
                                                يجب أن يكون اسم العميل 3 أحرف على الأقل
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="required" for="DealerName">اسم الوكيل</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-person-badge"></i></span>
                                                <input type="text" id="DealerName" name="DealerName" class="form-control"
                                                       ng-model="ctrl.MainObj.DealerName" required>
                                            </div>
                                            <div class="validation-message" ng-show="mForm.DealerName.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="required" for="DealerCustomerReleation">علاقة الوكيل بالعميل</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-people"></i></span>
                                                <input type="text" id="DealerCustomerReleation" name="DealerCustomerReleation"
                                                       class="form-control" ng-model="ctrl.MainObj.DealerCustomerReleation" required>
                                            </div>
                                            <div class="validation-message" ng-show="mForm.DealerCustomerReleation.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>
                                    <!-- حقل الجنسية -->
                                    <div class="col-md-6">
                                        <div class="form-group" ng-class="{'has-error': mForm.NationalityID.$invalid && (mForm.NationalityID.$dirty || mForm.NationalityID.$touched)}">
                                            <label class="required" for="NationalityID">الجنسية</label>
                                            <md-autocomplete id="NationalityID"
                                                             md-selected-item="ctrl.NationalityID"
                                                             md-require-match="true"
                                                             md-search-text="ctrl.TxtselectedNationality"
                                                             md-items="item in ctrl.searchNationalities(ctrl.TxtselectedNationality)"
                                                             md-item-text="item.Name"
                                                             md-min-length="0"
                                                             md-selected-item-change="ctrl.SelectedNationalityChange()"
                                                             placeholder="اختر  عنوان"
                                                             ng-required="!ctrl.MainObj.IsSalary">
                                                <md-item-template>
                                                    <span>{{item.Name}}</span>
                                                </md-item-template>
                                            </md-autocomplete>
                                            <div class="validation-message" ng-show="mForm.NationalityID.$error.required && (mForm.NationalityID.$dirty || mForm.NationalityID.$touched)">
                                                يجب اختيار الجنسية
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>

                            <!-- البيانات الشخصية -->
                            <fieldset class="form-section">
                                <legend class="section-title">
                                    <i class="bi bi-person-vcard"></i>
                                    البيانات الشخصية
                                </legend>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="required" for="BirthDate">سنة الميلاد</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-calendar2-event"></i></span>
                                                <input type="number" id="BirthDate" name="BirthDate" class="form-control"
                                                       ng-model="ctrl.MainObj.BirthDate" required min="1900" max="2100" step="1">
                                            </div>
                                            <div class="validation-message" ng-show="mForm.BirthDate.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="required" for="BirthPlace">مكان الميلاد</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-geo-alt"></i></span>
                                                <input type="text" id="BirthPlace" name="BirthPlace" class="form-control"
                                                       ng-model="ctrl.MainObj.BirthPlace" required>
                                            </div>
                                            <div class="validation-message" ng-show="mForm.BirthPlace.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="required" for="AddressID">العنوان</label>

                                            <md-autocomplete id="AddressID"
                                                             md-selected-item="ctrl.AddressID"
                                                             md-require-match="true"
                                                             md-search-text="ctrl.TxtSearchAddresses"
                                                             md-items="item in ctrl.searchAddresses(ctrl.TxtSearchAddresses)"
                                                             md-item-text="item.Name"
                                                             md-min-length="0"
                                                             md-selected-item-change="ctrl.SelectedAddressesChange()"
                                                             placeholder="اختر  عنوان"
                                                             ng-required="!ctrl.MainObj.IsSalary">
                                                <md-item-template>
                                                    <span>{{item.Name}}</span>
                                                </md-item-template>
                                            </md-autocomplete>
                                            <div class="validation-message" ng-show="mForm.AddressID.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="required" for="MaritalStatusID">الحالة الاجتماعية</label>

                                            <md-autocomplete id="MaritalStatusID"
                                                             md-selected-item="ctrl.MaritalStatusID"
                                                             md-require-match="true"
                                                             md-search-text="ctrl.TXtsearchMaritalStatuses"
                                                             md-items="item in ctrl.MaritalStatuseslist(ctrl.TXtsearchMaritalStatuses)"
                                                             md-item-text="item.Name"
                                                             md-min-length="0"
                                                             md-selected-item-change="ctrl.SelectedMaritalStatusesChange()"
                                                             placeholder="اختر الحالة الاجتماعية"
                                                             ng-required="!ctrl.MainObj.IsSalary">
                                                <md-item-template>
                                                    <span>{{item.Name}}</span>
                                                </md-item-template>
                                            </md-autocomplete>
                                            <div class="validation-message" ng-show="mForm.ProfessionID.$error.required">هذا الحقل مطلوب</div>
                                            <div class="validation-message" ng-show="mForm.MaritalStatusID.$error.required && (mForm.MaritalStatusID.$dirty || mForm.MaritalStatusID.$touched)">
                                                يجب اختيار الحالة الاجتماعية
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>

                            <!-- البيانات المهنية والمالية -->
                            <fieldset class="form-section">
                                <legend class="section-title">
                                    <i class="bi bi-briefcase-fill"></i>
                                    البيانات المهنية والمالية
                                </legend>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="required" for="IsSalary">طريقة احتساب القسط</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-calculator"></i></span>
                                                <select id="IsSalary" name="IsSalary" class="form-select"
                                                        ng-model="ctrl.IsSalary"
                                                        ng-change="ctrl.SelectedIsSalaryChanged()" required>
                                                    <option value="1">مرتب</option>
                                                    <option value="0">مؤهل علمي</option>
                                                </select>
                                            </div>
                                            <div class="validation-message" ng-show="mForm.IsSalary.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="required" for="ProfessionID">المهنة</label>
                                            <md-autocomplete id="ProfessionID"
                                                             md-selected-item="ctrl.ProfessionID"
                                                             md-require-match="true"
                                                             md-search-text="ctrl.TxtSearchProffistion"
                                                             md-items="item in ctrl.searchProfessions(ctrl.TxtSearchProffistion)"
                                                             md-item-text="item.Name"
                                                             md-min-length="0"
                                                             md-selected-item-change="ctrl.SelectedProffistionChange()"
                                                             placeholder="اختر المهنة"
                                                             required>
                                                <md-item-template>
                                                    <span>{{item.Name}}</span>
                                                </md-item-template>
                                            </md-autocomplete>
                                            <div class="validation-message" ng-show="mForm.ProfessionID.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- حقول مشروطة بناءً على طريقة الاحتساب -->
                                <div class="row mt-3" ng-if="ctrl.MainObj.IsSalary">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="required" for="Salary">قيمة المرتب</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-cash"></i></span>
                                                <input type="number" id="Salary" name="Salary" class="form-control"
                                                       ng-model="ctrl.MainObj.Salary"
                                                       ng-change="ctrl.St_CalIns()" required>
                                            </div>
                                            <div class="validation-message" ng-show="mForm.Salary.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>

                                    <div class="col-md-6" ng-if="ctrl.MainObj.IsFreePer">
                                        <div class="form-group">
                                            <label class="required" for="Percentage">النسبة المئوية</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-percent"></i></span>
                                                <input type="number" id="Percentage" name="Percentage" class="form-control"
                                                       ng-model="ctrl.MainObj.Percentage"
                                                       ng-change="ctrl.St_CalIns()" ng-required="ctrl.MainObj.IsFreePer">
                                            </div>
                                            <div class="validation-message" ng-show="mForm.Percentage.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>

                                    <div class="col-md-6" ng-if="!ctrl.MainObj.IsFreePer">
                                        <div class="form-group">
                                            <label class="required" for="PerValue">النسبة</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-percent"></i></span>
                                                <select id="PerValue" name="PerValue" class="form-select"
                                                        ng-options="x.Value as x.Name for x in ctrl.PercentageList"
                                                        ng-model="ctrl.PerValue"
                                                        ng-change="ctrl.SelectedPerValueChanged()" ng-required="!ctrl.MainObj.IsFreePer">
                                                </select>
                                            </div>
                                            <div class="validation-message" ng-show="mForm.PerValue.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mt-3" ng-if="!ctrl.MainObj.IsSalary">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="required" for="AcademicQualificationID">المؤهل العلمي</label>

                                            <md-autocomplete id="AcademicQualificationID"
                                                             md-selected-item="ctrl.AcademicQualificationID"
                                                             md-require-match="true"
                                                             md-search-text="ctrl.TxtSearchAcademicQualification"
                                                             md-items="item in ctrl.searchAcademicQualifications(ctrl.TxtSearchAcademicQualification)"
                                                             md-item-text="item.Name"
                                                             md-min-length="0"
                                                             md-selected-item-change="ctrl.SelectedAcademicQualificationChange()"
                                                             placeholder="اختر  المؤهل العلمي"
                                                             ng-required="!ctrl.MainObj.IsSalary">
                                                <md-item-template>
                                                    <span>{{item.Name}}</span>
                                                </md-item-template>
                                            </md-autocomplete>
                                            <div class="validation-message" ng-show="mForm.AcademicQualificationID.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>

                            <!-- بيانات المطبوعة -->
                            <fieldset class="form-section">
                                <legend class="section-title">
                                    <i class="bi bi-file-text"></i>
                                    بيانات المطبوعة
                                </legend>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="required" for="pap_no">رقم المطبوعة</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-upc"></i></span>
                                                <input type="number" id="pap_no" name="pap_no" class="form-control text-center"
                                                       ng-model="ctrl.MainObj.pap_no"
                                                       ng-click="ctrl.IsExistPayperNum = 0"
                                                       min="0" required>
                                            </div>
                                            <div class="validation-message" ng-show="mForm.pap_no.$error.required">هذا الحقل مطلوب</div>
                                            <div class="text-danger mt-2" ng-if="ctrl.IsExistPayperNum == 1">
                                                الرقم غير صحيح
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>
                        </div>

                        <!-- جانب احتساب القسط -->
                        <div class="col-4">
                            <div class="calculation-panel">
                                <div class="calculation-header">
                                    <i class="bi bi-calculator-fill"></i>
                                    احتساب القسط
                                </div>

                                <div class="calculation-items">
                                    <div class="calc-item">
                                        <div class="calc-label">صافي القسط</div>
                                        <div class="calc-value">{{ctrl.MainObj.installment | number:'2'}}</div>
                                    </div>

                                    <div class="calc-item">
                                        <div class="calc-label">الدمغة</div>
                                        <div class="calc-value">{{ctrl.MainObj.Stamp | number:'2'}}</div>
                                    </div>

                                    <div class="calc-item">
                                        <div class="calc-label">الضريبة</div>
                                        <div class="calc-value">{{ctrl.MainObj.Tax | number:'2'}}</div>
                                    </div>

                                    <div class="calc-item">
                                        <div class="calc-label">رسوم إشراف</div>
                                        <div class="calc-value">{{ctrl.MainObj.Supervision | number:'2'}}</div>
                                    </div>

                                    <div class="calc-item">
                                        <div class="calc-label">م.الإصدار</div>
                                        <div class="calc-value">{{ctrl.MainObj.Version | number:'2'}}</div>
                                    </div>

                                    <div class="calc-total">
                                        <div class="total-label">المجموع</div>
                                        <div class="total-value">{{ctrl.MainObj.Total | number:'2'}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer justify-content-center" style="background-color:red">
                <button type="button" class="btn btn-success col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" ng-disabled="mForm.$invalid" ng-click="ctrl.a600015()">
                    حفظ
                </button>
                <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">إلغاء الأمر</button>
            </div>
            @*<div class="modal-footer justify-content-center" ng-if="ctrl.showhFlag==2">
                    <button type="button" class="btn btn-success col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" ng-disabled="mForm.$invalid" ng-click="ctrl.a40008()">
                        حفظ كجديد
                    </button>
                    <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">إلغاء الأمر</button>
                </div>*@
        </div>
    </div>
</div>








