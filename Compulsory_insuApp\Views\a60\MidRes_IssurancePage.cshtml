@{
    ViewBag.Title = "MidRes_IssurancePage";
}

<link href="~/Content/CSS/insurance-form-styles.css?v=1.0.0.2" rel="stylesheet" />
<link href="~/Content/CSS/bootstrap.rtl.min.css?v=1.0.0.1" rel="stylesheet" />
<link href="~/Content/InssunaceStyle.css?v=1.0.0.2" rel="stylesheet" media="screen" />
<link href="~/Content/PrintRPt.css?v=1.0.0.4" rel="stylesheet" media="print" />
<link href="~/Content/Cairo.css" rel="stylesheet" />
<link href="~/Content/CSS/all.min.css" rel="stylesheet" />


<div class="row justify-content-center d-print-none  " style="background-color:white">

    <div class="col-lg-12   col-md-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12" ng-if="ctrl.ShowFlag == 0">
        <div class="card  navglassColor ">
            <div class="card-header">

                <div class="btn-group cairo" dir="ltr" role="group">
                    <button type="button" disabled class="btn btn-primary btn-danger border-ridues  cairo "> واجهة إصدار وثيقة تامين برتقالية</button>
                    <button type="button" ng-click="ctrl.BtnHomePage()" class="btn btn-success bi bi-house-fill"><span class="m-2">الرئيسية</span></button>
                </div>
                <div class="col-auto float-end">
                    <button type="button"
                            class="btn btn-danger border-ridues  cairo "
                            data-bs-toggle="modal" data-bs-target="#NewItem" ng-click="ctrl.clearMainObj()">
                        إصدار وثيقة جديدة
                    </button>
                </div>
            </div>
        </div>

        <div class=" gradientModelBackg " style="background-color:aliceblue">


            <div class="row  ">
                <div class="col-sm-12 col-xs-12  offset-1 col-md-5 col-lg-5 col-xl-5 col-xxl-5">

                    <input class="inputStyle cairo " type="text" ng-model="search" placeholder="   بحث...   ">

                </div>
                <div class="col-sm-12 col-xs-12 col-md-3 col-lg-3 col-xl-3 col-xxl-3  ">



                </div>


            </div>

            <div class="row  mt-4 " style="height:80vh; padding:25px;overflow-y:scroll">
                <div class="table-responsive cairo ">
                    <table class="table     table-hover">
                        <tr class="  text-white" style="background-color:red">
                            <th class="text-center">#</th>
                            <th class="text-center">رقم الوثيقة</th>
                            <th class="text-center"> العميل </th>
                            <th class="text-center">  الوكيل </th>
                            <th class="text-center">العلاقة </th>
                            <th class="text-center"> تامين من يوم </th>
                            <th class="text-center"> تامين الى يوم </th>
                            <th class="text-center">  القسط  </th>
                            <th class="text-center">  الضرائب  </th>
                            <th class="text-center">  الإجمالي  </th>
                            <th class="text-center"> المصدر  </th>
                            <th class="text-center"> التاريخ  </th>
                            <th class="text-center" style="margin:0" colspan="3">العمليات</th>
                        </tr>
                        <tr class="tableStaylebody p-0 text-black" ng-repeat="x in ctrl.InsObj |filter:search ">
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{$index + 1}}</td>

                            <td class="text-center " dir="ltr" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">
                                {{x.CardNo}}
                            </td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.CustName}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.DealerName}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.Deal_relation}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.SDate}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.EDate}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.ins_val| number:'2'}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.TaxTotal | number:'2'}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.tot | number:'2'}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.InsertBy}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.InsertDate |date : "yyyy-MM-dd  a hh:mm" }}</td>
                            <td ng-hide="x.IsDelProg">
                                <button title="معاينة الوثيقة" type="button" class="btn btn-dark" ng-click="ctrl.prview(x)">
                                    <i class="bi bi-eye-fill"></i>
                                </button>
                            </td>
                            <td ng-hide="x.IsDelProg">
                                <button title="طباعة الوثيقة" type="button" class="btn btn-primary" ng-click="ctrl.printDoc(x)">
                                    <i class="bi bi-printer-fill"></i>
                                </button>
                            </td>
                            <td ng-hide="x.IsDelProg">
                                <button title="حذف الوثيقة" type="button" class="btn btn-danger" ng-click="ctrl.BtnStatus(x)">
                                    <i class="bi bi-trash-fill" aria-hidden="true"></i>
                                </button>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="col-12 insurance-document" ng-if="ctrl.ShowFlag == 1">
        <!-- Print Controls -->
        <div class="print-controls d-print-none mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <button class="btn btn-success btn-lg shadow-sm" ng-click="ctrl.ShowFlag = 0">
                    <i class="bi bi-arrow-right-circle"></i> رجوع
                </button>
                <button class="btn btn-primary btn-lg shadow-sm" ng-click="ctrl.printDoc(x)">
                    <i class="bi bi-printer-fill"></i> طباعة الوثيقة
                </button>
            </div>
        </div>

        <!-- Document Header -->
        <div class="document-header text-center mb-5">
            <div class="header-logo mb-3">
                <div class="logo-container">
                    <i class="bi bi-shield-check text-primary" style="font-size: 4rem;"></i>
                </div>
            </div>
            <h2 class="document-title text-primary fw-bold mb-2">وثيقة تأمين مسؤولية طبية</h2>
            <h4 class="document-subtitle text-secondary">الشركة الإتحادية للتأمين</h4>
            <div class="document-number mt-3">
                <span class="badge bg-primary fs-6 px-4 py-2">رقم الوثيقة: {{ctrl.printObj.CardNo}}</span>
            </div>
        </div>
        <!-- Office Information Section -->
        <div class="office-info-section mb-5">
            <div class="section-header">
                <h4 class="section-title text-primary">
                    <i class="bi bi-building"></i> معلومات المكتب والوثيقة
                </h4>
            </div>
            <div class="info-grid">
                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="info-card premium-card">
                            <div class="card-icon">
                                <i class="bi bi-file-earmark-text text-primary"></i>
                            </div>
                            <div class="card-content">
                                <label class="field-label">رقم الوثيقة</label>
                                <div class="field-value">{{ctrl.printObj.CardNo}}</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-card premium-card">
                            <div class="card-icon">
                                <i class="bi bi-building text-success"></i>
                            </div>
                            <div class="card-content">
                                <label class="field-label">اسم الفرع / المكتب</label>
                                <div class="field-value">{{ctrl.printObj.ag_name}}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row g-4 mt-2">
                    <div class="col-12">
                        <div class="info-card premium-card">
                            <div class="card-icon">
                                <i class="bi bi-geo-alt text-warning"></i>
                            </div>
                            <div class="card-content">
                                <label class="field-label">عنوان المكتب / الفرع المصدر للوثيقة</label>
                                <div class="field-value">{{ctrl.printObj.ag_adress}}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contractor Information Section -->
        <div class="contractor-info-section mb-5">
            <div class="section-header">
                <h4 class="section-title text-success">
                    <i class="bi bi-person-badge"></i> معلومات المتعاقد
                </h4>
            </div>
            <div class="info-grid">
                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="info-card premium-card">
                            <div class="card-icon">
                                <i class="bi bi-person text-primary"></i>
                            </div>
                            <div class="card-content">
                                <label class="field-label">اسم المتعاقد</label>
                                <div class="field-value">{{ctrl.printObj.Dela_Name}}</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-card premium-card">
                            <div class="card-icon">
                                <i class="bi bi-house text-info"></i>
                            </div>
                            <div class="card-content">
                                <label class="field-label">العنوان</label>
                                <div class="field-value">{{ctrl.printObj.Cus_adress}}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row g-4 mt-2">
                    <div class="col-12">
                        <div class="info-card premium-card">
                            <div class="card-icon">
                                <i class="bi bi-link-45deg text-secondary"></i>
                            </div>
                            <div class="card-content">
                                <label class="field-label">صلة المتعاقد بالمؤمن له</label>
                                <div class="field-value">{{ctrl.printObj.Dela_cus_relation}}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Insured Person Information Section -->
        <div class="insured-info-section mb-5">
            <div class="section-header">
                <h4 class="section-title text-info">
                    <i class="bi bi-person-circle"></i> معلومات المؤمن له
                </h4>
            </div>
            <div class="info-grid">
                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="info-card premium-card">
                            <div class="card-icon">
                                <i class="bi bi-person-fill text-primary"></i>
                            </div>
                            <div class="card-content">
                                <label class="field-label">اسم المؤمن له</label>
                                <div class="field-value">{{ctrl.printObj.Cus_Name}}</div>
                            </div>
                            <div class="card-content">
                                <label class="field-label">سنة الميلاد</label>
                                <div class="field-value">{{ctrl.printObj.BirthDate}}</div>
                            </div>
                            <div class="card-content">
                                <label class="field-label"> الحالة الإجتماعية</label>
                                <div class="field-value">{{ctrl.printObj.MaritalStatus}}</div>
                            </div>
                            <div class="card-content">
                                <label class="field-label"> المهنة </label>
                                <div class="field-value">{{ctrl.printObj.Profession}}</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-card premium-card">
                            <div class="card-icon">
                                <i class="bi bi-flag text-success"></i>
                            </div>
                            <div class="card-content">
                                <label class="field-label">الجنسية</label>
                                <div class="field-value">{{ctrl.printObj.Nationality}}</div>
                            </div>
                            <div class="card-content">
                                <label class="field-label">مكان الميلاد</label>
                                <div class="field-value">{{ctrl.printObj.BirthPlace}}</div>
                            </div> 
                            <div class="card-content">
                                <label class="field-label"> المؤهل العلمي</label>
                                <div class="field-value">{{ctrl.printObj.AcademicQualification}}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Insurance Coverage Period Section -->
        <div class="coverage-section mb-5">
            <div class="section-header">
                <h4 class="section-title text-success">
                    <i class="bi bi-calendar-check"></i> فترة سريان التأمين
                </h4>
            </div>
            <div class="coverage-container">
                <div class="coverage-timeline">
                    <div class="timeline-item start-date">
                        <div class="timeline-icon">
                            <i class="bi bi-play-circle-fill text-success"></i>
                        </div>
                        <div class="timeline-content">
                            <h6 class="timeline-title text-success">بداية التأمين</h6>
                            <div class="timeline-details">
                                <div class="detail-row">
                                    <span class="detail-label">التاريخ:</span>
                                    <span class="detail-value">{{ctrl.printObj.Sdate}}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">اليوم:</span>
                                    <span class="detail-value">{{ctrl.printObj.Sday}}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">الساعة:</span>
                                    <span class="detail-value">{{ctrl.printObj.Stime}}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-connector"></div>
                    
                    <div class="timeline-item end-date">
                        <div class="timeline-icon">
                            <i class="bi bi-stop-circle-fill text-danger"></i>
                        </div>
                        <div class="timeline-content">
                            <h6 class="timeline-title text-danger">نهاية التأمين</h6>
                            <div class="timeline-details">
                                <div class="detail-row">
                                    <span class="detail-label">التاريخ:</span>
                                    <span class="detail-value">{{ctrl.printObj.Edate}}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">اليوم:</span>
                                    <span class="detail-value">{{ctrl.printObj.Eday}}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">الساعة:</span>
                                    <span class="detail-value">{{ctrl.printObj.Etime}}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="coverage-status">
                    <div class="status-badge active">
                        <i class="bi bi-shield-check"></i>
                        <span>وثيقة سارية المفعول</span>
                    </div>
                </div>
            </div>
        </div>
        <!-- Document Issue Information -->
        <div class="issue-section mb-5">
            <div class="section-header">
                <h4 class="section-title text-dark">
                    <i class="bi bi-file-earmark-text"></i> معلومات الإصدار
                </h4>
            </div>
            <div class="issue-container">
                <div class="row g-4">
                    <div class="col-md-3">
                        <div class="info-card premium-card">
                            <div class="card-icon">
                                <i class="bi bi-calendar3 text-primary"></i>
                            </div>
                            <div class="card-content">
                                <label class="field-label">تحرر في يوم</label>
                                <div class="field-value">{{ctrl.printObj.ins_day}}</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-card premium-card">
                            <div class="card-icon">
                                <i class="bi bi-calendar-day text-success"></i>
                            </div>
                            <div class="card-content">
                                <label class="field-label">الموافق</label>
                                <div class="field-value">{{ctrl.printObj.ins_dom}}</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-card premium-card">
                            <div class="card-icon">
                                <i class="bi bi-calendar-month text-info"></i>
                            </div>
                            <div class="card-content">
                                <label class="field-label">من شهر</label>
                                <div class="field-value">{{ctrl.printObj.ins_month}}</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-card premium-card">
                            <div class="card-icon">
                                <i class="bi bi-calendar4-range text-warning"></i>
                            </div>
                            <div class="card-content">
                                <label class="field-label">سنة</label>
                                <div class="field-value">{{ctrl.printObj.ins_year}}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Financial Information -->
        <div class="financial-section mb-5">
            <div class="section-header">
                <h4 class="section-title text-warning">
                    <i class="bi bi-currency-dollar"></i> المعلومات المالية
                </h4>
            </div>
            <div class="financial-container">
                <div class="row g-4">
                    <div class="col-md-3">
                        <div class="financial-card">
                            <div class="card-icon">
                                <i class="bi bi-cash text-success"></i>
                            </div>
                            <div class="card-content">
                                <label class="field-label">قيمة التأمين</label>
                                <div class="field-value amount">{{ctrl.printObj.Insat}} د.ل</div>
                            </div>
                            <div class="card-content">
                                <label class="field-label"> م.اصدار</label>
                                <div class="field-value amount">{{ctrl.printObj.Virsion}} د.ل</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="financial-card">
                            <div class="card-icon">
                                <i class="bi bi-percent text-warning"></i>
                            </div>
                            <div class="card-content">
                                <label class="field-label">الضريبة</label>
                                <div class="field-value amount">{{ctrl.printObj.Tax}} د.ل</div>
                            </div>
                            <div class="card-content">
                                <label class="field-label">م.اشراف</label>
                                <div class="field-value amount">{{ctrl.printObj.Supervision}} د.ل</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="financial-card">
                            <div class="card-icon">
                                <i class="bi bi-stamp text-info"></i>
                            </div>
                            <div class="card-content">
                                <label class="field-label">الدمغة</label>
                                <div class="field-value amount">{{ctrl.printObj.Stamp}} د.ل</div>
                            </div>
                            <div class="card-content">
                                <label class="field-label">اجمالي الضرائب</label>
                                <div class="field-value amount">{{ctrl.printObj.Taxtotal}} د.ل</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="financial-card total-card">
                            <div class="card-icon">
                                <i class="bi bi-calculator text-primary"></i>
                            </div>
                            <div class="card-content">
                                <label class="field-label">الإجمالي</label>
                                <div class="field-value amount total">{{ctrl.printObj.Total}} د.ل</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Document Footer -->
        <div class="document-footer text-center mt-5 pt-4">
            <div class="footer-content">
                <p class="footer-text">هذه الوثيقة صادرة عن الشركة الإتحادية للتأمين</p>
                <p class="footer-text">وتخضع لأحكام وشروط التأمين المعمول بها</p>
                <div class="footer-signature mt-4">
                    <div class="signature-line"></div>
                    <p class="signature-text">توقيع المسؤول</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced CSS for Professional Insurance Document -->
    <style>
        /* Main Document Styling */
        .insurance-document {
            font-family: 'Segoe UI', 'Tahoma', 'Geneva', 'Verdana', sans-serif;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            min-height: 100vh;
            padding: 30px;
            color: #333;
        }

        /* Print Controls */
        .print-controls {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        /* Document Header */
        .document-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 8px 30px rgba(0, 123, 255, 0.3);
            margin-bottom: 40px;
        }

        .logo-container {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 100px;
            height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
        }

        .document-title {
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .document-subtitle {
            font-size: 1.5rem;
            opacity: 0.9;
        }

        .document-number .badge {
            font-size: 1.1rem;
            padding: 12px 25px;
            border-radius: 25px;
        }

        /* Section Headers */
        .section-header {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            padding: 15px 0;
            border-bottom: 3px solid;
            position: relative;
            display: flex;
            align-items: center;
            gap: 15px;
        }

            .section-title::after {
                content: '';
                position: absolute;
                bottom: -3px;
                left: 0;
                width: 60px;
                height: 3px;
                background: currentColor;
                border-radius: 2px;
            }

        /* Premium Cards */
        .premium-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: none;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            height: 100%;
            display: flex;
            align-items: center;
            gap: 20px;
        }

            .premium-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            }

        .card-icon {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

            .card-icon i {
                font-size: 1.5rem;
            }

        .card-content {
            flex: 1;
        }

        .field-label {
            font-size: 0.9rem;
            color: #6c757d;
            font-weight: 500;
            margin-bottom: 5px;
            display: block;
        }

        .field-value {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            line-height: 1.3;
        }

        /* Coverage Timeline */
        .coverage-container {
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 5px 20px rgba(40, 167, 69, 0.1);
        }

        .coverage-timeline {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 30px;
            position: relative;
        }

        .timeline-item {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            flex: 1;
            max-width: 300px;
            text-align: center;
        }

        .timeline-icon {
            margin-bottom: 15px;
        }

            .timeline-icon i {
                font-size: 2.5rem;
            }

        .timeline-title {
            font-weight: 600;
            margin-bottom: 15px;
        }

        .timeline-details {
            text-align: right;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .detail-label {
            font-weight: 500;
            color: #6c757d;
        }

        .detail-value {
            font-weight: 600;
            color: #2c3e50;
        }

        .timeline-connector {
            flex: 1;
            height: 4px;
            background: linear-gradient(90deg, #28a745 0%, #dc3545 100%);
            margin: 0 20px;
            border-radius: 2px;
            position: relative;
        }

            .timeline-connector::before {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 20px;
                height: 20px;
                background: #ffc107;
                border-radius: 50%;
                border: 3px solid white;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            }

        .coverage-status {
            text-align: center;
        }

        .status-badge {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }

        /* Financial Cards */
        .financial-container {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 5px 20px rgba(255, 193, 7, 0.1);
        }

        .financial-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            height: 100%;
            display: flex;
            align-items: center;
            gap: 20px;
        }

            .financial-card:hover {
                transform: translateY(-3px);
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            }

        .total-card {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
        }

            .total-card .field-label,
            .total-card .field-value {
                color: white;
            }

        .amount {
            font-size: 1.3rem;
            font-weight: 700;
        }

        .total {
            font-size: 1.5rem;
        }

        /* Document Footer */
        .document-footer {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            padding: 40px;
            border-radius: 20px;
            margin-top: 50px;
        }

        .footer-text {
            font-size: 1.1rem;
            margin-bottom: 10px;
            opacity: 0.9;
        }

        .signature-line {
            width: 200px;
            height: 2px;
            background: white;
            margin: 0 auto 10px;
            border-radius: 1px;
        }

        .signature-text {
            font-size: 0.9rem;
            opacity: 0.8;
            margin: 0;
        }

        /* Print Styles */
        print {
            .insurance-document

        {
            background: white !important;
            padding: 20px !important;
        }

        .print-controls {
            display: none !important;
        }

        .document-header {
            background: white !important;
            color: #000 !important;
            border: 2px solid #000 !important;
        }

        .section-title {
            color: #000 !important;
            border-bottom-color: #000 !important;
        }

        .premium-card, .financial-card, .timeline-item {
            border: 1px solid #000 !important;
            background: white !important;
            box-shadow: none !important;
        }

        .coverage-container, .financial-container {
            background: white !important;
            border: 1px solid #000 !important;
        }

        .document-footer {
            background: white !important;
            color: #000 !important;
            border: 2px solid #000 !important;
        }

        .status-badge, .total-card {
            background: white !important;
            color: #000 !important;
            border: 1px solid #000 !important;
        }

        .timeline-connector {
            background: #000 !important;
        }

        .signature-line {
            background: #000 !important;
        }

        /* Hide gradients and shadows in print */
        * {
            box-shadow: none !important;
            text-shadow: none !important;
        }

        .card-icon {
            background: white !important;
            border: 1px solid #000 !important;
        }

        .logo-container {
            background: white !important;
            border: 2px solid #000 !important;
        }
        }
    </style>



</div>


<div class="d-print-block d-none">
    <div class="print-controls  mb-4">
        <div class="d-flex justify-content-between align-items-center">
            <button class="btn btn-success btn-lg shadow-sm" ng-click="ctrl.ShowFlag = 0">
                <i class="bi bi-arrow-right-circle"></i> رجوع
            </button>
        </div>
    </div>
    <div class="col-12" style="margin-top: 11cm !important">
        <div class="row">
            <div class="card-content col">
                <label class="field-label">رقم الوثيقة:- {{ctrl.Doc.CardNo}}</label>
            </div>
            <div class="card-content col">
                <label class="field-label">إسم المكتب / الفرع:- {{ctrl.Doc.ag_name}}</label>
            </div>
        </div>
        <div class="row">
            <div class="card-content col">
                <label class="field-label">عنوان المكتب / الفرع المصدر للوثيقة:- {{ctrl.Doc.Addriss}}</label>
            </div>
            <div class="card-content col">
                <label class="field-label">إسم المتعاقد:- {{ctrl.Doc.DealerName}}</label>
            </div>
        </div>
        <div class="row">
            <div class="card-content col">
                <label class="field-label">صلة المتاقد بالمؤمن له:- {{ctrl.Doc.Deal_relation}}</label>
            </div>
        </div>
        <div class="row">
            <div class="card-content col">
                <label class="field-label">إسم المؤمن له:- {{ctrl.Doc.CustName}}</label>
            </div>
            <div class="card-content col">
                <label class="field-label">الجنسية:- </label>
            </div>
            <div class="row">
                <div class="card-content col">
                    <label class="field-label">تاريخ ومكان الميلاد:- </label>
                </div>
                <div class="card-content col">
                    <label class="field-label">الحالة الإجتماعية:- </label>
                </div>
            </div>
            <div class="row">
                <div class="card-content col">
                    <label class="field-label">المهنة:- </label>
                </div>
                <div class="card-content col">
                    <label class="field-label">العنوان:- </label>
                </div>
                <div class="card-content col">
                    <label class="field-label">مكان العمل:- </label>
                </div>
            </div>
            <div class="row">
                <div class="card-content col">
                    <label class="field-label">مدة التأمين:- </label>
                </div>
                <div class="card-content col">
                    <label class="field-label">اعتبارا من يوم:- </label>
                </div>
                <div class="card-content col">
                    <label class="field-label">الى يوم:- </label>
                </div>
            </div>
    </div>
</div>
    </div>



<div class="modal fade cairo d-print-none" id="NewItem" tabindex="-1" data-bs-backdrop="static" aria-labelledby="{{ctrl.Title}}" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content gradentModelGray">
            <div class="modal-header" style="background-color:red">
                <h5 class="modal-title text-white" id="exampleModalLabel">إضافة وثيقة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body cairo " style="background-color:red">
                <form name="mForm" autocomplete="off" novalidate ng-submit="ctrl.saveMainObj()" ng-form-commit>
                    <div class="row">
                        <div class="col-8">
                            <!-- بيانات التأمين الأساسية -->
                            <fieldset class="form-section">
                                <legend class="section-title">
                                    <i class="bi bi-shield-fill"></i>
                                    بيانات التأمين الأساسية
                                </legend>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group" ng-class="{'has-error': mForm.Date_From.$invalid && (mForm.Date_From.$dirty || mForm.Date_From.$touched)}">
                                            <label class="required" for="Date_From">تاريخ البداية</label>
                                            <div class="input-group" ng-class="{'is-invalid': mForm.Date_From.$invalid && (mForm.Date_From.$dirty || mForm.Date_From.$touched), 'is-valid': mForm.Date_From.$valid && (mForm.Date_From.$dirty || mForm.Date_From.$touched)}">
                                                <span class="input-group-text"><i class="bi bi-calendar-date"></i></span>
                                                <input type="date" id="Date_From" name="Date_From" class="form-control"
                                                       ng-model="ctrl.MainObj.Date_From"
                                                       ng-change="ctrl.CalEndDate()"
                                                       required>
                                            </div>
                                            <div class="validation-message" ng-show="mForm.Date_From.$error.required && (mForm.Date_From.$dirty || mForm.Date_From.$touched)">
                                                يجب إدخال تاريخ البداية
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group" ng-class="{'has-error': mForm.Duration.$invalid && (mForm.Duration.$dirty || mForm.Duration.$touched)}">
                                            <label class="required" for="Duration">المدة</label>
                                            <div class="input-group" ng-class="{'is-invalid': mForm.Duration.$invalid && (mForm.Duration.$dirty || mForm.Duration.$touched), 'is-valid': mForm.Duration.$valid && (mForm.Duration.$dirty || mForm.Duration.$touched)}">
                                                <span class="input-group-text"><i class="bi bi-clock"></i></span>
                                                <input type="number" id="Duration" name="Duration" class="form-control"
                                                       ng-model="ctrl.MainObj.Duration"
                                                       ng-change="ctrl.CalEndDate()"
                                                       required min="1">
                                            </div>
                                            <div class="validation-message" ng-show="mForm.Duration.$error.required && (mForm.Duration.$dirty || mForm.Duration.$touched)">
                                                يجب إدخال المدة
                                            </div>
                                            <div class="validation-message" ng-show="mForm.Duration.$error.min && (mForm.Duration.$dirty || mForm.Duration.$touched)">
                                                يجب أن تكون المدة أكبر من صفر
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="Date_To">تاريخ النهاية</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-calendar-check"></i></span>
                                                <input type="date" id="Date_To" class="form-control"
                                                       ng-model="ctrl.MainObj.Date_To" disabled>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>

                            <!-- بيانات العميل -->
                            <fieldset class="form-section">
                                <legend class="section-title">
                                    <i class="bi bi-person-fill"></i>
                                    بيانات العميل
                                </legend>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group" ng-class="{'has-error': mForm.CustomerName.$invalid && (mForm.CustomerName.$dirty || mForm.CustomerName.$touched)}">
                                            <label class="required" for="CustomerName">اسم العميل</label>
                                            <div class="input-group" ng-class="{'is-invalid': mForm.CustomerName.$invalid && (mForm.CustomerName.$dirty || mForm.CustomerName.$touched), 'is-valid': mForm.CustomerName.$valid && (mForm.CustomerName.$dirty || mForm.CustomerName.$touched)}">
                                                <span class="input-group-text"><i class="bi bi-person"></i></span>
                                                <input type="text" id="CustomerName" name="CustomerName" class="form-control"
                                                       ng-model="ctrl.MainObj.CustomerName"
                                                       required
                                                       ng-minlength="3">
                                            </div>
                                            <div class="validation-message" ng-show="mForm.CustomerName.$error.required && (mForm.CustomerName.$dirty || mForm.CustomerName.$touched)">
                                                يجب إدخال اسم العميل
                                            </div>
                                            <div class="validation-message" ng-show="mForm.CustomerName.$error.minlength && (mForm.CustomerName.$dirty || mForm.CustomerName.$touched)">
                                                يجب أن يكون اسم العميل 3 أحرف على الأقل
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="required" for="DealerName">اسم الوكيل</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-person-badge"></i></span>
                                                <input type="text" id="DealerName" name="DealerName" class="form-control"
                                                       ng-model="ctrl.MainObj.DealerName" required>
                                            </div>
                                            <div class="validation-message" ng-show="mForm.DealerName.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="required" for="DealerCustomerReleation">علاقة الوكيل بالعميل</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-people"></i></span>
                                                <input type="text" id="DealerCustomerReleation" name="DealerCustomerReleation"
                                                       class="form-control" ng-model="ctrl.MainObj.DealerCustomerReleation" required>
                                            </div>
                                            <div class="validation-message" ng-show="mForm.DealerCustomerReleation.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>
                                    <!-- حقل الجنسية -->
                                    <div class="col-md-6">
                                        <div class="form-group" ng-class="{'has-error': mForm.NationalityID.$invalid && (mForm.NationalityID.$dirty || mForm.NationalityID.$touched)}">
                                            <label class="required" for="NationalityID">الجنسية</label>
                                            <md-autocomplete id="NationalityID"
                                                             md-selected-item="ctrl.NationalityID"
                                                             md-require-match="true"
                                                             md-search-text="ctrl.TxtselectedNationality"
                                                             md-items="item in ctrl.searchNationalities(ctrl.TxtselectedNationality)"
                                                             md-item-text="item.Name"
                                                             md-min-length="0"
                                                             md-selected-item-change="ctrl.SelectedNationalityChange()"
                                                             placeholder="اختر  عنوان"
                                                             ng-required="!ctrl.MainObj.IsSalary">
                                                <md-item-template>
                                                    <span>{{item.Name}}</span>
                                                </md-item-template>
                                            </md-autocomplete>
                                            <div class="validation-message" ng-show="mForm.NationalityID.$error.required && (mForm.NationalityID.$dirty || mForm.NationalityID.$touched)">
                                                يجب اختيار الجنسية
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>

                            <!-- البيانات الشخصية -->
                            <fieldset class="form-section">
                                <legend class="section-title">
                                    <i class="bi bi-person-vcard"></i>
                                    البيانات الشخصية
                                </legend>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="required" for="BirthDate">سنة الميلاد</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-calendar2-event"></i></span>
                                                <input type="number" id="BirthDate" name="BirthDate" class="form-control"
                                                       ng-model="ctrl.MainObj.BirthDate" required min="1900" max="2100" step="1">
                                            </div>
                                            <div class="validation-message" ng-show="mForm.BirthDate.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="required" for="BirthPlace">مكان الميلاد</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-geo-alt"></i></span>
                                                <input type="text" id="BirthPlace" name="BirthPlace" class="form-control"
                                                       ng-model="ctrl.MainObj.BirthPlace" required>
                                            </div>
                                            <div class="validation-message" ng-show="mForm.BirthPlace.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="required" for="AddressID">العنوان</label>

                                            <md-autocomplete id="AddressID"
                                                             md-selected-item="ctrl.AddressID"
                                                             md-require-match="true"
                                                             md-search-text="ctrl.TxtSearchAddresses"
                                                             md-items="item in ctrl.searchAddresses(ctrl.TxtSearchAddresses)"
                                                             md-item-text="item.Name"
                                                             md-min-length="0"
                                                             md-selected-item-change="ctrl.SelectedAddressesChange()"
                                                             placeholder="اختر  عنوان"
                                                             ng-required="!ctrl.MainObj.IsSalary">
                                                <md-item-template>
                                                    <span>{{item.Name}}</span>
                                                </md-item-template>
                                            </md-autocomplete>
                                            <div class="validation-message" ng-show="mForm.AddressID.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="required" for="MaritalStatusID">الحالة الاجتماعية</label>

                                            <md-autocomplete id="MaritalStatusID"
                                                             md-selected-item="ctrl.MaritalStatusID"
                                                             md-require-match="true"
                                                             md-search-text="ctrl.TXtsearchMaritalStatuses"
                                                             md-items="item in ctrl.MaritalStatuseslist(ctrl.TXtsearchMaritalStatuses)"
                                                             md-item-text="item.Name"
                                                             md-min-length="0"
                                                             md-selected-item-change="ctrl.SelectedMaritalStatusesChange()"
                                                             placeholder="اختر الحالة الاجتماعية"
                                                             ng-required="!ctrl.MainObj.IsSalary">
                                                <md-item-template>
                                                    <span>{{item.Name}}</span>
                                                </md-item-template>
                                            </md-autocomplete>
                                            <div class="validation-message" ng-show="mForm.ProfessionID.$error.required">هذا الحقل مطلوب</div>
                                            <div class="validation-message" ng-show="mForm.MaritalStatusID.$error.required && (mForm.MaritalStatusID.$dirty || mForm.MaritalStatusID.$touched)">
                                                يجب اختيار الحالة الاجتماعية
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>

                            <!-- البيانات المهنية والمالية -->
                            <fieldset class="form-section">
                                <legend class="section-title">
                                    <i class="bi bi-briefcase-fill"></i>
                                    البيانات المهنية والمالية
                                </legend>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="required" for="IsSalary">طريقة احتساب القسط</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-calculator"></i></span>
                                                <select id="IsSalary" name="IsSalary" class="form-select"
                                                        ng-model="ctrl.IsSalary"
                                                        ng-change="ctrl.SelectedIsSalaryChanged()" required>
                                                    <option value="1">مرتب</option>
                                                    <option value="0">مؤهل علمي</option>
                                                </select>
                                            </div>
                                            <div class="validation-message" ng-show="mForm.IsSalary.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="required" for="ProfessionID">المهنة</label>
                                            <md-autocomplete id="ProfessionID"
                                                             md-selected-item="ctrl.ProfessionID"
                                                             md-require-match="true"
                                                             md-search-text="ctrl.TxtSearchProffistion"
                                                             md-items="item in ctrl.searchProfessions(ctrl.TxtSearchProffistion)"
                                                             md-item-text="item.Name"
                                                             md-min-length="0"
                                                             md-selected-item-change="ctrl.SelectedProffistionChange()"
                                                             placeholder="اختر المهنة"
                                                             required>
                                                <md-item-template>
                                                    <span>{{item.Name}}</span>
                                                </md-item-template>
                                            </md-autocomplete>
                                            <div class="validation-message" ng-show="mForm.ProfessionID.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- حقول مشروطة بناءً على طريقة الاحتساب -->
                                <div class="row mt-3" ng-if="ctrl.MainObj.IsSalary">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="required" for="Salary">قيمة المرتب</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-cash"></i></span>
                                                <input type="number" id="Salary" name="Salary" class="form-control"
                                                       ng-model="ctrl.MainObj.Salary"
                                                       ng-change="ctrl.St_CalIns()" required>
                                            </div>
                                            <div class="validation-message" ng-show="mForm.Salary.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>

                                    <div class="col-md-6" ng-if="ctrl.MainObj.IsFreePer">
                                        <div class="form-group">
                                            <label class="required" for="Percentage">النسبة المئوية</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-percent"></i></span>
                                                <input type="number" id="Percentage" name="Percentage" class="form-control"
                                                       ng-model="ctrl.MainObj.Percentage"
                                                       ng-change="ctrl.St_CalIns()" ng-required="ctrl.MainObj.IsFreePer">
                                            </div>
                                            <div class="validation-message" ng-show="mForm.Percentage.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>

                                    <div class="col-md-6" ng-if="!ctrl.MainObj.IsFreePer">
                                        <div class="form-group">
                                            <label class="required" for="PerValue">النسبة</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-percent"></i></span>
                                                <select id="PerValue" name="PerValue" class="form-select"
                                                        ng-options="x.Value as x.Name for x in ctrl.PercentageList"
                                                        ng-model="ctrl.PerValue"
                                                        ng-change="ctrl.SelectedPerValueChanged()" ng-required="!ctrl.MainObj.IsFreePer">
                                                </select>
                                            </div>
                                            <div class="validation-message" ng-show="mForm.PerValue.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mt-3" ng-if="!ctrl.MainObj.IsSalary">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="required" for="AcademicQualificationID">المؤهل العلمي</label>

                                            <md-autocomplete id="AcademicQualificationID"
                                                             md-selected-item="ctrl.AcademicQualificationID"
                                                             md-require-match="true"
                                                             md-search-text="ctrl.TxtSearchAcademicQualification"
                                                             md-items="item in ctrl.searchAcademicQualifications(ctrl.TxtSearchAcademicQualification)"
                                                             md-item-text="item.Name"
                                                             md-min-length="0"
                                                             md-selected-item-change="ctrl.SelectedAcademicQualificationChange()"
                                                             placeholder="اختر  المؤهل العلمي"
                                                             ng-required="!ctrl.MainObj.IsSalary">
                                                <md-item-template>
                                                    <span>{{item.Name}}</span>
                                                </md-item-template>
                                            </md-autocomplete>
                                            <div class="validation-message" ng-show="mForm.AcademicQualificationID.$error.required">هذا الحقل مطلوب</div>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>

                            <!-- بيانات المطبوعة -->
                            <fieldset class="form-section">
                                <legend class="section-title">
                                    <i class="bi bi-file-text"></i>
                                    بيانات المطبوعة
                                </legend>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="required" for="pap_no">رقم المطبوعة</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-upc"></i></span>
                                                <input type="number" id="pap_no" name="pap_no" class="form-control text-center"
                                                       ng-model="ctrl.MainObj.pap_no"
                                                       ng-click="ctrl.IsExistPayperNum = 0"
                                                       min="0" required>
                                            </div>
                                            <div class="validation-message" ng-show="mForm.pap_no.$error.required">هذا الحقل مطلوب</div>
                                            <div class="text-danger mt-2" ng-if="ctrl.IsExistPayperNum == 1">
                                                الرقم غير صحيح
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>
                        </div>

                        <!-- جانب احتساب القسط -->
                        <div class="col-4">
                            <div class="calculation-panel">
                                <div class="calculation-header">
                                    <i class="bi bi-calculator-fill"></i>
                                    احتساب القسط
                                </div>

                                <div class="calculation-items">
                                    <div class="calc-item">
                                        <div class="calc-label">صافي القسط</div>
                                        <div class="calc-value">{{ctrl.MainObj.installment | number:'2'}}</div>
                                    </div>

                                    <div class="calc-item">
                                        <div class="calc-label">الدمغة</div>
                                        <div class="calc-value">{{ctrl.MainObj.Stamp | number:'2'}}</div>
                                    </div>

                                    <div class="calc-item">
                                        <div class="calc-label">الضريبة</div>
                                        <div class="calc-value">{{ctrl.MainObj.Tax | number:'2'}}</div>
                                    </div>

                                    <div class="calc-item">
                                        <div class="calc-label">رسوم إشراف</div>
                                        <div class="calc-value">{{ctrl.MainObj.Supervision | number:'2'}}</div>
                                    </div>

                                    <div class="calc-item">
                                        <div class="calc-label">م.الإصدار</div>
                                        <div class="calc-value">{{ctrl.MainObj.Version | number:'2'}}</div>
                                    </div>

                                    <div class="calc-total">
                                        <div class="total-label">المجموع</div>
                                        <div class="total-value">{{ctrl.MainObj.Total | number:'2'}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer justify-content-center" style="background-color:red">
                <button type="button" class="btn btn-success col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" ng-disabled="mForm.$invalid" ng-click="ctrl.a600015()">
                    حفظ
                </button>
                <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">إلغاء الأمر</button>
            </div>
            @*<div class="modal-footer justify-content-center" ng-if="ctrl.showhFlag==2">
                    <button type="button" class="btn btn-success col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" ng-disabled="mForm.$invalid" ng-click="ctrl.a40008()">
                        حفظ كجديد
                    </button>
                    <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">إلغاء الأمر</button>
                </div>*@
        </div>
    </div>
</div>








