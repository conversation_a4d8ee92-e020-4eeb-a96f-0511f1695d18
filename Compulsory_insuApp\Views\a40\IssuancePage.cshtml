<link href="~/Content/CSS/bootstrap.rtl.min.css??v=@DateTime.Now.Ticks" rel="stylesheet" />
<link href="~/Content/InssunaceStyle.css??v=@DateTime.Now.Ticks" rel="stylesheet" media="screen" />
<link href="~/Content/A4PageCss.css?v=@DateTime.Now.Ticks" rel="stylesheet" media="print" />
<link href="~/Content/Cairo.css?v=?v=@DateTime.Now.Ticks" rel="stylesheet" />
<link href="~/Content/CSS/all.min.css?v=?v=@DateTime.Now.Ticks" rel="stylesheet" />
<link href="~/Content/A4PageCss.css?v=@DateTime.Now.Ticks" rel="stylesheet" media="print" />
@{
    if (ViewBag.CompID == 1)
    {
        Html.RenderPartial("_EthPrint");
    }
    if (ViewBag.CompID == 2)
    {
        Html.RenderPartial("_wikPrint");
    } 
    if (ViewBag.CompID == 3)
    {
        Html.RenderPartial("alasimaprint");
    }
}

<div class="row justify-content-center d-print-none  " ng-if="ctrl.switchFlag==1" style="background-color:white">

    <div class="col-lg-12   col-md-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12" style="background-color:white">
        <div class="card  navglassColor ">
            <div class="card-header" style="background-color:white">

                <div class="btn-group cairo" dir="ltr" role="group">
                    <button type="button" disabled class="btn btn-primary border-ridues  cairo "> واجهة إصدار وثيقة تامين إجباري</button>
                    <button type="button" ng-click="ctrl.BtnHomePage()" class="btn btn-success bi bi-house-fill"> <span class="m-2">الرئيسية</span></button>
                </div>
                <div class="col-auto float-end">
                    <button class="btn btn-primary btn-lg border-ridues  cairo " ng-if="ctrl.AgPerObj.Cump_AdditionalServices" ng-click="ctrl.a400030()">خدمات إضافية</button>
                    <button class="btn btn-primary btn-lg border-ridues  cairo " ng-if="ctrl.AgPerObj.Cump_OldInterface" ng-click="ctrl.a4000311()"> واجهة بيانات المنظومة القديمة </button>
                </div>
            </div>


        </div>

        <div class="bg-light">


            <div class="row  ">
                <div class="col-sm-12 col-xs-12  offset-1 col-md-5 col-lg-5 col-xl-5 col-xxl-5">

                    <input class="inputStyle cairo " type="text" ng-model="search" placeholder="   بحث...   ">

                </div>
                <div class="col-sm-12 col-xs-12 col-md-3 col-lg-3 col-xl-3 col-xxl-3 float-end ">

                    <button type="button"
                            class="btn btn-primary border-ridues  cairo float-end " style="width:50%;margin:25px 0 0;"
                            data-bs-toggle="modal" data-bs-target="#NewItem" ng-click="ctrl.AddNew()">
                        إصدار وثيقة جديدة
                    </button>

                </div>


            </div>

            <div class="row  mt-4  " style="height:80vh; padding:25px;overflow-y:scroll">
                <div class="table-responsive cairo ">
                    <table class="table     table-hover">
                        <tr class=" text-white" style="background-color:cornflowerblue">
                            <th class="text-center">#</th>
                            <th class="text-center">نوع الوثيقة</th>
                            <th class="text-center">  رقم الوثيقة </th>
                            <th class="text-center">  المؤمن له </th>
                            <th class="text-center">  السيارة </th>
                            <th class="text-center">  رقم اللوحة </th>
                            <th class="text-center">  اللون   </th>
                            <th class="text-center"> القسط </th>
                            <th class="text-center"> الضرائب </th>
                            <th class="text-center"> الإجمالي </th>
                            <th class="text-center">تاريخ الإدخال </th>
                            <th class="text-center">  المدخل </th>
                            <th class="text-center" style="margin:0" colspan="3">العمليات</th>
                        </tr>
                        <tr class="tableStaylebody p-0 text-dark" ng-repeat="x in ctrl.InsObj |filter:search " style="background-color:white">
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{$index + 1}}</td>
                            <td class="text-center">{{x.TypeDesc}}</td>
                            <td class="text-center " dir="ltr" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">
                                {{x.Ins_SN}}
                            </td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.c_name}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.car_name}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.iron_bord}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.color}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.ins_val| number:'2'}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.TaxVal| number:'2'}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.total_tax | number:'2'}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.InsertDate |date : "yyyy-MM-dd  a hh:mm" }}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.InsertBy}}</td>

                            <td ng-hide="x.IsDelProg">
                                <button title="معاينة الوثيقة" type="button" class="btn btn-dark " ng-click="ctrl.prview(x)">
                                    <i class="bi bi-eye-fill"></i>
                                </button>
                            </td>

                            <td colspan="2" ng-show="x.IsDelProg">
                                <button title="التعليقات ({{x.NotCount}})" type="button" class="btn btn-secondary " ng-click="ctrl.prview(x)">
                                    <i class="bi bi-eye-fill"></i>
                                </button>
                            </td>

                            <td ng-hide="x.IsDelProg">
                                <button title="حذف الوثيقة" type="button" class="btn btn-danger" ng-click="ctrl.BtnStatus(x)">
                                    <i class="bi bi-trash-fill" aria-hidden="true"></i>
                                </button>
                            </td>

                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row justify-content-center d-print-none  " ng-if="ctrl.switchFlag==2">

    <div class="col-lg-12   col-md-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12">
        <div class="card  navglassColor ">
            <div class="card-header gradientModelBackg ">

                <div class="btn-group cairo" role="group">
                    <button type="button" ng-click="ctrl.BtnHomePage()" class="btn btn-success bi bi-house-fill"> <span class="m-2">الرئيسية</span></button>
                    <button type="button" disabled class="btn btn-primary">  معاينة وثيقة تامين إجباري</button>
                    <button type="button" ng-click="ctrl.BackOneStep()" class="btn btn-secondary bi bi-arrow-right"> <span class="m-2">عودة</span></button>
                </div>

                @{
                    if (ViewBag.CompID == 1)
                    {
                        Html.RenderPartial("_EthPrint_button");
                    }
                    if (ViewBag.CompID == 2)
                    {
                        Html.RenderPartial("_wikPrint_button");
                    }
                    if (ViewBag.CompID == 3)
                    {
                        Html.RenderPartial("alasimaprint_button");
                    }
                }

            </div>


        </div>

        <div class="row    ">
            <div class="col-9 mt-2">
                <table class="table cairo  ">

                    <tbody>
                        <tr class="border border-1 cairo" style="margin-top:20px !important">
                            <th class="border border-1" scope="col">رقم الوثيقة</th>
                            <th scope="col" style="direction:ltr">{{ ctrl.printObj.Ins_SN}} </th>

                        </tr>
                        <tr class="border border-1">
                            <th>  اسم المكتب / الفرع</th>
                            <th class="border border-1">{{ctrl.printObj.Agency}} </th>
                            <th>وعنوانه   </th>
                            <th class="border border-1">{{ctrl.printObj.agencyCityAddres}} </th>
                        </tr>

                        <tr class="border border-1">
                            <th class="col-4">      مدة التأمين من ظهر  </th>
                            <th class="col-3 border border-1">{{ctrl.printObj.f_date |date : "yyyy-MM-dd"}} </th>
                            <th class="col-2">إلى ظهر    </th>
                            <th class=" col-3 border border-1">{{ctrl.printObj.to_date |date : "yyyy-MM-dd"}} </th>
                        </tr>
                        <tr class="border border-1">
                            <th class="border border-1" scope="col">أسم المؤمن له  </th>
                            <th scope="col">{{ctrl.printObj.c_name}} </th>

                        </tr>
                        <tr class="border border-1">
                            <th class="col-4"> العنوان  </th>
                            <th class="col-3 border border-1">{{ctrl.printObj.Address}} </th>
                            <th class="col-2">رقم الهاتف    </th>
                            <th class=" col-3 border border-1">{{ctrl.printObj.PhoneNum}} </th>
                        </tr>
                        <tr>
                            <th colspan="4">بيانات المركبة  </th>


                        </tr>
                        <tr class="border border-1 cairo">
                            <th class="border border-1" scope="col">رقم اللوحة المعدنية</th>
                            <th colspan="3">{{ ctrl.printObj.iron_bord}} </th>

                        </tr>
                        <tr class="border border-1 cairo">
                            <th>  النوع      </th>
                            <th class="border border-1">{{ctrl.printObj.car_name}} </th>
                            <th>بلد الصنع     </th>
                            <th class="border border-1">{{ctrl.printObj.contry}} </th>
                        </tr>

                        <tr class="border border-1 cairo">
                            <th>  قوة المحرك بالحصان      </th>
                            <th class="border border-1">{{ctrl.printObj.Engine_Capacity}} </th>
                            <th> اللون     </th>
                            <th class="border border-1">{{ctrl.printObj.color}} </th>
                        </tr>


                    </tbody>
                </table>
                <table class="table margin_negitiv  cairo ">
                    <tbody>
                        <tr class="border border-1">
                            <th class="col-3">      الحمولة بالطن      </th>
                            <th class="col-1 border border-1">{{ctrl.printObj.PayLoad}} </th>
                            <th class="col-5"> عدد الركاب المصرح لهم بخلاف السائق     </th>
                            <th class=" col-2 border border-1">{{ctrl.printObj.NomOfPassengers}} </th>
                        </tr>

                    </tbody>
                </table>
                <table class="table margin_negitiv cairo  ">
                    <tbody>
                        <tr class="border border-1">
                            <th class="col-2">  سنة   الصنع      </th>
                            <th class=" col-1 border border-1">{{ctrl.printObj.Manufacturing_Year}} </th>
                            <th class="col-3"> الغرض من الترخيص     </th>
                            <th class=" col-5 border border-1">{{ctrl.printObj.mas_name}} </th>
                        </tr>

                    </tbody>
                </table>
                <table class="table margin_negitiv cairo">
                    <tbody>
                        <tr class="border border-1">
                            <th class="col-3">  الجهة المقيد بها   </th>
                            <th class=" col-2 border border-1">{{ctrl.printObj.InsuAdress}} </th>
                            <th class="col-3">     رقم المحرك     </th>
                            <th class=" col-4 border border-1">{{ctrl.printObj.Engine_number}} </th>
                        </tr>

                    </tbody>
                </table>
                <table class="table margin_negitiv cairo">
                    <tbody>
                        <tr class="border border-1">
                            <th class="col-4  border border-1" scope="col">رقم الهيكل</th>
                            <th class="col-8">{{ ctrl.printObj.Chassis_number}} </th>

                        </tr>

                    </tbody>
                </table>

                <table class="table margin_negitiv cairo" style=" border: 1px #fff solid !important">
                    <tr>
                        <th class="col text-start"> القسط طبقا للبند رقم  </th>
                        <th class="col text-center">1   </th>
                        <th class="col text-center"> من التعريفة المقررة  </th>



                    </tr>
                </table>

                <table class="table  cairo   border border-1 margin_negitiv">
                    <tbody>
                        <tr class="border border-1">
                            <th class="col border border-1">  صافي القسط     </th>
                            <th class=" col border border-1"> الضريبة  </th>
                            <th class=" col border border-1"> الدمغة </th>
                            <th class=" col border border-1">الاشراف  </th>
                            <th class=" col border border-1"> م.الاصدار </th>
                            <th class=" col border border-1">   الاجمالي  (د.ل)  </th>
                        </tr>
                        <tr class="border border-1">
                            <th class="col ">  {{ ctrl.printObj.ins_val  |number:'2'}}      </th>
                            <th class=" col">  {{ ctrl.printObj.Tax  |number:'2'}}     </th>
                            <th class=" col">  {{ ctrl.printObj.Tax_stamp  |number:'2'}}     </th>
                            <th class=" col">  {{ ctrl.printObj.Tax_supervision |number:'2'}}    </th>
                            <th class=" col">  {{ ctrl.printObj.Tax_insu  |number:'2'}}    </th>
                            <th class=" col">  {{ ctrl.printObj.total_tax | number:'2'}}    </th>

                        </tr>
                    </tbody>
                </table>
                <table class="table margin_negitiv cairo" style=" border: 1px #fff solid !important">
                    <tr>
                        <th class="col text-start">       الرقم بالحروف  </th>




                    </tr>
                </table>





            </div>

            <div class="col-3 text-center">
                <table class="table margin_negitiv   " style=" border: 1px #fff solid !important">
                    <tbody>
                        <tr>
                            <th class="col-4  ">
                                <qrcode data="{{ctrl.printObj.Ins_SN}}" class="" size="100" href="http://example.com"></qrcode>
                            </th>


                        </tr>

                    </tbody>

                </table>

                <table class="table margin_negitiv cairo  " style=" border: 1px #fff solid !important">
                    <tbody>
                        <tr>
                            <th class="col text-start"> تاريخ الاصدار :  {{ctrl.printObj.InsertDate |date : "dd-MM-yyyy"}}  </th>
                            <th class="col text-center">    الساعة : {{ctrl.printObj.InsertDate |date : "a hh:mm"}}  </th>

                        </tr>

                    </tbody>

                </table>
            </div>
        </div>


    </div>
</div>









<div class="row justify-content-center d-print-none  " ng-if="ctrl.switchFlag==5">

    <div class="col-lg-12   col-md-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12">
        <div class="card  navglassColor ">
            <div class="card-header ">

                <div class="btn-group cairo" dir="ltr" role="group">
                    <button type="button" disabled class="btn btn-primary">واجهة بيانات المنظومة  القديمة </button>
                    <button type="button" ng-click="ctrl.Back()" class="btn btn-primary "> <span class="m-2">واجهة إصدار وثيقة تامين إجباري</span></button>
                    <button type="button" ng-click="ctrl.BtnHomePage()" class="btn btn-success bi bi-house-fill"> <span class="m-2">الرئيسية</span></button>
                </div>
                <div class="col-3  float-end ">
                    <button type="button" ng-click="ctrl.RepleacOldDoc()" class="btn btn-primary  border-ridues  cairo   " style="width:100%"> تغير ملكية </button>

                </div>
            </div>


        </div>

        <div class="  ">


            <div class="row    ">
                <div class="col-8 mt-2">

                </div>


                <div class="col-1 mt-2">

                </div>

            </div>
            <div class="row  mt-4 " style="height:80vh; padding:25px;overflow-y:scroll">
                <div class="col-12  ">
                
                    <table class="table table-success table-bordered table-hover">
                        <tr class="bg-primary  text-white">
                            <th class="text-center">#</th>
                            <th class="text-center">   نوع الوثيقة   </th>
                            <th class="text-center">  رقم الوثيقة السابقة </th>
                            <th class="text-center">    رقم الوثيقة الجديدة  </th>
                            <th class="text-center">     اسم المؤمن له     </th>
                            <th class="text-center">         نوع السيارة     </th>
                            <th class="text-center">تاريخ الإدخال </th>
                            <th class="text-center">  المدخل </th>
                        </tr>
                        <tr class="  p-0" ng-repeat="x in ctrl.OldOWner">
                            <td class="text-center">{{$index + 1}}</td>
                            <td class="text-center">{{x.DocType==4? 'وثيقة من النظام السابق'  :'' }}</td>
                            <td class="text-center " dir="ltr">
                                {{x.OldDocSn}}
                            </td>

                            <td class="text-center">{{x.NewDocSN}}</td>
                            <td class="text-center">{{x.cusNume}}</td>
                            <td class="text-center">{{x.carName}}</td>
                            <td class="text-center">{{x.InsertDate}}</td>
                            <td class="text-center">{{x.InsertBy}}</td>






                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
















<div class="modal fade cairo" id="NewItem" tabindex="-1" data-bs-backdrop="static" aria-labelledby="{{ctrl.Title}}" aria-hidden="true">
    <div class="modal-dialog modal-xl 	">
        <div class="modal-content gradentModelGray">
            <div class="modal-header text-light bg-primary">
                <h5 class="modal-title" id="exampleModalLabel">{{ctrl.DlTitle}}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body cairo bg-primary">
                <form name="mForm" autocomplete="off" novalidate>
                    <div class="row bg-primary">
                        <div class="col-9 bg-primary">

                            <fieldset class="row text-white mt-1 gradientModelBackg" style="border:1px solid #808080">
                                <legend class="fs-5">التأمين</legend>


                                <div class="col-4">
                                    <label for="Com_ID"> الجهة <span ng-show="mForm.Com_ID.$error.required" class="text-danger">   * </span>  </label>

                                    <md-autocomplete flex required
                                                     md-require-match="true"
                                                     md-search-text="ctrl.Com_Name"
                                                     md-input-name="Com_ID"
                                                     md-selected-item="ctrl.selected_Comp_ID"
                                                     md-no-cache="true"
                                                     ng-style=" mForm.Com_ID.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}"
                                                     md-selected-item-change="ctrl.SelectedCompanies()" md-items="item in ctrl.SearchCompanies(ctrl.Com_Name)"
                                                     md-item-text="item.Name" md-min-length="0" placeholder="" input-aria-labelledby="cityName">
                                        <md-item-template>
                                            <span md-highlight-text="ctrl.Com_Name" md-highlight-flags="^i">{{item.Name}}</span>
                                        </md-item-template>
                                        <md-not-found>
                                            العنصر     "{{ctrl.Com_Name}}"  غير موجود.

                                        </md-not-found>
                                    </md-autocomplete>

                                </div>
                                <div class="col-4 cairo">
                                    <label for="Ins_Type"> نوع التأمين  <span ng-show="mForm.Ins_Type.$error.required" class="text-danger float-end ">*</span></label>
                                    <select required form-control name="Ins_Type" class="form-select text-center " ng-model="ctrl.Ins_Type" ng-change="ctrl.a400015()"
                                            ng-style="mForm.Ins_Type.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                            ng-options="x.ID as x.Name for x in ctrl.ct_li"></select>



                                </div>
                                <div class="col-4 cairo">
                                    <label for="Ins_Type"> مدة التأمين  <span ng-show="mForm.Dur_Type.$error.required" class="text-danger float-end ">*</span></label>
                                    <select required form-control name="Dur_Type" class="form-select text-center " ng-model="ctrl.Dur_Type" ng-change="ctrl.a400020()"
                                            ng-style="mForm.Dur_Type.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                            ng-options="x.ID as x.Name for x in ctrl.DurTypes"></select>



                                </div>
                                <div class="col-4 cairo ">
                                    <label for="Sdate">تاريخ بداية التأمين  <span ng-show="mForm.Sdate.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required name="Sdate" type="date" disabled readonly
                                           ng-style="mForm.Sdate.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj.Sdate" class="form-control text-center" />



                                </div>
                                <div class="col-4 cairo ">
                                    <label for="Edate"> تاريخ نهاية التأمين    <span ng-show="mForm.Edate.$error.required" class="text-danger float-end ">*</span></label>
                                    <input required name="Edate" type="date" disabled readonly
                                           ng-style="mForm.Edate.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj.Edate" class="form-control text-center" />


                                </div>

                                <div class="col-4 cairo ">
                                    <label for="c_masterID">الغرض من الترخيص  <span ng-show="mForm.c_masterID.$error.required" class="text-danger float-end  ">*</span></label>
                                    <select required name="c_masterID" class="form-select text-center" ng-change="ctrl.a40005()"
                                            ng-style="mForm.c_masterID.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                            ng-model="ctrl.MainObj.c_masterID"
                                            ng-options="x.ID as x.Name for x in ctrl.MasterObj"></select>

                                </div>
                                <div class="col-4 cairo ">
                                    <label for="c_DetailsID">مواصفات السيارة <span ng-if="mForm.c_DetailsID.$error.required" class="text-danger float-end  ">*</span></label>
                                    <select required name="c_DetailsID" class="form-select text-center" ng-change="ctrl.a40006()"
                                            ng-style="mForm.c_DetailsID.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                            ng-model="ctrl.Selected_c_DetailsID"
                                            ng-options="x.ID as x.Name for x in ctrl.PricDeObj"></select>



                                </div>
                                <div class="col-2 cairo">

                                    <label for="NoOfPassen">
                                        عدد الركاب   <span ng-if="mForm.NoOfPassen.$error.required" class="text-danger float-end  ">*</span>
                                    </label>
                                    <input required name="NoOfPassen" type="number" min="{{ctrl.Selected_c_DetailsID.MinNoPass}}" max="{{ctrl.Selected_c_DetailsID.MaxNoPass}}"
                                           ng-style="mForm.NoOfPassen.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj.NoOfPassen" class="form-control text-center" ng-change="ctrl.a400016()">
                                    <span ng-show="mForm.NoOfPassen.$error.max" class="text-danger float-end m-2">أقصى قيمة {{ctrl.Selected_c_DetailsID.MaxNoPass}}</span>
                                    <span ng-show="mForm.NoOfPassen.$error.min" class="text-danger float-end m-2">اقل قيمة {{ctrl.Selected_c_DetailsID.MinNoPass}}</span>

                                </div>
                                <div class="col-2 ">
                                    <label for="PassPrice"> سعر الراكب  <span ng-show="mForm.PassPrice.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required name="PassPrice" type="number" min="0" disabled readonly
                                           ng-style="mForm.PassPrice.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj.PassPrice" class="form-control text-center" />


                                </div>
                                <div class="col-4 mb-2">

                                    <label for="c_load">الحمولة <span ng-show="mForm.c_load.$error.required" class="text-danger float-end ">*</span></label>
                                    <input required readonly name="c_load" type="number" min="0"
                                           ng-style="mForm.c_load.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj.c_load" max="" class="form-control text-center">


                                    <span ng-show="mForm.c_load.$error.max" class="text-danger float-end m-2">خطأ</span>

                                </div>

                            </fieldset>


                            <fieldset class="row mt-2 mb-2 gradientModelBackg text-white" style="border:1px solid #808080">

                                <legend class="fs-5">  المؤمن له </legend>
                                <div class="col-4">
                                    <label for="city_ID"> جهة الترخيص <span ng-if="mForm.city_ID.$error.required" class="text-danger">   * </span>  </label>

                                    <md-autocomplete flex required
                                                     md-require-match="true"
                                                     md-search-text="ctrl.searchCity"
                                                     md-input-name="city_ID"
                                                     md-selected-item="ctrl.selected_city_license_Item"
                                                     md-no-cache="true"
                                                     md-input-minlength="1"
                                                     md-input-maxlength="18"
                                                     ng-style=" mForm.city_ID.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}"
                                                     md-selected-item-change="ctrl.Selected_City_Item(item)" md-items="item in ctrl.SearchCity(ctrl.searchCity)"
                                                     md-item-text="item.Name" placeholder="قم بإدخال اول حرف وإختر من القائمة" input-aria-labelledby="cityName">
                                        <md-item-template>
                                            <span md-highlight-text="ctrl.searchCity" md-highlight-flags="^i">{{item.Name}}</span>
                                        </md-item-template>
                                        <md-not-found>
                                            العنصر     "{{ctrl.searchCity}}"  غير موجود.

                                        </md-not-found>
                                    </md-autocomplete>

                                </div>
                                <div class="col-4">
                                    <label for="c_Name"> اسم الزبون  <span ng-show="mForm.c_Name.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required ng-disabled="ctrl.selected_Comp_ID.IsDisabed" name="c_Name" type="text"
                                           ng-style="mForm.c_Name.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj.c_Name" class="form-control">

                                </div>

                                <div class="col-4">
                                    <label for="c_Address">العنوان <span ng-if="mForm.c_Address.$error.required" class="text-danger">   * </span>  </label>

                                    <md-autocomplete flex required
                                                     md-require-match="true"
                                                     md-search-text="ctrl.SearchCity1"
                                                     md-input-name="c_Address"
                                                     md-selected-item="ctrl.c_Address"
                                                     md-no-cache="true"
                                                     md-input-minlength="1"
                                                     md-input-maxlength="18"
                                                     ng-style=" mForm.c_Address.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}"
                                                     md-selected-item-change="ctrl.Selected_City_Item_Addres()" md-items="item in ctrl.SearchCityAddres(ctrl.SearchCity1)"
                                                     md-item-text="item.Name" placeholder="قم بإدخال اول حرف وإختر من القائمة" input-aria-labelledby="c_Address">
                                        <md-item-template>
                                            <span md-highlight-text="ctrl.SearchCity1" md-highlight-flags="^i">{{item.Name}}</span>
                                        </md-item-template>
                                        <md-not-found>
                                            العنصر     "{{ctrl.searchCity1}}"  غير موجود.

                                        </md-not-found>
                                    </md-autocomplete>

                                </div>
                                <div class="col-4">
                                    <label for="c_Address">العنوان <span ng-show="mForm.c_Address.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required name="c_Address" type="text"
                                           ng-style="mForm.c_Address.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj.c_Address" class="form-control ">

                                </div>

                                <div class="col-4 mb-2">
                                    <label for="c_Address">رقم الهاتف <span ng-show="mForm.c_phone.$error.required || mForm.c_phone.$error.c_phone" class="text-danger float-end ">*</span></label>
                                    <input required name="c_phone" type="number" @*pattern="^(?:0|\(?\+33\)?\s?|0033\s?)[1-79](?:[\.\-\s]?\d\d){4}$"*@
                                           ng-style="mForm.c_phone.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj.c_phone" class="form-control ">

                                </div>

                            </fieldset>
                            <fieldset class="row mt-2 text-white gradientModelBackg " style="border:1px solid #808080">

                                <legend class="fs-5">    بيانات المركبة :</legend>


                                <div class="col-4">
                                    <label for="Ib_num">  رقم  اللوحة المعدنية  <span ng-show="mForm.Ib_num.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required name="Ib_num" type="text"
                                           ng-style="mForm.Ib_num.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           pattern="^[0-9-]+$" ng-model="ctrl.MainObj.Ib_num" class="form-control ">

                                </div>


                                <div class="col-4">
                                    <label for="car_ID">نوع السيارة  <span ng-show="mForm.car_ID.$error.required" class="text-danger">   * </span>  </label>

                                    <md-autocomplete flex required
                                                     md-require-match="true"
                                                     md-search-text="ctrl.searchCar"
                                                     md-selected-item="ctrl.selected_Car_Brand_Item"
                                                     md-input-name="car_ID"
                                                     md-no-cache="true"
                                                     md-input-minlength="1"
                                                     md-input-maxlength="18"
                                                     ng-style=" mForm.car_ID.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}"
                                                     md-selected-item-change="ctrl.SelectedCarChange(item)" md-items="item in ctrl.SearchCar(ctrl.searchCar)"
                                                     md-item-text="item.Name" placeholder="قم بإدخال اول حرف وإختر من القائمة" input-aria-labelledby="Name">
                                        <md-item-template>
                                            <span md-highlight-text="ctrl.searchCar" md-highlight-flags="^i">{{item.Name}}</span>
                                        </md-item-template>
                                        <md-not-found>
                                            العنصر     "{{ctrl.searchCar}}"  غير موجود.

                                        </md-not-found>
                                    </md-autocomplete>

                                </div>
                                <div class="col-4">
                                    <label for="car_color">لون السيارة  <span ng-if="mForm.car_color.$error.required" class="text-danger">   * </span>  </label>

                                    <md-autocomplete flex required
                                                     md-search-text="ctrl.searchColor"
                                                     md-input-name="car_color"
                                                     md-no-cache="true"
                                                     md-require-match="true"
                                                     md-input-minlength="1"
                                                     md-input-maxlength="18"
                                                     md-selected-item="ctrl.selected_Car_Color_Item"
                                                     ng-style=" mForm.car_color.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}"
                                                     md-selected-item-change="ctrl.SelectedColorChange(item)" md-items="item in ctrl.SearchColor(ctrl.searchColor)"
                                                     md-item-text="item.Name" md-min-length="0" placeholder="قم بإدخال اول حرف وإختر من القائمة" input-aria-labelledby="Name">
                                        <md-item-template>
                                            <span md-highlight-text="ctrl.searchColor" md-highlight-flags="^i">{{item.Name}}</span>
                                        </md-item-template>
                                        <md-not-found>
                                            العنصر     "{{ctrl.searchColor}}"  غير موجود.

                                        </md-not-found>
                                    </md-autocomplete>

                                </div>

                                <div class="col-4">
                                    <label for="chass_num"> رقم الهيكل <span ng-show="mForm.chass_num.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required name="chass_num" type="text"
                                           ng-style="mForm.chass_num.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj.chass_num" class="form-control ">


                                </div>
                                <div class="col-4">
                                    <label for="Eng_num"> رقم المحرك <span ng-show="mForm.chass_num.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required name="Eng_num" type="text"
                                           ng-style="mForm.Eng_num.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj.Eng_num" class="form-control ">


                                </div>
                                <div class="col-4">

                                    <label for="eng_cap">قوة المحرك  <span ng-show="mForm.eng_cap.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required name="eng_cap" type="number"
                                           ng-style="mForm.eng_cap.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj.eng_cap" max="{{ctrl.Selected_c_DetailsID.MaxCap_eng}}" min="{{ctrl.Selected_c_DetailsID.MinCap_eng}}"
                                           class="form-control text-center">

                                    <span ng-show="mForm.eng_cap.$error.max" class="text-danger float-end m-2">أقصى قيمة {{ctrl.Selected_c_DetailsID.MaxCap_eng}}</span>
                                    <span ng-show="mForm.eng_cap.$error.min" class="text-danger float-end m-2">أقل قيمة {{ctrl.Selected_c_DetailsID.MinCap_eng}}</span>



                                </div>



                                <div class="col-4 ">
                                    <label for="m_year">سنة الصنع <span ng-show="mForm.m_year.$error.required" class="text-danger float-end  ">*</span></label>
                                    <select required name="m_year" class="form-select text-center"
                                            ng-style="mForm.m_year.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                            ng-model="ctrl.MainObj.m_year"
                                            ng-options="x as x for x in ctrl.m_Years|orderBy:'-'"></select>
                                </div>
                                <div class="col-4">
                                    <label for="cityName">بلد الصنع  <span ng-show="mForm.con_ID.$error.required" class="text-danger">   * </span>  </label>

                                    <md-autocomplete flex required
                                                     md-require-match="true"
                                                     md-search-text="ctrl.searchContory"
                                                     md-selected-item="ctrl.selected_manfictor_Item"
                                                     md-input-name="con_ID"
                                                     md-no-cache="true"
                                                     md-input-minlength="1"
                                                     md-input-maxlength="18"
                                                     ng-style=" mForm.con_ID.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}"
                                                     md-selected-item-change="ctrl.selectedContryChange(item)" md-items="item in ctrl.SearchContory(ctrl.searchContory)"
                                                     md-item-text="item.Name" placeholder="قم بإذخال اول حرف وإختر من القائمة" input-aria-labelledby="Name">
                                        <md-item-template>
                                            <span md-highlight-text="ctrl.searchContory" md-highlight-flags="^i">{{item.Name}}</span>
                                        </md-item-template>
                                        <md-not-found>
                                            العنصر     "{{ctrl.searchContory}}"  غير موجود.

                                        </md-not-found>
                                    </md-autocomplete>

                                </div>


                                <div class="col-4">

                                    <label for="pap_no">رقم المطبوعة<span ng-if="mForm.pap_no.$error.required" class="text-danger float-end ">*</span></label>
                                    <input required name="pap_no" type="number" min="0" ng-click="ctrl.IsExistPayperNum = 0"
                                           ng-style="mForm.pap_no.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj.pap_no" max="" class="form-control text-center">
                                    <span ng-if="ctrl.IsExistPayperNum == 1" class="text-danger float-end m-2">
                                        الرقم غير
                                        صحيح
                                    </span>


                                </div>
                            </fieldset>





                        </div>
                        <div class="col-3">

                            <fieldset class="row g-1 justify-content-center text-center text-white" style="border: 1px solid #808080; height: 75vh">

                                <legend class="fs-5">    إحتساب القسط   :</legend>
                                <label for="fname" class="col-12">صافي القسط</label>
                                <label style="height:50px" class="bg-dark border-1 border-white col-12 text-white text-center"
                                       ng-model="ctrl.NetPri" name="NetPri">{{ctrl.NetPri | number:'2'}}</label><br>
                                <label for="fname" class="col-12">الدمغة</label>
                                <label style="height:50px" class="bg-dark border-1 border-danger col-12 text-white text-center"
                                       name="NetPri">{{ctrl.t_Stamp | number:'2'}}</label><br>
                                <label for="fname" class="col-12">الضريبة</label>
                                <label style="height:50px" class="bg-dark border-1 border-danger col-12 text-white text-center"
                                       name="NetPri">{{ctrl.t_Tax | number:'2'}}</label><br>
                                <label for="fname" class="col-12">رسوم إشراف</label>
                                <label style="height:50px" class="bg-dark border-1 border-danger col-12 text-white text-center"
                                       name="NetPri">{{ctrl.Esh | number:'2'}}</label><br>
                                <label for="fname" class="col-12">م.الإصدار</label>
                                <label style="height:50px" class="bg-dark border-1 border-danger col-12 text-white text-center"
                                       name="NetPri">{{ctrl.t_Insu | number:'2'}}</label><br>
                                <label for="fname" class="col-12">المجموع</label>
                                <label style="height:100px;text-align:center" class="bg-dark border-1 border-danger col-12 text-white text-center fw-bolder fs-4"
                                       name="NetPri">{{ctrl.Totoal | number:'2'}}</label><br>
                            </fieldset>
                        </div>


                    </div>


                </form>
            </div>
            <div class="modal-footer justify-content-center bg-primary" ng-if="ctrl.showhFlag==1">
                <button type="button" class="btn btn-success col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" ng-disabled="mForm.$invalid" ng-click="ctrl.a40007()">
                    حفظ
                </button>
                <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">إلغاء الأمر</button>
            </div>
            <div class="modal-footer justify-content-center" ng-if="ctrl.showhFlag==2">
                <button type="button" class="btn btn-success col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" ng-disabled="mForm.$invalid" ng-click="ctrl.a40008()">
                    حفظ كجديد
                </button>
                <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">إلغاء الأمر</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="Dl_Message" tabindex="-1" data-bs-backdrop="static" aria-labelledby="{{ctrl.Title}}" aria-hidden="true">
    <div class="modal-dialog ">
        <div class="modal-content">
            <form name="FrmMsg" autocomplete="off">
                <div class="modal-header">
                    <h5 class="modal-title">{{ctrl.DlTitle}}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-auto">
                            <h4 class="m-4 text-danger">{{ctrl.DlMessage}}</h4>
                        </div>

                        <div class="col-12">
                            <label for="">سبب الحذف    <span ng-show="FrmMsg.DelNote.$error.required" class="text-danger float-end ">*</span></label>
                            <textarea required rows="8" class="form-control border border-1" maxlength="3000" ng-model="ctrl.DelNote" name="DelNote"></textarea>
                            <span ng-show="FrmMsg.DelNote.$error.maxlength" class="text-danger float-end ">تجاوزت الحد المسموح لطول الرسالة</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" ng-disabled="FrmMsg.$invalid" class="btn btn-success col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal" ng-click="ctrl.a45006()">
                        نعم
                    </button>
                    <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">لا</button>
                </div>
            </form>
        </div>
    </div>
</div>


<div class="row justify-content-center d-print-none  " ng-if="ctrl.switchFlag==4">

    <div class="col-lg-12   col-md-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12">
        <div class="card  navglassColor ">
            <div class="card-header ">

                <div class="btn-group cairo" dir="ltr" role="group">
                    <button type="button" disabled class="btn btn-primary">واجهة الخدمات الإضافية </button>
                    <button type="button" ng-click="ctrl.Back()" class="btn btn-primary bi bi-house-fill"> <span class="m-2">واجهة إصدار وثيقة تامين إجباري</span></button>
                    <button type="button" ng-click="ctrl.BtnHomePage()" class="btn btn-success bi bi-house-fill"> <span class="m-2">الرئيسية</span></button>
                </div>

            </div>


        </div>

        <div class="  ">


            <form class="row ">
                <div class="btn-group cairo col-4 mt-5 pl-3 px-4">

                    <input required class="form-control" type="text" ng-model="ctrl.SearchTxt" placeholder="    بحث أكتب رقم الوثيقة اورقم QR او رقم او رقم الهيكل...   ">
                    <button class="btn  btn-primary" ng-click="ctrl.a400031()">بحث</button>
                </div>


            </form>
            <div class="row  mt-4 " style="height:80vh; padding:25px;overflow-y:scroll">
                <div class="col-12">
                    <table class="table" style="font-size:14px">
                        <tr >
                            <th class="text-center">#</th>
                            <th class="text-center">نوع الوثيقة</th>
                            <th class="text-center">  رقم الوثيقة </th>
                            <th class="text-center">  المؤمن له </th>
                            <th class="text-center">  السيارة </th>
                            <th class="text-center">  رقم اللوحة </th>
                            <th class="text-center">  اللون   </th>
                            <th class="text-center"> القسط </th>
                            <th class="text-center"> الضرائب </th>
                            <th class="text-center"> الإجمالي </th>
                            <th class="text-center">تاريخ الإدخال </th>
                            <th class="text-center">  المدخل </th>

                            <th class="text-center" style="margin:0" colspan="2">العمليات</th>
                        </tr>
                        <tr  ng-repeat="x in ctrl.SerDocTb">
                            <td class="text-center">{{$index + 1}}</td>
                            <td class="text-center">{{x.TypeDesc}}</td>
                            <td class="text-center " dir="ltr">
                                {{x.Ins_SN}}
                            </td>
                            <td class="text-center">{{x.c_name}}</td>
                            <td class="text-center">{{x.car_name}}</td>
                            <td class="text-center">{{x.iron_bord}}</td>
                            <td class="text-center">{{x.color}}</td>
                            <td class="text-center">{{x.ins_val| number:'2'}}</td>
                            <td class="text-center">{{x.TaxVal| number:'2'}}</td>
                            <td class="text-center">{{x.total_tax | number:'2'}}</td>
                            <td class="text-center">{{x.InsertDate |date : "yyyy-MM-dd  a hh:mm" }}</td>
                            <td class="text-center">{{x.InsertBy}}</td>

                            <td>
                                <button type="button" class="btn btn-dark " ng-click="ctrl.a400032(x)">
                                    بدل فاقد
                                    <i class="fa-solid fa-eye"></i>
                                </button>
                            </td>

                            <td>
                                <button type="button" class="btn btn-primary" ng-click="ctrl.a400035(x)">
                                    تغير ملكية
                                    <i class="fa fa-file-text" aria-hidden="true"></i>

                                </button>
                            </td>

                            <td>
                                @*<button type="button" class="btn btn-primary" ng-click="ctrl.a400036(x)">
                                        تجديد
                                        <i class="fa fa-file-text" aria-hidden="true"></i>

                                    </button>*@
                            </td>

                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="ReNewDl" tabindex="-1" data-bs-backdrop="static" aria-labelledby="{{ctrl.Title}}" aria-hidden="true">
    <div class="modal-dialog  ">
        <form name="ReNewDl" class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">{{ctrl.DlTitle}}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row g-3">
                    <div class="col-12">
                        <label for="NoOfPassen">
                            رقم المطبوعة  <span ng-if="ReNewDl.DocNoTXT.$error.required" class="text-danger float-end  ">*</span>
                        </label>
                        <input required name="DocNoTXT" type="number"
                               ng-style="ReNewDl.DocNoTXT.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                               ng-model="ctrl.RenewObj.DocNoTXT" class="form-control text-center">
                        <span ng-if="ctrl.IsExistPayperNum == 1" class="text-danger float-end m-2">
                            الرقم غير
                            صحيح
                        </span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" ng-click="ctrl.a400033()">
                    إصدار بدل فاقد
                </button>
                <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">إلغاء الأمرا</button>
            </div>

        </form>
    </div>
</div>

<div class="modal fade cairo " id="ReplaceDoc" tabindex="-1" data-bs-backdrop="static" aria-labelledby="{{ctrl.Title}}" aria-hidden="true">
    <div class="modal-dialog modal-xl 	">
        <div class="modal-content gradentModelGray">
            <div class="modal-header">
                <h5 class="modal-title">{{ctrl.DlTitle}}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body cairo">
                <form name="ReplaceDocFrm" autocomplete="off" novalidate>
                    <div class="row">
                        <div class="col-9">



                            <fieldset class="row mt-2 mb-2 gradientModelBackg text-white" style="border:1px solid #808080">

                                <legend class="fs-5">  المؤمن له </legend>
                                <div class="col-4">
                                    <label for="city_ID"> جهة الترخيص <span ng-if="ReplaceDocFrm.city_ID.$error.required" class="text-danger">   * </span>  </label>

                                    <md-autocomplete flex required
                                                     md-require-match="true"
                                                     md-search-text="ctrl.searchCity"
                                                     md-input-name="city_ID"
                                                     md-selected-item="ctrl.selected_city_license_Item"
                                                     md-no-cache="true"
                                                     md-input-minlength="1"
                                                     md-input-maxlength="18"
                                                     ng-style=" ReplaceDocFrm.city_ID.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}"
                                                     md-selected-item-change="ctrl.Selected_City_Item(item)" md-items="item in ctrl.SearchCity(ctrl.searchCity)"
                                                     md-item-text="item.Name" placeholder="قم بإدخال اول حرف وإختر من القائمة" input-aria-labelledby="cityName">
                                        <md-item-template>
                                            <span md-highlight-text="ctrl.searchCity" md-highlight-flags="^i">{{item.Name}}</span>
                                        </md-item-template>
                                        <md-not-found>
                                            العنصر     "{{ctrl.searchCity}}"  غير موجود.

                                        </md-not-found>
                                    </md-autocomplete>

                                </div>
                                <div class="col-4">
                                    <label for="c_Name"> اسم الزبون  <span ng-show="ReplaceDocFrm.c_Name.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required ng-disabled="ctrl.selected_Comp_ID.IsDisabed" name="c_Name" type="text"
                                           ng-style="ReplaceDocFrm.c_Name.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.RePlaceObj.c_Name" class="form-control">

                                </div>

                                <div class="col-4">
                                    <label for="c_Address">العنوان <span ng-if="ReplaceDocFrm.c_Address.$error.required" class="text-danger">   * </span>  </label>

                                    <md-autocomplete flex required
                                                     md-require-match="true"
                                                     md-search-text="ctrl.SearchCity1"
                                                     md-input-name="c_Address"
                                                     md-selected-item="ctrl.c_Address"
                                                     md-no-cache="true"
                                                     md-input-minlength="1"
                                                     md-input-maxlength="18"
                                                     ng-style=" ReplaceDocFrm.c_Address.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}"
                                                     md-selected-item-change="ctrl.Selected_City_Item_Addres()" md-items="item in ctrl.SearchCityAddres(ctrl.SearchCity1)"
                                                     md-item-text="item.Name" placeholder="قم بإدخال اول حرف وإختر من القائمة" input-aria-labelledby="c_Address">
                                        <md-item-template>
                                            <span md-highlight-text="ctrl.SearchCity1" md-highlight-flags="^i">{{item.Name}}</span>
                                        </md-item-template>
                                        <md-not-found>
                                            العنصر     "{{ctrl.searchCity1}}"  غير موجود.

                                        </md-not-found>
                                    </md-autocomplete>

                                </div>
                                <div class="col-4">
                                    <label for="c_Address">العنوان <span ng-show="mForm.c_Address.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required name="c_Address" type="text"
                                           ng-style="ReplaceDocFrm.c_Address.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.RePlaceObj.c_Address" class="form-control ">

                                </div>

                                <div class="col-4 mb-2">
                                    <label for="c_Address">رقم الهاتف <span ng-show="mForm.c_phone.$error.required || mForm.c_phone.$error.c_phone" class="text-danger float-end ">*</span></label>
                                    <input required name="c_phone" type="number" @*pattern="^(?:0|\(?\+33\)?\s?|0033\s?)[1-79](?:[\.\-\s]?\d\d){4}$"*@
                                           ng-style="ReplaceDocFrm.c_phone.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.RePlaceObj.c_phone" class="form-control ">

                                </div>
                                <div class="col-4">
                                    <label for="Ib_num">  رقم  اللوحة المعدنية  <span ng-show="ReplaceDocFrm.Ib_num.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required name="Ib_num" type="text"
                                           ng-style="ReplaceDocFrm.Ib_num.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           pattern="^[0-9-]+$" ng-model="ctrl.RePlaceObj.Ib_num" class="form-control ">

                                </div>
                                <div class="col-4">
                                    <label for="NoOfPassen">
                                        رقم المطبوعة  <span ng-if="ReplaceDocFrm.DocNoTXT.$error.required" class="text-danger float-end  ">*</span>
                                    </label>
                                    <input required name="DocNoTXT" type="number"
                                           ng-style="ReplaceDocFrm.DocNoTXT.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.RePlaceObj.DocNoTXT" class="form-control text-center">
                                    <span ng-if="ctrl.IsExistPayperNum == 1" class="text-danger float-end m-2">
                                        الرقم غير
                                        صحيح
                                    </span>
                                </div>
                            </fieldset>






                        </div>
                        <div class="col-3">

                            <fieldset class="row g-1 justify-content-center text-center" style="border: 1px solid #808080; height: 75vh">

                                <legend class="fs-5">    إحتساب القسط   :</legend>
                                <label for="fname" class="col-12">صافي القسط</label>
                                <label style="height:50px" class="bg-dark border-1 border-white col-12 text-white text-center"
                                       ng-model="ctrl.NetPri" name="NetPri">{{ctrl.NetPri | number:'2'}}</label><br>
                                <label for="fname" class="col-12">الدمغة</label>
                                <label style="height:50px" class="bg-dark border-1 border-danger col-12 text-white text-center"
                                       name="NetPri">{{0.5 | number:'2'}}</label><br>
                                <label for="fname" class="col-12">الضريبة</label>
                                <label style="height:50px" class="bg-dark border-1 border-danger col-12 text-white text-center"
                                       name="NetPri">{{0 | number:'2'}}</label><br>
                                <label for="fname" class="col-12">رسوم إشراف</label>
                                <label style="height:50px" class="bg-dark border-1 border-danger col-12 text-white text-center"
                                       name="NetPri">{{0| number:'2'}}</label><br>
                                <label for="fname" class="col-12">م.الإصدار</label>
                                <label style="height:50px" class="bg-dark border-1 border-danger col-12 text-white text-center"
                                       name="NetPri">{{2 | number:'2'}}</label><br>
                                <label for="fname" class="col-12">المجموع</label>
                                <label style="height:100px;text-align:center" class="bg-dark border-1 border-danger col-12 text-white text-center fw-bolder fs-4"
                                       name="NetPri">{{2.5 | number:'2'}}</label><br>
                            </fieldset>
                        </div>


                    </div>


                </form>
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-success col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" ng-disabled="ReplaceDocFrm.$invalid" ng-click="ctrl.a400051()">
                    حفظ
                </button>
                <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">إلغاء الأمر</button>
            </div>

        </div>
    </div>
</div>


<div class="modal fade cairo " id="OldRenew" tabindex="-1" data-bs-backdrop="static" aria-labelledby="{{ctrl.Title}}" aria-hidden="true">
    <div class="modal-dialog modal-xl 	">
        <div class="modal-content gradentModelGray">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">{{ctrl.DlTitle}}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body cairo">
                <form name="OldRenew" autocomplete="off" novalidate>
                    <div class="row">
                        <div class="col-9">

                            <fieldset class="row text-white mt-1 gradientModelBackg" style="border:1px solid #808080">
                                <legend class="fs-5">التأمين</legend>

                                <div class="col-4 cairo ">
                                    <label for="Sdate">تاريخ بداية التأمين  <span ng-show="OldRenew.S_Date.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required name="S_Date" type="date" disabled readonly
                                           ng-style="OldRenew.S_Date.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj1.S_Date" class="form-control text-center" />



                                </div>
                                <div class="col-4 cairo ">
                                    <label for="Edate"> تاريخ نهاية التأمين    <span ng-show="OldRenew.E_date.$error.required" class="text-danger float-end ">*</span></label>
                                    <input required name="E_date" type="date"
                                           ng-style="OldRenew.E_date.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj1.E_date" class="form-control text-center" />


                                </div>

                                <div class="col-4 cairo ">
                                    <label for="c_masterID">الغرض من الترخيص  <span ng-show="OldRenew.c_masterID.$error.required" class="text-danger float-end  ">*</span></label>
                                    <select required name="c_masterID" class="form-select text-center" ng-change="ctrl.a4000666()"
                                            ng-style="OldRenew.c_masterID.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                            ng-model="ctrl.MainObj1.c_masterID"
                                            ng-options="x.ID as x.Name for x in ctrl.MasterObj"></select>

                                </div>
                                <div class="col-4 cairo ">
                                    <label for="c_DetailsID">مواصفات السيارة <span ng-if="OldRenew.c_DetailsID.$error.required" class="text-danger float-end  ">*</span></label>
                                    <select required name="c_DetailsID" class="form-select text-center" ng-change="ctrl.a40006123()"
                                            ng-style="OldRenew.c_DetailsID.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                            ng-model="ctrl.Selected_c_DetailsID"
                                            ng-options="x.ID as x.Name for x in ctrl.PricDeObj"></select>



                                </div>
                                <div class="col-2 cairo">

                                    <label for="NoOfPassen">
                                        عدد الركاب   <span ng-if="OldRenew.NoOfPassen.$error.required" class="text-danger float-end  ">*</span>
                                    </label>
                                    <input required name="NoOfPassen" type="number" min="{{ctrl.Selected_c_DetailsID.MinNoPass}}" max="{{ctrl.Selected_c_DetailsID.MaxNoPass}}"
                                           ng-style="OldRenew.NoOfPassen.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj1.NoOfPassen" class="form-control text-center" ng-change="ctrl.a400016()">
                                    <span ng-show="OldRenew.NoOfPassen.$error.max" class="text-danger float-end m-2">أقصى قيمة {{ctrl.Selected_c_DetailsID.MaxNoPass}}</span>
                                    <span ng-show="OldRenew.NoOfPassen.$error.min" class="text-danger float-end m-2">اقل قيمة {{ctrl.Selected_c_DetailsID.MinNoPass}}</span>

                                </div>
                                <div class="col-2 ">
                                    <label for="PassPrice"> سعر الراكب  <span ng-show="OldRenew.PassPrice.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required name="PassPrice" type="number" min="0"
                                           ng-style="OldRenew.PassPrice.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj1.PassPrice" class="form-control text-center" />


                                </div>
                                <div class="col-4 mb-2">

                                    <label for="c_load">الحمولة <span ng-show="OldRenew.c_load.$error.required" class="text-danger float-end ">*</span></label>
                                    <input required name="c_load" type="number" min="0"
                                           ng-style="OldRenew.c_load.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj1.c_load" max="" class="form-control text-center">


                                    <span ng-show="OldRenew.c_load.$error.max" class="text-danger float-end m-2">خطأ</span>

                                </div>

                            </fieldset>


                            <fieldset class="row mt-2 mb-2 gradientModelBackg text-white" style="border:1px solid #808080">

                                <legend class="fs-5">  المؤمن له </legend>
                                <div class="col-4">
                                    <label for="city_ID"> جهة الترخيص <span ng-if="OldRenew.city_ID.$error.required" class="text-danger">   * </span>  </label>

                                    <md-autocomplete flex required
                                                     md-require-match="true"
                                                     md-search-text="ctrl.searchCity"
                                                     md-input-name="city_ID"
                                                     md-selected-item="ctrl.selected_city_license_Item"
                                                     md-no-cache="true"
                                                     md-input-minlength="1"
                                                     md-input-maxlength="18"
                                                     ng-style=" OldRenew.city_ID.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}"
                                                     md-selected-item-change="ctrl.Selected_City_Item1(item)" md-items="item in ctrl.SearchCity(ctrl.searchCity)"
                                                     md-item-text="item.Name" placeholder="قم بإدخال اول حرف وإختر من القائمة" input-aria-labelledby="cityName">
                                        <md-item-template>
                                            <span md-highlight-text="ctrl.searchCity" md-highlight-flags="^i">{{item.Name}}</span>
                                        </md-item-template>
                                        <md-not-found>
                                            العنصر     "{{ctrl.searchCity}}"  غير موجود.

                                        </md-not-found>
                                    </md-autocomplete>

                                </div>
                                <div class="col-4">
                                    <label for="c_Name"> اسم الزبون  <span ng-show="OldRenew.c_Name.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required ng-disabled="ctrl.selected_Comp_ID.IsDisabed" name="c_Name" type="text"
                                           ng-style="OldRenew.c_Name.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj1.c_Name" class="form-control">

                                </div>

                                <div class="col-4">
                                    <label for="c_Address">العنوان <span ng-if="OldRenew.c_Address.$error.required" class="text-danger">   * </span>  </label>

                                    <md-autocomplete flex required
                                                     md-require-match="true"
                                                     md-search-text="ctrl.SearchCity1"
                                                     md-input-name="c_Address"
                                                     md-selected-item="ctrl.c_Address1"
                                                     md-no-cache="true"
                                                     md-input-minlength="1"
                                                     md-input-maxlength="18"
                                                     ng-style=" OldRenew.c_Address.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}"
                                                     md-selected-item-change="ctrl.Selected_City_Item_Addres1()" md-items="item in ctrl.SearchCityAddres(ctrl.SearchCity1)"
                                                     md-item-text="item.Name" placeholder="قم بإدخال اول حرف وإختر من القائمة" input-aria-labelledby="c_Address">
                                        <md-item-template>
                                            <span md-highlight-text="ctrl.SearchCity1" md-highlight-flags="^i">{{item.Name}}</span>
                                        </md-item-template>
                                        <md-not-found>
                                            العنصر     "{{ctrl.searchCity1}}"  غير موجود.

                                        </md-not-found>
                                    </md-autocomplete>

                                </div>
                                <div class="col-4">
                                    <label for="c_Address">العنوان <span ng-show="OldRenew.c_Address.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required name="c_Address" type="text"
                                           ng-style="OldRenew.c_Address.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj1.c_Address" class="form-control ">

                                </div>

                                <div class="col-4 mb-2">
                                    <label for="c_Address">رقم الهاتف <span ng-show="OldRenew.c_phone.$error.required || mForm.c_phone.$error.c_phone" class="text-danger float-end ">*</span></label>
                                    <input required name="c_phone" type="number"
                                           ng-style="OldRenew.c_phone.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj1.c_phone" class="form-control ">

                                </div>

                            </fieldset>
                            <fieldset class="row mt-2 text-white gradientModelBackg " style="border:1px solid #808080">

                                <legend class="fs-5">    بيانات المركبة :</legend>


                                <div class="col-4">
                                    <label for="Ib_num">  رقم  اللوحة المعدنية  <span ng-show="OldRenew.Ib_num.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required name="Ib_num" type="text"
                                           ng-style="OldRenew.Ib_num.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           pattern="^[0-9-]+$" ng-model="ctrl.MainObj1.Ib_num" class="form-control ">

                                </div>


                                <div class="col-4">
                                    <label for="car_ID">نوع السيارة  <span ng-show="OldRenew.car_ID.$error.required" class="text-danger">   * </span>  </label>

                                    <md-autocomplete flex required
                                                     md-require-match="true"
                                                     md-search-text="ctrl.searchCar"
                                                     md-selected-item="ctrl.selected_Car_Brand_Item"
                                                     md-input-name="car_ID"
                                                     md-no-cache="true"
                                                     md-input-minlength="1"
                                                     md-input-maxlength="18"
                                                     ng-style=" OldRenew.car_ID.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}"
                                                     md-selected-item-change="ctrl.SelectedCarChange1(item)" md-items="item in ctrl.SearchCar(ctrl.searchCar)"
                                                     md-item-text="item.Name" placeholder="قم بإدخال اول حرف وإختر من القائمة" input-aria-labelledby="Name">
                                        <md-item-template>
                                            <span md-highlight-text="ctrl.searchCar" md-highlight-flags="^i">{{item.Name}}</span>
                                        </md-item-template>
                                        <md-not-found>
                                            العنصر     "{{ctrl.searchCar}}"  غير موجود.

                                        </md-not-found>
                                    </md-autocomplete>

                                </div>
                                <div class="col-4">
                                    <label for="car_color">لون السيارة  <span ng-if="OldRenew.car_color.$error.required" class="text-danger">   * </span>  </label>

                                    <md-autocomplete flex required
                                                     md-search-text="ctrl.searchColor"
                                                     md-input-name="car_color"
                                                     md-no-cache="true"
                                                     md-require-match="true"
                                                     md-input-minlength="1"
                                                     md-input-maxlength="18"
                                                     md-selected-item="ctrl.selected_Car_Color_Item"
                                                     ng-style=" OldRenew.car_color.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}"
                                                     md-selected-item-change="ctrl.SelectedColorChange1(item)" md-items="item in ctrl.SearchColor(ctrl.searchColor)"
                                                     md-item-text="item.Name" md-min-length="0" placeholder="قم بإدخال اول حرف وإختر من القائمة" input-aria-labelledby="Name">
                                        <md-item-template>
                                            <span md-highlight-text="ctrl.searchColor" md-highlight-flags="^i">{{item.Name}}</span>
                                        </md-item-template>
                                        <md-not-found>
                                            العنصر     "{{ctrl.searchColor}}"  غير موجود.

                                        </md-not-found>
                                    </md-autocomplete>

                                </div>

                                <div class="col-4">
                                    <label for="chass_num"> رقم الهيكل <span ng-show="OldRenew.chass_num.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required name="chass_num" type="text"
                                           ng-style="OldRenew.chass_num.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj1.chass_num" class="form-control ">


                                </div>
                                <div class="col-4">
                                    <label for="Eng_num"> رقم المحرك <span ng-show="OldRenew.chass_num.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required name="Eng_num" type="text"
                                           ng-style="OldRenew.Eng_num.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj1.Eng_num" class="form-control ">


                                </div>
                                <div class="col-4">

                                    <label for="eng_cap">قوة المحرك  <span ng-show="OldRenew.eng_cap.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required name="eng_cap" type="number"
                                           ng-style="OldRenew.eng_cap.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj1.eng_cap" max="{{ctrl.Selected_c_DetailsID.MaxCap_eng}}" min="{{ctrl.Selected_c_DetailsID.MinCap_eng}}"
                                           class="form-control text-center">

                                    <span ng-show="OldRenew.eng_cap.$error.max" class="text-danger float-end m-2">أقصى قيمة {{ctrl.Selected_c_DetailsID.MaxCap_eng}}</span>
                                    <span ng-show="OldRenew.eng_cap.$error.min" class="text-danger float-end m-2">أقل قيمة {{ctrl.Selected_c_DetailsID.MinCap_eng}}</span>



                                </div>



                                <div class="col-4 ">
                                    <label for="m_year">سنة الصنع <span ng-show="OldRenew.m_year.$error.required" class="text-danger float-end  ">*</span></label>
                                    <select required name="m_year" class="form-select text-center"
                                            ng-style="OldRenew.m_year.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                            ng-model="ctrl.MainObj1.m_year"
                                            ng-options="x as x for x in ctrl.m_Years|orderBy:'-'"></select>
                                </div>
                                <div class="col-4">
                                    <label for="cityName">بلد الصنع  <span ng-show="OldRenew.con_ID.$error.required" class="text-danger">   * </span>  </label>

                                    <md-autocomplete flex required
                                                     md-require-match="true"
                                                     md-search-text="ctrl.searchContory"
                                                     md-selected-item="ctrl.selected_manfictor_Item"
                                                     md-input-name="con_ID"
                                                     md-no-cache="true"
                                                     md-input-minlength="1"
                                                     md-input-maxlength="18"
                                                     ng-style=" OldRenew.con_ID.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}"
                                                     md-selected-item-change="ctrl.selectedContryChange1(item)" md-items="item in ctrl.SearchContory(ctrl.searchContory)"
                                                     md-item-text="item.Name" placeholder="قم بإذخال اول حرف وإختر من القائمة" input-aria-labelledby="Name">
                                        <md-item-template>
                                            <span md-highlight-text="ctrl.searchContory" md-highlight-flags="^i">{{item.Name}}</span>
                                        </md-item-template>
                                        <md-not-found>
                                            العنصر     "{{ctrl.searchContory}}"  غير موجود.

                                        </md-not-found>
                                    </md-autocomplete>

                                </div>


                                <div class="col-4">

                                    <label for="pap_no">رقم المطبوعة<span ng-if="OldRenew.pap_no.$error.required" class="text-danger float-end ">*</span></label>
                                    <input required name="pap_no" type="number" min="0" ng-click="ctrl.IsExistPayperNum = 0"
                                           ng-style="OldRenew.pap_no.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj1.pap_no" max="" class="form-control text-center">
                                    <span ng-if="ctrl.IsExistPayperNum == 1" class="text-danger float-end m-2">
                                        الرقم غير
                                        صحيح
                                    </span>


                                </div>
                                <div class="col-4">

                                    <label for="oldDocNum">رقم الوثيقة السابقة <span ng-if="OldRenew.oldDocNum.$error.required" class="text-danger float-end ">*</span></label>
                                    <input required name="oldDocNum" type="text" ng-click="ctrl.IsExistPayperNum = 0"
                                           ng-style="OldRenew.oldDocNum.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj1.OldDocNum" max="" class="form-control text-center">
                                    <span ng-if="ctrl.IsExistPayperNum == 1" class="text-danger float-end m-2">

                                    </span>


                                </div>
                            </fieldset>





                        </div>
                        <div class="col-3">

                            <fieldset class="row g-1 justify-content-center text-center" style="border: 1px solid #808080; height: 75vh">

                                <legend class="fs-5">    إحتساب القسط   :</legend>
                                <label for="fname" class="col-12">صافي القسط</label>
                                <label style="height:50px" class="bg-dark border-1 border-white col-12 text-white text-center"
                                       ng-model="ctrl.NetPri" name="NetPri">{{ctrl.NetPri | number:'2'}}</label><br>
                                <label for="fname" class="col-12">الدمغة</label>
                                <label style="height:50px" class="bg-dark border-1 border-danger col-12 text-white text-center"
                                       name="NetPri">{{ctrl.t_Stamp | number:'2'}}</label><br>
                                <label for="fname" class="col-12">الضريبة</label>
                                <label style="height:50px" class="bg-dark border-1 border-danger col-12 text-white text-center"
                                       name="NetPri">{{ctrl.t_Tax | number:'2'}}</label><br>
                                <label for="fname" class="col-12">رسوم إشراف</label>
                                <label style="height:50px" class="bg-dark border-1 border-danger col-12 text-white text-center"
                                       name="NetPri">{{ctrl.Esh | number:'2'}}</label><br>
                                <label for="fname" class="col-12">م.الإصدار</label>
                                <label style="height:50px" class="bg-dark border-1 border-danger col-12 text-white text-center"
                                       name="NetPri">{{ctrl.t_Insu | number:'2'}}</label><br>
                                <label for="fname" class="col-12">المجموع</label>
                                <label style="height:100px;text-align:center" class="bg-dark border-1 border-danger col-12 text-white text-center fw-bolder fs-4"
                                       name="NetPri">{{ctrl.Totoal | number:'2'}}</label><br>
                            </fieldset>
                        </div>


                    </div>


                </form>
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-success col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" ng-disabled="OldRenew.$invalid" ng-click="ctrl.a400052()">
                    حفظ
                </button>
                <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">إلغاء الأمر</button>
            </div>

        </div>
    </div>
</div>
