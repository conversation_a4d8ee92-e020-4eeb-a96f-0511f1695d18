<link href="~/Content/CSS/bootstrap.rtl.min.css??v=@DateTime.Now.Ticks" rel="stylesheet" />
<link href="~/Content/InssunaceStyle.css??v=@DateTime.Now.Ticks" rel="stylesheet" media="screen" />
<link href="~/Content/A4PageCss.css?v=@DateTime.Now.Ticks" rel="stylesheet" media="print" />
<link href="~/Content/Cairo.css?v=?v=@DateTime.Now.Ticks" rel="stylesheet" />
<link href="~/Content/CSS/all.min.css?v=?v=@DateTime.Now.Ticks" rel="stylesheet" />
<link href="~/Content/A4PageCss.css?v=@DateTime.Now.Ticks" rel="stylesheet" media="print" />
@{
    if (ViewBag.CompID == 1)
    {
        Html.RenderPartial("_EthPrint");
    }
    if (ViewBag.CompID == 2)
    {
        Html.RenderPartial("_wikPrint");
    } 
    if (ViewBag.CompID == 3)
    {
        Html.RenderPartial("alasimaprint");
    }
}

<div class="row justify-content-center d-print-none  " ng-if="ctrl.switchFlag==1" style="background-color:white">

    <div class="col-lg-12   col-md-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12" style="background-color:white">
        <div class="card  navglassColor ">
            <div class="card-header" style="background-color:white">

                <div class="btn-group cairo" dir="ltr" role="group">
                    <button type="button" disabled class="btn btn-primary border-ridues  cairo "> واجهة إصدار وثيقة تامين إجباري</button>
                    <button type="button" ng-click="ctrl.BtnHomePage()" class="btn btn-success bi bi-house-fill"> <span class="m-2">الرئيسية</span></button>
                </div>
                <div class="col-auto float-end">
                    <button class="btn btn-primary btn-lg border-ridues  cairo " ng-if="ctrl.AgPerObj.Cump_AdditionalServices" ng-click="ctrl.a400030()">خدمات إضافية</button>
                    <button class="btn btn-primary btn-lg border-ridues  cairo " ng-if="ctrl.AgPerObj.Cump_OldInterface" ng-click="ctrl.a4000311()"> واجهة بيانات المنظومة القديمة </button>
                </div>
            </div>


        </div>

        <div class="bg-light">


            <div class="row  ">
                <div class="col-sm-12 col-xs-12  offset-1 col-md-5 col-lg-5 col-xl-5 col-xxl-5">

                    <input class="inputStyle cairo " type="text" ng-model="search" placeholder="   بحث...   ">

                </div>
                <div class="col-sm-12 col-xs-12 col-md-3 col-lg-3 col-xl-3 col-xxl-3 float-end ">

                    <button type="button"
                            class="btn btn-primary border-ridues  cairo float-end " style="width:50%;margin:25px 0 0;"
                            data-bs-toggle="modal" data-bs-target="#NewItem" ng-click="ctrl.AddNew()">
                        إصدار وثيقة جديدة
                    </button>

                </div>


            </div>

            <div class="row  mt-4  " style="height:80vh; padding:25px;overflow-y:scroll">
                <div class="table-responsive cairo ">
                    <table class="table     table-hover">
                        <tr class=" text-white" style="background-color:cornflowerblue">
                            <th class="text-center">#</th>
                            <th class="text-center">نوع الوثيقة</th>
                            <th class="text-center">  رقم الوثيقة </th>
                            <th class="text-center">  المؤمن له </th>
                            <th class="text-center">  السيارة </th>
                            <th class="text-center">  رقم اللوحة </th>
                            <th class="text-center">  اللون   </th>
                            <th class="text-center"> القسط </th>
                            <th class="text-center"> الضرائب </th>
                            <th class="text-center"> الإجمالي </th>
                            <th class="text-center">تاريخ الإدخال </th>
                            <th class="text-center">  المدخل </th>
                            <th class="text-center" style="margin:0" colspan="3">العمليات</th>
                        </tr>
                        <tr class="tableStaylebody p-0 text-dark" ng-repeat="x in ctrl.InsObj |filter:search " style="background-color:white">
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{$index + 1}}</td>
                            <td class="text-center">{{x.TypeDesc}}</td>
                            <td class="text-center " dir="ltr" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">
                                {{x.Ins_SN}}
                            </td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.c_name}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.car_name}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.iron_bord}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.color}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.ins_val| number:'2'}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.TaxVal| number:'2'}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.total_tax | number:'2'}}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.InsertDate |date : "yyyy-MM-dd  a hh:mm" }}</td>
                            <td class="text-center" ng-style="x.IsDelProg ? {'opacity':'0.7'}:{'opacity':'1'} ">{{x.InsertBy}}</td>

                            <td ng-hide="x.IsDelProg">
                                <button title="معاينة الوثيقة" type="button" class="btn btn-dark " ng-click="ctrl.prview(x)">
                                    <i class="bi bi-eye-fill"></i>
                                </button>
                            </td>

                            <td colspan="2" ng-show="x.IsDelProg">
                                <button title="التعليقات ({{x.NotCount}})" type="button" class="btn btn-secondary " ng-click="ctrl.prview(x)">
                                    <i class="bi bi-eye-fill"></i>
                                </button>
                            </td>

                            <td ng-hide="x.IsDelProg">
                                <button title="حذف الوثيقة" type="button" class="btn btn-danger" ng-click="ctrl.BtnStatus(x)">
                                    <i class="bi bi-trash-fill" aria-hidden="true"></i>
                                </button>
                            </td>

                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Premium Insurance Document -->
<div class="insurance-document-container" ng-if="ctrl.switchFlag==2">
    <!-- Enhanced Control Panel -->
    <div class="control-panel-premium d-print-none">
        <div class="control-header">
            <div class="control-left">
                <button type="button" ng-click="ctrl.BtnHomePage()" class="btn-premium btn-home">
                    <div class="btn-icon">
                        <i class="bi bi-house-fill"></i>
                    </div>
                    <span class="btn-text">الرئيسية</span>
                </button>
            </div>
            <div class="control-center">
                <div class="document-status-premium">
                    <div class="status-indicator-premium active"></div>
                    <h3 class="status-title">معاينة وثيقة التأمين الإجباري</h3>
                    <p class="status-subtitle">Compulsory Insurance Policy Preview</p>
                </div>
            </div>
            <div class="control-right">
                <button type="button" ng-click="ctrl.BackOneStep()" class="btn-premium btn-back">
                    <div class="btn-icon">
                        <i class="bi bi-arrow-right"></i>
                    </div>
                    <span class="btn-text">عودة</span>
                </button>
            </div>
        </div>

        <div class="print-buttons-section">
            @{
                if (ViewBag.CompID == 1)
                {
                    Html.RenderPartial("_EthPrint_button");
                }
                if (ViewBag.CompID == 2)
                {
                    Html.RenderPartial("_wikPrint_button");
                }
                if (ViewBag.CompID == 3)
                {
                    Html.RenderPartial("alasimaprint_button");
                }
            }
        </div>
    </div>

    <!-- Premium Document Content -->
    <div class="document-content-premium">

        <div class="document-layout">
            <div class="main-content">
                <!-- Policy Information Section -->
                <div class="info-section-premium">
                    <div class="section-header-premium">
                        <div class="section-icon-premium">
                            <i class="bi bi-file-text-fill"></i>
                        </div>
                        <div class="section-title-premium">
                            <h3>معلومات الوثيقة</h3>
                            <p>Policy Information</p>
                        </div>
                    </div>

                    <div class="info-cards-grid">
                        <div class="info-card-premium highlight">
                            <div class="card-icon-premium primary">
                                <i class="bi bi-hash"></i>
                            </div>
                            <div class="card-content-premium">
                                <div class="card-label">رقم الوثيقة</div>
                                <div class="card-value">{{ ctrl.printObj.Ins_SN}}</div>
                                <div class="card-label-en">Policy Number</div>
                            </div>
                        </div>

                        <div class="info-card-premium">
                            <div class="card-icon-premium success">
                                <i class="bi bi-building"></i>
                            </div>
                            <div class="card-content-premium">
                                <div class="card-label">المكتب / الفرع</div>
                                <div class="card-value">{{ctrl.printObj.Agency}}</div>
                                <div class="card-label-en">Office / Branch</div>
                            </div>
                        </div>

                        <div class="info-card-premium">
                            <div class="card-icon-premium info">
                                <i class="bi bi-geo-alt-fill"></i>
                            </div>
                            <div class="card-content-premium">
                                <div class="card-label">العنوان</div>
                                <div class="card-value">{{ctrl.printObj.agencyCityAddres}}</div>
                                <div class="card-label-en">Address</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Coverage Period Section -->
                <div class="coverage-section-premium">
                    <div class="section-header-premium">
                        <div class="section-icon-premium">
                            <i class="bi bi-calendar-range"></i>
                        </div>
                        <div class="section-title-premium">
                            <h3>فترة التغطية</h3>
                            <p>Coverage Period</p>
                        </div>
                    </div>

                    <div class="coverage-timeline">
                        <div class="timeline-item start">
                            <div class="timeline-icon">
                                <i class="bi bi-play-circle-fill"></i>
                            </div>
                            <div class="timeline-content">
                                <div class="timeline-label">من تاريخ</div>
                                <div class="timeline-date">{{ctrl.printObj.f_date |date : "yyyy-MM-dd"}}</div>
                                <div class="timeline-label-en">From Date</div>
                            </div>
                        </div>

                        <div class="timeline-connector">
                            <div class="connector-line"></div>
                            <div class="connector-icon">
                                <i class="bi bi-arrow-right"></i>
                            </div>
                        </div>

                        <div class="timeline-item end">
                            <div class="timeline-icon">
                                <i class="bi bi-stop-circle-fill"></i>
                            </div>
                            <div class="timeline-content">
                                <div class="timeline-label">إلى تاريخ</div>
                                <div class="timeline-date">{{ctrl.printObj.to_date |date : "yyyy-MM-dd"}}</div>
                                <div class="timeline-label-en">To Date</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Insured Person Section -->
                <div class="insured-section-premium">
                    <div class="section-header-premium">
                        <div class="section-icon-premium">
                            <i class="bi bi-person-fill"></i>
                        </div>
                        <div class="section-title-premium">
                            <h3>بيانات المؤمن له</h3>
                            <p>Insured Person Details</p>
                        </div>
                    </div>

                    <div class="insured-cards-grid">
                        <div class="info-card-premium">
                            <div class="card-icon-premium warning">
                                <i class="bi bi-person-badge"></i>
                            </div>
                            <div class="card-content-premium">
                                <div class="card-label">اسم المؤمن له</div>
                                <div class="card-value">{{ctrl.printObj.c_name}}</div>
                                <div class="card-label-en">Insured Name</div>
                            </div>
                        </div>

                        <div class="info-card-premium">
                            <div class="card-icon-premium secondary">
                                <i class="bi bi-house-fill"></i>
                            </div>
                            <div class="card-content-premium">
                                <div class="card-label">العنوان</div>
                                <div class="card-value">{{ctrl.printObj.Address}}</div>
                                <div class="card-label-en">Address</div>
                            </div>
                        </div>

                        <div class="info-card-premium">
                            <div class="card-icon-premium danger">
                                <i class="bi bi-telephone-fill"></i>
                            </div>
                            <div class="card-content-premium">
                                <div class="card-label">رقم الهاتف</div>
                                <div class="card-value">{{ctrl.printObj.PhoneNum}}</div>
                                <div class="card-label-en">Phone Number</div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Vehicle Information Section -->
                <div class="vehicle-section-premium">
                    <div class="section-header-premium">
                        <div class="section-icon-premium">
                            <i class="bi bi-car-front-fill"></i>
                        </div>
                        <div class="section-title-premium">
                            <h3>بيانات المركبة</h3>
                            <p>Vehicle Information</p>
                        </div>
                    </div>

                    <div class="vehicle-highlight-card">
                        <div class="highlight-icon">
                            <i class="bi bi-credit-card-2-front"></i>
                        </div>
                        <div class="highlight-content">
                            <div class="highlight-label">رقم اللوحة المعدنية</div>
                            <div class="highlight-value">{{ ctrl.printObj.iron_bord}}</div>
                            <div class="highlight-label-en">License Plate Number</div>
                        </div>
                    </div>

                    <div class="vehicle-details-grid">
                        <div class="vehicle-card">
                            <div class="vehicle-icon">
                                <i class="bi bi-truck"></i>
                            </div>
                            <div class="vehicle-info">
                                <div class="vehicle-label">النوع</div>
                                <div class="vehicle-value">{{ctrl.printObj.car_name}}</div>
                                <div class="vehicle-label-en">Type</div>
                            </div>
                        </div>

                        <div class="vehicle-card">
                            <div class="vehicle-icon">
                                <i class="bi bi-globe"></i>
                            </div>
                            <div class="vehicle-info">
                                <div class="vehicle-label">بلد الصنع</div>
                                <div class="vehicle-value">{{ctrl.printObj.contry}}</div>
                                <div class="vehicle-label-en">Country of Origin</div>
                            </div>
                        </div>

                        <div class="vehicle-card">
                            <div class="vehicle-icon">
                                <i class="bi bi-speedometer2"></i>
                            </div>
                            <div class="vehicle-info">
                                <div class="vehicle-label">قوة المحرك</div>
                                <div class="vehicle-value">{{ctrl.printObj.Engine_Capacity}} حصان</div>
                                <div class="vehicle-label-en">Engine Power (HP)</div>
                            </div>
                        </div>

                        <div class="vehicle-card">
                            <div class="vehicle-icon">
                                <i class="bi bi-palette-fill"></i>
                            </div>
                            <div class="vehicle-info">
                                <div class="vehicle-label">اللون</div>
                                <div class="vehicle-value">{{ctrl.printObj.color}}</div>
                                <div class="vehicle-label-en">Color</div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Vehicle Specifications Section -->
                <div class="specifications-section-premium">
                    <div class="section-header-premium">
                        <div class="section-icon-premium">
                            <i class="bi bi-gear-fill"></i>
                        </div>
                        <div class="section-title-premium">
                            <h3>المواصفات التقنية</h3>
                            <p>Technical Specifications</p>
                        </div>
                    </div>

                    <div class="specifications-grid">
                        <div class="spec-card">
                            <div class="spec-icon">
                                <i class="bi bi-box"></i>
                            </div>
                            <div class="spec-content">
                                <div class="spec-label">الحمولة</div>
                                <div class="spec-value">{{ctrl.printObj.PayLoad}} طن</div>
                                <div class="spec-label-en">Payload (Tons)</div>
                            </div>
                        </div>

                        <div class="spec-card">
                            <div class="spec-icon">
                                <i class="bi bi-people-fill"></i>
                            </div>
                            <div class="spec-content">
                                <div class="spec-label">عدد الركاب</div>
                                <div class="spec-value">{{ctrl.printObj.NomOfPassengers}} راكب</div>
                                <div class="spec-label-en">Passengers</div>
                            </div>
                        </div>

                        <div class="spec-card">
                            <div class="spec-icon">
                                <i class="bi bi-calendar-date"></i>
                            </div>
                            <div class="spec-content">
                                <div class="spec-label">سنة الصنع</div>
                                <div class="spec-value">{{ctrl.printObj.Manufacturing_Year}}</div>
                                <div class="spec-label-en">Manufacturing Year</div>
                            </div>
                        </div>

                        <div class="spec-card wide">
                            <div class="spec-icon">
                                <i class="bi bi-clipboard-check"></i>
                            </div>
                            <div class="spec-content">
                                <div class="spec-label">الغرض من الترخيص</div>
                                <div class="spec-value">{{ctrl.printObj.mas_name}}</div>
                                <div class="spec-label-en">License Purpose</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Technical Details Section -->
                <div class="technical-section-premium">
                    <div class="section-header-premium">
                        <div class="section-icon-premium">
                            <i class="bi bi-tools"></i>
                        </div>
                        <div class="section-title-premium">
                            <h3>التفاصيل التقنية</h3>
                            <p>Technical Details</p>
                        </div>
                    </div>

                    <div class="technical-details-grid">
                        <div class="technical-card">
                            <div class="technical-icon">
                                <i class="bi bi-building"></i>
                            </div>
                            <div class="technical-content">
                                <div class="technical-label">الجهة المقيد بها</div>
                                <div class="technical-value">{{ctrl.printObj.InsuAdress}}</div>
                                <div class="technical-label-en">Registered Authority</div>
                            </div>
                        </div>

                        <div class="technical-card">
                            <div class="technical-icon">
                                <i class="bi bi-cpu"></i>
                            </div>
                            <div class="technical-content">
                                <div class="technical-label">رقم المحرك</div>
                                <div class="technical-value">{{ctrl.printObj.Engine_number}}</div>
                                <div class="technical-label-en">Engine Number</div>
                            </div>
                        </div>

                        <div class="technical-card full-width">
                            <div class="technical-icon">
                                <i class="bi bi-diagram-3"></i>
                            </div>
                            <div class="technical-content">
                                <div class="technical-label">رقم الهيكل</div>
                                <div class="technical-value">{{ ctrl.printObj.Chassis_number}}</div>
                                <div class="technical-label-en">Chassis Number</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Financial Section -->
                <div class="financial-section-premium">
                    <div class="section-header-premium">
                        <div class="section-icon-premium">
                            <i class="bi bi-cash-stack"></i>
                        </div>
                        <div class="section-title-premium">
                            <h3>التفاصيل المالية</h3>
                            <p>Financial Details</p>
                        </div>
                    </div>

                    <div class="tariff-note">
                        <div class="note-icon">
                            <i class="bi bi-info-circle-fill"></i>
                        </div>
                        <div class="note-text">
                            <span>القسط طبقاً للبند رقم <strong>1</strong> من التعريفة المقررة</span>
                        </div>
                    </div>

                    <div class="financial-breakdown">
                        <div class="financial-grid">
                            <div class="financial-item">
                                <div class="item-icon">
                                    <i class="bi bi-cash-coin"></i>
                                </div>
                                <div class="item-content">
                                    <div class="item-label">صافي القسط</div>
                                    <div class="item-value">{{ ctrl.printObj.ins_val | number:'2'}}</div>
                                    <div class="item-label-en">Net Premium</div>
                                </div>
                            </div>

                            <div class="financial-item">
                                <div class="item-icon">
                                    <i class="bi bi-percent"></i>
                                </div>
                                <div class="item-content">
                                    <div class="item-label">الضريبة</div>
                                    <div class="item-value">{{ ctrl.printObj.Tax | number:'2'}}</div>
                                    <div class="item-label-en">Tax</div>
                                </div>
                            </div>

                            <div class="financial-item">
                                <div class="item-icon">
                                    <i class="bi bi-stamp"></i>
                                </div>
                                <div class="item-content">
                                    <div class="item-label">الدمغة</div>
                                    <div class="item-value">{{ ctrl.printObj.Tax_stamp | number:'2'}}</div>
                                    <div class="item-label-en">Stamp</div>
                                </div>
                            </div>

                            <div class="financial-item">
                                <div class="item-icon">
                                    <i class="bi bi-eye-fill"></i>
                                </div>
                                <div class="item-content">
                                    <div class="item-label">الإشراف</div>
                                    <div class="item-value">{{ ctrl.printObj.Tax_supervision | number:'2'}}</div>
                                    <div class="item-label-en">Supervision</div>
                                </div>
                            </div>

                            <div class="financial-item">
                                <div class="item-icon">
                                    <i class="bi bi-file-earmark-text"></i>
                                </div>
                                <div class="item-content">
                                    <div class="item-label">م.الإصدار</div>
                                    <div class="item-value">{{ ctrl.printObj.Tax_insu | number:'2'}}</div>
                                    <div class="item-label-en">Issuance</div>
                                </div>
                            </div>

                            <div class="financial-item total">
                                <div class="item-icon">
                                    <i class="bi bi-calculator"></i>
                                </div>
                                <div class="item-content">
                                    <div class="item-label">الإجمالي</div>
                                    <div class="item-value">{{ ctrl.printObj.total_tax | number:'2'}} د.ل</div>
                                    <div class="item-label-en">Total (LYD)</div>
                                </div>
                            </div>
                        </div>

                        <div class="amount-in-words">
                            <div class="words-icon">
                                <i class="bi bi-fonts"></i>
                            </div>
                            <div class="words-content">
                                <div class="words-label">المبلغ بالحروف</div>
                                <div class="words-placeholder">_________________________________</div>
                                <div class="words-label-en">Amount in Words</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar Section -->
            <div class="sidebar-content">
                <!-- QR Code Section -->
                <div class="qr-section-premium">
                    <div class="qr-header">
                        <div class="qr-icon">
                            <i class="bi bi-qr-code"></i>
                        </div>
                        <div class="qr-title">
                            <h4>رمز التحقق</h4>
                            <p>Verification Code</p>
                        </div>
                    </div>
                    <div class="qr-container">
                        <qrcode data="{{ctrl.printObj.Ins_SN}}" class="qr-code" size="120" href="http://example.com"></qrcode>
                        <div class="qr-label">{{ctrl.printObj.Ins_SN}}</div>
                    </div>
                </div>

                <!-- Issue Date Section -->
                <div class="issue-date-section">
                    <div class="date-header">
                        <div class="date-icon">
                            <i class="bi bi-calendar-check"></i>
                        </div>
                        <div class="date-title">
                            <h4>تاريخ الإصدار</h4>
                            <p>Issue Date</p>
                        </div>
                    </div>
                    <div class="date-details">
                        <div class="date-item">
                            <div class="date-label">التاريخ</div>
                            <div class="date-value">{{ctrl.printObj.InsertDate | date : "dd-MM-yyyy"}}</div>
                        </div>
                        <div class="date-separator">|</div>
                        <div class="date-item">
                            <div class="date-label">الساعة</div>
                            <div class="date-value">{{ctrl.printObj.InsertDate | date : "a hh:mm"}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Premium CSS Styles -->
<style>
    /* Main Container */
    .insurance-document-container {
        font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
        background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 50%, #f0f4f8 100%);
        min-height: 100vh;
        padding: 20px;
        color: #1e293b;
    }

    /* Enhanced Control Panel */
    .control-panel-premium {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 25px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        margin-bottom: 30px;
    }

    .control-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .btn-premium {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        border: none;
        border-radius: 15px;
        padding: 12px 25px;
        color: white;
        font-weight: 600;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 10px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
        text-decoration: none;
        cursor: pointer;
    }

    .btn-home {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
    }

    .btn-back {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        box-shadow: 0 4px 15px rgba(245, 158, 11, 0.4);
    }

    .btn-premium:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(59, 130, 246, 0.6);
    }

    .btn-icon {
        font-size: 16px;
    }

    .document-status-premium {
        text-align: center;
    }

    .status-indicator-premium {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: linear-gradient(135deg, #10b981, #059669);
        margin: 0 auto 10px;
        animation: pulse-premium 2s infinite;
    }

    .status-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 5px;
    }

    .status-subtitle {
        font-size: 1rem;
        color: #64748b;
        margin: 0;
    }

     pulse-premium {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }

    /* Document Layout */
    .document-content-premium {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(20px);
        border-radius: 25px;
        padding: 40px;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .document-layout {
        display: flex;
        gap: 30px;
    }

    .main-content {
        flex: 2;
    }

    .sidebar-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 25px;
    }

    /* Section Headers */
    .section-header-premium {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 25px;
        padding-bottom: 15px;
        border-bottom: 2px solid rgba(59, 130, 246, 0.1);
    }

    .section-icon-premium {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
        box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
    }

    .section-title-premium h3 {
        font-size: 1.4rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 3px;
    }

    .section-title-premium p {
        font-size: 0.9rem;
        color: #64748b;
        margin: 0;
    }

    /* Sections */
    .info-section-premium,
    .coverage-section-premium,
    .insured-section-premium,
    .vehicle-section-premium,
    .specifications-section-premium,
    .technical-section-premium,
    .financial-section-premium {
        margin-bottom: 40px;
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(226, 232, 240, 0.8);
    }

    /* Info Cards */
    .info-cards-grid,
    .insured-cards-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
    }

    .info-card-premium {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(226, 232, 240, 0.8);
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 20px;
        position: relative;
        overflow: hidden;
    }

    .info-card-premium::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    }

    .info-card-premium.highlight::before {
        background: linear-gradient(90deg, #f59e0b, #d97706);
    }

    .info-card-premium:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    }

    .card-icon-premium {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        flex-shrink: 0;
    }

    .card-icon-premium.primary {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    }

    .card-icon-premium.success {
        background: linear-gradient(135deg, #10b981, #059669);
    }

    .card-icon-premium.info {
        background: linear-gradient(135deg, #06b6d4, #0891b2);
    }

    .card-icon-premium.warning {
        background: linear-gradient(135deg, #f59e0b, #d97706);
    }

    .card-icon-premium.secondary {
        background: linear-gradient(135deg, #6b7280, #4b5563);
    }

    .card-icon-premium.danger {
        background: linear-gradient(135deg, #ef4444, #dc2626);
    }

    .card-content-premium {
        flex: 1;
    }

    .card-label {
        font-size: 0.9rem;
        color: #64748b;
        font-weight: 500;
        margin-bottom: 5px;
    }

    .card-value {
        font-size: 1.2rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 3px;
        line-height: 1.3;
    }

    .card-label-en {
        font-size: 0.8rem;
        color: #94a3b8;
        font-style: italic;
    }

    /* Coverage Timeline */
    .coverage-timeline {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 30px;
        margin: 30px 0;
    }

    .timeline-item {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        padding: 25px;
        text-align: center;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
        min-width: 200px;
    }

    .timeline-icon {
        margin-bottom: 15px;
    }

    .timeline-icon i {
        font-size: 2rem;
        color: #3b82f6;
    }

    .timeline-label {
        font-size: 0.9rem;
        color: #64748b;
        margin-bottom: 8px;
    }

    .timeline-date {
        font-size: 1.1rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 5px;
    }

    .timeline-label-en {
        font-size: 0.8rem;
        color: #94a3b8;
        font-style: italic;
    }

    .timeline-connector {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .connector-line {
        width: 60px;
        height: 3px;
        background: linear-gradient(90deg, #3b82f6, #1d4ed8);
        border-radius: 2px;
    }

    .connector-icon {
        color: #3b82f6;
        font-size: 1.2rem;
    }

    /* Vehicle Section */
    .vehicle-highlight-card {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 25px;
        display: flex;
        align-items: center;
        gap: 25px;
        box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
    }

    .highlight-icon {
        width: 80px;
        height: 80px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        backdrop-filter: blur(10px);
    }

    .highlight-content {
        flex: 1;
    }

    .highlight-label {
        font-size: 1rem;
        opacity: 0.9;
        margin-bottom: 8px;
    }

    .highlight-value {
        font-size: 2rem;
        font-weight: 800;
        margin-bottom: 5px;
    }

    .highlight-label-en {
        font-size: 0.9rem;
        opacity: 0.8;
    }

    .vehicle-details-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }

    .vehicle-card {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        padding: 20px;
        display: flex;
        align-items: center;
        gap: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
    }

    .vehicle-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .vehicle-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #10b981, #059669);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
    }

    .vehicle-info {
        flex: 1;
    }

    .vehicle-label {
        font-size: 0.9rem;
        color: #64748b;
        margin-bottom: 5px;
    }

    .vehicle-value {
        font-size: 1.1rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 3px;
    }

    .vehicle-label-en {
        font-size: 0.8rem;
        color: #94a3b8;
        font-style: italic;
    }

    /* Specifications Section */
    .specifications-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }

    .spec-card {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
    }

    .spec-card.wide {
        grid-column: span 2;
    }

    .spec-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .spec-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #f59e0b, #d97706);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
        margin: 0 auto 15px;
    }

    .spec-content {
        text-align: center;
    }

    .spec-label {
        font-size: 0.9rem;
        color: #64748b;
        margin-bottom: 8px;
    }

    .spec-value {
        font-size: 1.1rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 5px;
    }

    .spec-label-en {
        font-size: 0.8rem;
        color: #94a3b8;
        font-style: italic;
    }

    /* Technical Details */
    .technical-details-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
    }

    .technical-card {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        padding: 25px;
        display: flex;
        align-items: center;
        gap: 20px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
    }

    .technical-card.full-width {
        grid-column: 1 / -1;
    }

    .technical-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .technical-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #6b7280, #4b5563);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
        flex-shrink: 0;
    }

    .technical-content {
        flex: 1;
    }

    .technical-label {
        font-size: 0.9rem;
        color: #64748b;
        margin-bottom: 5px;
    }

    .technical-value {
        font-size: 1.1rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 3px;
    }

    .technical-label-en {
        font-size: 0.8rem;
        color: #94a3b8;
        font-style: italic;
    }

    /* Financial Section */
    .tariff-note {
        background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 25px;
        display: flex;
        align-items: center;
        gap: 15px;
        border-left: 4px solid #3b82f6;
    }

    .note-icon {
        color: #3b82f6;
        font-size: 1.5rem;
    }

    .note-text {
        font-size: 1rem;
        color: #1e293b;
        font-weight: 500;
    }

    .financial-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .financial-item {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .financial-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #10b981, #059669);
    }

    .financial-item.total {
        background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
        color: white;
    }

    .financial-item.total::before {
        background: linear-gradient(90deg, #f59e0b, #d97706);
    }

    .financial-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .item-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #10b981, #059669);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
        margin: 0 auto 15px;
    }

    .financial-item.total .item-icon {
        background: rgba(255, 255, 255, 0.2);
    }

    .item-content {
        text-align: center;
    }

    .item-label {
        font-size: 0.9rem;
        color: #64748b;
        margin-bottom: 8px;
    }

    .financial-item.total .item-label {
        color: rgba(255, 255, 255, 0.8);
    }

    .item-value {
        font-size: 1.2rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 5px;
    }

    .financial-item.total .item-value {
        color: white;
        font-size: 1.4rem;
    }

    .item-label-en {
        font-size: 0.8rem;
        color: #94a3b8;
        font-style: italic;
    }

    .financial-item.total .item-label-en {
        color: rgba(255, 255, 255, 0.7);
    }

    .amount-in-words {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-radius: 15px;
        padding: 25px;
        display: flex;
        align-items: center;
        gap: 20px;
        border: 2px dashed #cbd5e1;
    }

    .words-icon {
        color: #64748b;
        font-size: 1.5rem;
    }

    .words-content {
        flex: 1;
    }

    .words-label {
        font-size: 0.9rem;
        color: #64748b;
        margin-bottom: 8px;
    }

    .words-placeholder {
        font-size: 1.1rem;
        color: #94a3b8;
        margin-bottom: 5px;
    }

    .words-label-en {
        font-size: 0.8rem;
        color: #94a3b8;
        font-style: italic;
    }

    /* Sidebar Sections */
    .qr-section-premium,
    .issue-date-section {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 25px;
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(226, 232, 240, 0.8);
        text-align: center;
    }

    .qr-header,
    .date-header {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 15px;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 2px solid rgba(59, 130, 246, 0.1);
    }

    .qr-icon,
    .date-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.1rem;
    }

    .qr-title h4,
    .date-title h4 {
        font-size: 1.2rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 3px;
    }

    .qr-title p,
    .date-title p {
        font-size: 0.9rem;
        color: #64748b;
        margin: 0;
    }

    .qr-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }

    .qr-code {
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .qr-label {
        font-size: 0.9rem;
        font-weight: 600;
        color: #64748b;
        background: rgba(59, 130, 246, 0.1);
        padding: 8px 15px;
        border-radius: 20px;
    }

    .date-details {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 20px;
    }

    .date-item {
        text-align: center;
    }

    .date-label {
        font-size: 0.9rem;
        color: #64748b;
        margin-bottom: 8px;
    }

    .date-value {
        font-size: 1.1rem;
        font-weight: 700;
        color: #1e293b;
        background: rgba(59, 130, 246, 0.1);
        padding: 8px 12px;
        border-radius: 10px;
    }

    .date-separator {
        font-size: 1.2rem;
        color: #cbd5e1;
        font-weight: 300;
    }

    /* Responsive Design */
     (max-width: 1200px) {
        .document-layout {
            flex-direction: column;
        }

        .sidebar-content {
            flex-direction: row;
            gap: 20px;
        }

        .qr-section-premium,
        .issue-date-section {
            flex: 1;
        }
    }

     (max-width: 768px) {
        .insurance-document-container {
            padding: 15px;
        }

        .control-header {
            flex-direction: column;
            gap: 20px;
        }

        .document-content-premium {
            padding: 25px;
        }

        .info-cards-grid,
        .insured-cards-grid,
        .vehicle-details-grid,
        .specifications-grid,
        .technical-details-grid,
        .financial-grid {
            grid-template-columns: 1fr;
        }

        .coverage-timeline {
            flex-direction: column;
            gap: 20px;
        }

        .timeline-connector {
            transform: rotate(90deg);
        }

        .sidebar-content {
            flex-direction: column;
        }

        .spec-card.wide {
            grid-column: span 1;
        }

        .technical-card.full-width {
            grid-column: span 1;
        }

        .vehicle-highlight-card {
            flex-direction: column;
            text-align: center;
        }

        .amount-in-words {
            flex-direction: column;
            text-align: center;
        }
    }

     (max-width: 480px) {
        .btn-premium {
            padding: 10px 15px;
            font-size: 12px;
        }

        .status-title {
            font-size: 1.2rem;
        }

        .section-title-premium h3 {
            font-size: 1.2rem;
        }

        .card-value,
        .vehicle-value,
        .spec-value,
        .technical-value,
        .item-value {
            font-size: 1rem;
        }

        .highlight-value {
            font-size: 1.5rem;
        }
    }

    /* Print Styles */
     print {
        .control-panel-premium {
            display: none !important;
        }

        .insurance-document-container {
            background: white !important;
            padding: 20px !important;
        }

        .document-content-premium {
            background: white !important;
            box-shadow: none !important;
            border: none !important;
        }

        .document-layout {
            flex-direction: column;
        }

        .sidebar-content {
            flex-direction: row;
            justify-content: space-between;
        }

        * {
            box-shadow: none !important;
            text-shadow: none !important;
        }

        .info-card-premium,
        .vehicle-card,
        .spec-card,
        .technical-card,
        .financial-item,
        .qr-section-premium,
        .issue-date-section {
            border: 1px solid #e2e8f0 !important;
            background: white !important;
        }

        .vehicle-highlight-card,
        .financial-item.total {
            background: #f8fafc !important;
            color: #1e293b !important;
            border: 2px solid #1e293b !important;
        }
    }
</style>









<div class="row justify-content-center d-print-none  " ng-if="ctrl.switchFlag==5">

    <div class="col-lg-12   col-md-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12">
        <div class="card  navglassColor ">
            <div class="card-header ">

                <div class="btn-group cairo" dir="ltr" role="group">
                    <button type="button" disabled class="btn btn-primary">واجهة بيانات المنظومة  القديمة </button>
                    <button type="button" ng-click="ctrl.Back()" class="btn btn-primary "> <span class="m-2">واجهة إصدار وثيقة تامين إجباري</span></button>
                    <button type="button" ng-click="ctrl.BtnHomePage()" class="btn btn-success bi bi-house-fill"> <span class="m-2">الرئيسية</span></button>
                </div>
                <div class="col-3  float-end ">
                    <button type="button" ng-click="ctrl.RepleacOldDoc()" class="btn btn-primary  border-ridues  cairo   " style="width:100%"> تغير ملكية </button>

                </div>
            </div>


        </div>

        <div class="  ">


            <div class="row    ">
                <div class="col-8 mt-2">

                </div>


                <div class="col-1 mt-2">

                </div>

            </div>
            <div class="row  mt-4 " style="height:80vh; padding:25px;overflow-y:scroll">
                <div class="col-12  ">
                
                    <table class="table table-success table-bordered table-hover">
                        <tr class="bg-primary  text-white">
                            <th class="text-center">#</th>
                            <th class="text-center">   نوع الوثيقة   </th>
                            <th class="text-center">  رقم الوثيقة السابقة </th>
                            <th class="text-center">    رقم الوثيقة الجديدة  </th>
                            <th class="text-center">     اسم المؤمن له     </th>
                            <th class="text-center">         نوع السيارة     </th>
                            <th class="text-center">تاريخ الإدخال </th>
                            <th class="text-center">  المدخل </th>
                        </tr>
                        <tr class="  p-0" ng-repeat="x in ctrl.OldOWner">
                            <td class="text-center">{{$index + 1}}</td>
                            <td class="text-center">{{x.DocType==4? 'وثيقة من النظام السابق'  :'' }}</td>
                            <td class="text-center " dir="ltr">
                                {{x.OldDocSn}}
                            </td>

                            <td class="text-center">{{x.NewDocSN}}</td>
                            <td class="text-center">{{x.cusNume}}</td>
                            <td class="text-center">{{x.carName}}</td>
                            <td class="text-center">{{x.InsertDate}}</td>
                            <td class="text-center">{{x.InsertBy}}</td>






                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
















<div class="modal fade cairo" id="NewItem" tabindex="-1" data-bs-backdrop="static" aria-labelledby="{{ctrl.Title}}" aria-hidden="true">
    <div class="modal-dialog modal-xl 	">
        <div class="modal-content gradentModelGray">
            <div class="modal-header text-light bg-primary">
                <h5 class="modal-title" id="exampleModalLabel">{{ctrl.DlTitle}}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body cairo bg-primary">
                <form name="mForm" autocomplete="off" novalidate>
                    <div class="row bg-primary">
                        <div class="col-9 bg-primary">

                            <fieldset class="row text-white mt-1 gradientModelBackg" style="border:1px solid #808080">
                                <legend class="fs-5">التأمين</legend>


                                <div class="col-4">
                                    <label for="Com_ID"> الجهة <span ng-show="mForm.Com_ID.$error.required" class="text-danger">   * </span>  </label>

                                    <md-autocomplete flex required
                                                     md-require-match="true"
                                                     md-search-text="ctrl.Com_Name"
                                                     md-input-name="Com_ID"
                                                     md-selected-item="ctrl.selected_Comp_ID"
                                                     md-no-cache="true"
                                                     ng-style=" mForm.Com_ID.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}"
                                                     md-selected-item-change="ctrl.SelectedCompanies()" md-items="item in ctrl.SearchCompanies(ctrl.Com_Name)"
                                                     md-item-text="item.Name" md-min-length="0" placeholder="" input-aria-labelledby="cityName">
                                        <md-item-template>
                                            <span md-highlight-text="ctrl.Com_Name" md-highlight-flags="^i">{{item.Name}}</span>
                                        </md-item-template>
                                        <md-not-found>
                                            العنصر     "{{ctrl.Com_Name}}"  غير موجود.

                                        </md-not-found>
                                    </md-autocomplete>

                                </div>
                                <div class="col-4 cairo">
                                    <label for="Ins_Type"> نوع التأمين  <span ng-show="mForm.Ins_Type.$error.required" class="text-danger float-end ">*</span></label>
                                    <select required form-control name="Ins_Type" class="form-select text-center " ng-model="ctrl.Ins_Type" ng-change="ctrl.a400015()"
                                            ng-style="mForm.Ins_Type.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                            ng-options="x.ID as x.Name for x in ctrl.ct_li"></select>



                                </div>
                                <div class="col-4 cairo">
                                    <label for="Ins_Type"> مدة التأمين  <span ng-show="mForm.Dur_Type.$error.required" class="text-danger float-end ">*</span></label>
                                    <select required form-control name="Dur_Type" class="form-select text-center " ng-model="ctrl.Dur_Type" ng-change="ctrl.a400020()"
                                            ng-style="mForm.Dur_Type.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                            ng-options="x.ID as x.Name for x in ctrl.DurTypes"></select>



                                </div>
                                <div class="col-4 cairo ">
                                    <label for="Sdate">تاريخ بداية التأمين  <span ng-show="mForm.Sdate.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required name="Sdate" type="date" disabled readonly
                                           ng-style="mForm.Sdate.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj.Sdate" class="form-control text-center" />



                                </div>
                                <div class="col-4 cairo ">
                                    <label for="Edate"> تاريخ نهاية التأمين    <span ng-show="mForm.Edate.$error.required" class="text-danger float-end ">*</span></label>
                                    <input required name="Edate" type="date" disabled readonly
                                           ng-style="mForm.Edate.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj.Edate" class="form-control text-center" />


                                </div>

                                <div class="col-4 cairo ">
                                    <label for="c_masterID">الغرض من الترخيص  <span ng-show="mForm.c_masterID.$error.required" class="text-danger float-end  ">*</span></label>
                                    <select required name="c_masterID" class="form-select text-center" ng-change="ctrl.a40005()"
                                            ng-style="mForm.c_masterID.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                            ng-model="ctrl.MainObj.c_masterID"
                                            ng-options="x.ID as x.Name for x in ctrl.MasterObj"></select>

                                </div>
                                <div class="col-4 cairo ">
                                    <label for="c_DetailsID">مواصفات السيارة <span ng-if="mForm.c_DetailsID.$error.required" class="text-danger float-end  ">*</span></label>
                                    <select required name="c_DetailsID" class="form-select text-center" ng-change="ctrl.a40006()"
                                            ng-style="mForm.c_DetailsID.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                            ng-model="ctrl.Selected_c_DetailsID"
                                            ng-options="x.ID as x.Name for x in ctrl.PricDeObj"></select>



                                </div>
                                <div class="col-2 cairo">

                                    <label for="NoOfPassen">
                                        عدد الركاب   <span ng-if="mForm.NoOfPassen.$error.required" class="text-danger float-end  ">*</span>
                                    </label>
                                    <input required name="NoOfPassen" type="number" min="{{ctrl.Selected_c_DetailsID.MinNoPass}}" max="{{ctrl.Selected_c_DetailsID.MaxNoPass}}"
                                           ng-style="mForm.NoOfPassen.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj.NoOfPassen" class="form-control text-center" ng-change="ctrl.a400016()">
                                    <span ng-show="mForm.NoOfPassen.$error.max" class="text-danger float-end m-2">أقصى قيمة {{ctrl.Selected_c_DetailsID.MaxNoPass}}</span>
                                    <span ng-show="mForm.NoOfPassen.$error.min" class="text-danger float-end m-2">اقل قيمة {{ctrl.Selected_c_DetailsID.MinNoPass}}</span>

                                </div>
                                <div class="col-2 ">
                                    <label for="PassPrice"> سعر الراكب  <span ng-show="mForm.PassPrice.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required name="PassPrice" type="number" min="0" disabled readonly
                                           ng-style="mForm.PassPrice.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj.PassPrice" class="form-control text-center" />


                                </div>
                                <div class="col-4 mb-2">

                                    <label for="c_load">الحمولة <span ng-show="mForm.c_load.$error.required" class="text-danger float-end ">*</span></label>
                                    <input required readonly name="c_load" type="number" min="0"
                                           ng-style="mForm.c_load.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj.c_load" max="" class="form-control text-center">


                                    <span ng-show="mForm.c_load.$error.max" class="text-danger float-end m-2">خطأ</span>

                                </div>

                            </fieldset>


                            <fieldset class="row mt-2 mb-2 gradientModelBackg text-white" style="border:1px solid #808080">

                                <legend class="fs-5">  المؤمن له </legend>
                                <div class="col-4">
                                    <label for="city_ID"> جهة الترخيص <span ng-if="mForm.city_ID.$error.required" class="text-danger">   * </span>  </label>

                                    <md-autocomplete flex required
                                                     md-require-match="true"
                                                     md-search-text="ctrl.searchCity"
                                                     md-input-name="city_ID"
                                                     md-selected-item="ctrl.selected_city_license_Item"
                                                     md-no-cache="true"
                                                     md-input-minlength="1"
                                                     md-input-maxlength="18"
                                                     ng-style=" mForm.city_ID.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}"
                                                     md-selected-item-change="ctrl.Selected_City_Item(item)" md-items="item in ctrl.SearchCity(ctrl.searchCity)"
                                                     md-item-text="item.Name" placeholder="قم بإدخال اول حرف وإختر من القائمة" input-aria-labelledby="cityName">
                                        <md-item-template>
                                            <span md-highlight-text="ctrl.searchCity" md-highlight-flags="^i">{{item.Name}}</span>
                                        </md-item-template>
                                        <md-not-found>
                                            العنصر     "{{ctrl.searchCity}}"  غير موجود.

                                        </md-not-found>
                                    </md-autocomplete>

                                </div>
                                <div class="col-4">
                                    <label for="c_Name"> اسم الزبون  <span ng-show="mForm.c_Name.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required ng-disabled="ctrl.selected_Comp_ID.IsDisabed" name="c_Name" type="text"
                                           ng-style="mForm.c_Name.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj.c_Name" class="form-control">

                                </div>

                                <div class="col-4">
                                    <label for="c_Address">العنوان <span ng-if="mForm.c_Address.$error.required" class="text-danger">   * </span>  </label>

                                    <md-autocomplete flex required
                                                     md-require-match="true"
                                                     md-search-text="ctrl.SearchCity1"
                                                     md-input-name="c_Address"
                                                     md-selected-item="ctrl.c_Address"
                                                     md-no-cache="true"
                                                     md-input-minlength="1"
                                                     md-input-maxlength="18"
                                                     ng-style=" mForm.c_Address.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}"
                                                     md-selected-item-change="ctrl.Selected_City_Item_Addres()" md-items="item in ctrl.SearchCityAddres(ctrl.SearchCity1)"
                                                     md-item-text="item.Name" placeholder="قم بإدخال اول حرف وإختر من القائمة" input-aria-labelledby="c_Address">
                                        <md-item-template>
                                            <span md-highlight-text="ctrl.SearchCity1" md-highlight-flags="^i">{{item.Name}}</span>
                                        </md-item-template>
                                        <md-not-found>
                                            العنصر     "{{ctrl.searchCity1}}"  غير موجود.

                                        </md-not-found>
                                    </md-autocomplete>

                                </div>
                                <div class="col-4">
                                    <label for="c_Address">العنوان <span ng-show="mForm.c_Address.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required name="c_Address" type="text"
                                           ng-style="mForm.c_Address.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj.c_Address" class="form-control ">

                                </div>

                                <div class="col-4 mb-2">
                                    <label for="c_Address">رقم الهاتف <span ng-show="mForm.c_phone.$error.required || mForm.c_phone.$error.c_phone" class="text-danger float-end ">*</span></label>
                                    <input required name="c_phone" type="number" @*pattern="^(?:0|\(?\+33\)?\s?|0033\s?)[1-79](?:[\.\-\s]?\d\d){4}$"*@
                                           ng-style="mForm.c_phone.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj.c_phone" class="form-control ">

                                </div>

                            </fieldset>
                            <fieldset class="row mt-2 text-white gradientModelBackg " style="border:1px solid #808080">

                                <legend class="fs-5">    بيانات المركبة :</legend>


                                <div class="col-4">
                                    <label for="Ib_num">  رقم  اللوحة المعدنية  <span ng-show="mForm.Ib_num.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required name="Ib_num" type="text"
                                           ng-style="mForm.Ib_num.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           pattern="^[0-9-]+$" ng-model="ctrl.MainObj.Ib_num" class="form-control ">

                                </div>


                                <div class="col-4">
                                    <label for="car_ID">نوع السيارة  <span ng-show="mForm.car_ID.$error.required" class="text-danger">   * </span>  </label>

                                    <md-autocomplete flex required
                                                     md-require-match="true"
                                                     md-search-text="ctrl.searchCar"
                                                     md-selected-item="ctrl.selected_Car_Brand_Item"
                                                     md-input-name="car_ID"
                                                     md-no-cache="true"
                                                     md-input-minlength="1"
                                                     md-input-maxlength="18"
                                                     ng-style=" mForm.car_ID.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}"
                                                     md-selected-item-change="ctrl.SelectedCarChange(item)" md-items="item in ctrl.SearchCar(ctrl.searchCar)"
                                                     md-item-text="item.Name" placeholder="قم بإدخال اول حرف وإختر من القائمة" input-aria-labelledby="Name">
                                        <md-item-template>
                                            <span md-highlight-text="ctrl.searchCar" md-highlight-flags="^i">{{item.Name}}</span>
                                        </md-item-template>
                                        <md-not-found>
                                            العنصر     "{{ctrl.searchCar}}"  غير موجود.

                                        </md-not-found>
                                    </md-autocomplete>

                                </div>
                                <div class="col-4">
                                    <label for="car_color">لون السيارة  <span ng-if="mForm.car_color.$error.required" class="text-danger">   * </span>  </label>

                                    <md-autocomplete flex required
                                                     md-search-text="ctrl.searchColor"
                                                     md-input-name="car_color"
                                                     md-no-cache="true"
                                                     md-require-match="true"
                                                     md-input-minlength="1"
                                                     md-input-maxlength="18"
                                                     md-selected-item="ctrl.selected_Car_Color_Item"
                                                     ng-style=" mForm.car_color.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}"
                                                     md-selected-item-change="ctrl.SelectedColorChange(item)" md-items="item in ctrl.SearchColor(ctrl.searchColor)"
                                                     md-item-text="item.Name" md-min-length="0" placeholder="قم بإدخال اول حرف وإختر من القائمة" input-aria-labelledby="Name">
                                        <md-item-template>
                                            <span md-highlight-text="ctrl.searchColor" md-highlight-flags="^i">{{item.Name}}</span>
                                        </md-item-template>
                                        <md-not-found>
                                            العنصر     "{{ctrl.searchColor}}"  غير موجود.

                                        </md-not-found>
                                    </md-autocomplete>

                                </div>

                                <div class="col-4">
                                    <label for="chass_num"> رقم الهيكل <span ng-show="mForm.chass_num.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required name="chass_num" type="text"
                                           ng-style="mForm.chass_num.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj.chass_num" class="form-control ">


                                </div>
                                <div class="col-4">
                                    <label for="Eng_num"> رقم المحرك <span ng-show="mForm.chass_num.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required name="Eng_num" type="text"
                                           ng-style="mForm.Eng_num.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj.Eng_num" class="form-control ">


                                </div>
                                <div class="col-4">

                                    <label for="eng_cap">قوة المحرك  <span ng-show="mForm.eng_cap.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required name="eng_cap" type="number"
                                           ng-style="mForm.eng_cap.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj.eng_cap" max="{{ctrl.Selected_c_DetailsID.MaxCap_eng}}" min="{{ctrl.Selected_c_DetailsID.MinCap_eng}}"
                                           class="form-control text-center">

                                    <span ng-show="mForm.eng_cap.$error.max" class="text-danger float-end m-2">أقصى قيمة {{ctrl.Selected_c_DetailsID.MaxCap_eng}}</span>
                                    <span ng-show="mForm.eng_cap.$error.min" class="text-danger float-end m-2">أقل قيمة {{ctrl.Selected_c_DetailsID.MinCap_eng}}</span>



                                </div>



                                <div class="col-4 ">
                                    <label for="m_year">سنة الصنع <span ng-show="mForm.m_year.$error.required" class="text-danger float-end  ">*</span></label>
                                    <select required name="m_year" class="form-select text-center"
                                            ng-style="mForm.m_year.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                            ng-model="ctrl.MainObj.m_year"
                                            ng-options="x as x for x in ctrl.m_Years|orderBy:'-'"></select>
                                </div>
                                <div class="col-4">
                                    <label for="cityName">بلد الصنع  <span ng-show="mForm.con_ID.$error.required" class="text-danger">   * </span>  </label>

                                    <md-autocomplete flex required
                                                     md-require-match="true"
                                                     md-search-text="ctrl.searchContory"
                                                     md-selected-item="ctrl.selected_manfictor_Item"
                                                     md-input-name="con_ID"
                                                     md-no-cache="true"
                                                     md-input-minlength="1"
                                                     md-input-maxlength="18"
                                                     ng-style=" mForm.con_ID.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}"
                                                     md-selected-item-change="ctrl.selectedContryChange(item)" md-items="item in ctrl.SearchContory(ctrl.searchContory)"
                                                     md-item-text="item.Name" placeholder="قم بإذخال اول حرف وإختر من القائمة" input-aria-labelledby="Name">
                                        <md-item-template>
                                            <span md-highlight-text="ctrl.searchContory" md-highlight-flags="^i">{{item.Name}}</span>
                                        </md-item-template>
                                        <md-not-found>
                                            العنصر     "{{ctrl.searchContory}}"  غير موجود.

                                        </md-not-found>
                                    </md-autocomplete>

                                </div>


                                <div class="col-4">

                                    <label for="pap_no">رقم المطبوعة<span ng-if="mForm.pap_no.$error.required" class="text-danger float-end ">*</span></label>
                                    <input required name="pap_no" type="number" min="0" ng-click="ctrl.IsExistPayperNum = 0"
                                           ng-style="mForm.pap_no.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj.pap_no" max="" class="form-control text-center">
                                    <span ng-if="ctrl.IsExistPayperNum == 1" class="text-danger float-end m-2">
                                        الرقم غير
                                        صحيح
                                    </span>


                                </div>
                            </fieldset>





                        </div>
                        <div class="col-3">

                            <fieldset class="row g-1 justify-content-center text-center text-white" style="border: 1px solid #808080; height: 75vh">

                                <legend class="fs-5">    إحتساب القسط   :</legend>
                                <label for="fname" class="col-12">صافي القسط</label>
                                <label style="height:50px" class="bg-dark border-1 border-white col-12 text-white text-center"
                                       ng-model="ctrl.NetPri" name="NetPri">{{ctrl.NetPri | number:'2'}}</label><br>
                                <label for="fname" class="col-12">الدمغة</label>
                                <label style="height:50px" class="bg-dark border-1 border-danger col-12 text-white text-center"
                                       name="NetPri">{{ctrl.t_Stamp | number:'2'}}</label><br>
                                <label for="fname" class="col-12">الضريبة</label>
                                <label style="height:50px" class="bg-dark border-1 border-danger col-12 text-white text-center"
                                       name="NetPri">{{ctrl.t_Tax | number:'2'}}</label><br>
                                <label for="fname" class="col-12">رسوم إشراف</label>
                                <label style="height:50px" class="bg-dark border-1 border-danger col-12 text-white text-center"
                                       name="NetPri">{{ctrl.Esh | number:'2'}}</label><br>
                                <label for="fname" class="col-12">م.الإصدار</label>
                                <label style="height:50px" class="bg-dark border-1 border-danger col-12 text-white text-center"
                                       name="NetPri">{{ctrl.t_Insu | number:'2'}}</label><br>
                                <label for="fname" class="col-12">المجموع</label>
                                <label style="height:100px;text-align:center" class="bg-dark border-1 border-danger col-12 text-white text-center fw-bolder fs-4"
                                       name="NetPri">{{ctrl.Totoal | number:'2'}}</label><br>
                            </fieldset>
                        </div>


                    </div>


                </form>
            </div>
            <div class="modal-footer justify-content-center bg-primary" ng-if="ctrl.showhFlag==1">
                <button type="button" class="btn btn-success col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" ng-disabled="mForm.$invalid" ng-click="ctrl.a40007()">
                    حفظ
                </button>
                <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">إلغاء الأمر</button>
            </div>
            <div class="modal-footer justify-content-center" ng-if="ctrl.showhFlag==2">
                <button type="button" class="btn btn-success col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" ng-disabled="mForm.$invalid" ng-click="ctrl.a40008()">
                    حفظ كجديد
                </button>
                <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">إلغاء الأمر</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="Dl_Message" tabindex="-1" data-bs-backdrop="static" aria-labelledby="{{ctrl.Title}}" aria-hidden="true">
    <div class="modal-dialog ">
        <div class="modal-content">
            <form name="FrmMsg" autocomplete="off">
                <div class="modal-header">
                    <h5 class="modal-title">{{ctrl.DlTitle}}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-auto">
                            <h4 class="m-4 text-danger">{{ctrl.DlMessage}}</h4>
                        </div>

                        <div class="col-12">
                            <label for="">سبب الحذف    <span ng-show="FrmMsg.DelNote.$error.required" class="text-danger float-end ">*</span></label>
                            <textarea required rows="8" class="form-control border border-1" maxlength="3000" ng-model="ctrl.DelNote" name="DelNote"></textarea>
                            <span ng-show="FrmMsg.DelNote.$error.maxlength" class="text-danger float-end ">تجاوزت الحد المسموح لطول الرسالة</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" ng-disabled="FrmMsg.$invalid" class="btn btn-success col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal" ng-click="ctrl.a45006()">
                        نعم
                    </button>
                    <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">لا</button>
                </div>
            </form>
        </div>
    </div>
</div>


<div class="row justify-content-center d-print-none  " ng-if="ctrl.switchFlag==4">

    <div class="col-lg-12   col-md-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12">
        <div class="card  navglassColor ">
            <div class="card-header ">

                <div class="btn-group cairo" dir="ltr" role="group">
                    <button type="button" disabled class="btn btn-primary">واجهة الخدمات الإضافية </button>
                    <button type="button" ng-click="ctrl.Back()" class="btn btn-primary bi bi-house-fill"> <span class="m-2">واجهة إصدار وثيقة تامين إجباري</span></button>
                    <button type="button" ng-click="ctrl.BtnHomePage()" class="btn btn-success bi bi-house-fill"> <span class="m-2">الرئيسية</span></button>
                </div>

            </div>


        </div>

        <div class="  ">


            <form class="row ">
                <div class="btn-group cairo col-4 mt-5 pl-3 px-4">

                    <input required class="form-control" type="text" ng-model="ctrl.SearchTxt" placeholder="    بحث أكتب رقم الوثيقة اورقم QR او رقم او رقم الهيكل...   ">
                    <button class="btn  btn-primary" ng-click="ctrl.a400031()">بحث</button>
                </div>


            </form>
            <div class="row  mt-4 " style="height:80vh; padding:25px;overflow-y:scroll">
                <div class="col-12">
                    <table class="table" style="font-size:14px">
                        <tr >
                            <th class="text-center">#</th>
                            <th class="text-center">نوع الوثيقة</th>
                            <th class="text-center">  رقم الوثيقة </th>
                            <th class="text-center">  المؤمن له </th>
                            <th class="text-center">  السيارة </th>
                            <th class="text-center">  رقم اللوحة </th>
                            <th class="text-center">  اللون   </th>
                            <th class="text-center"> القسط </th>
                            <th class="text-center"> الضرائب </th>
                            <th class="text-center"> الإجمالي </th>
                            <th class="text-center">تاريخ الإدخال </th>
                            <th class="text-center">  المدخل </th>

                            <th class="text-center" style="margin:0" colspan="2">العمليات</th>
                        </tr>
                        <tr  ng-repeat="x in ctrl.SerDocTb">
                            <td class="text-center">{{$index + 1}}</td>
                            <td class="text-center">{{x.TypeDesc}}</td>
                            <td class="text-center " dir="ltr">
                                {{x.Ins_SN}}
                            </td>
                            <td class="text-center">{{x.c_name}}</td>
                            <td class="text-center">{{x.car_name}}</td>
                            <td class="text-center">{{x.iron_bord}}</td>
                            <td class="text-center">{{x.color}}</td>
                            <td class="text-center">{{x.ins_val| number:'2'}}</td>
                            <td class="text-center">{{x.TaxVal| number:'2'}}</td>
                            <td class="text-center">{{x.total_tax | number:'2'}}</td>
                            <td class="text-center">{{x.InsertDate |date : "yyyy-MM-dd  a hh:mm" }}</td>
                            <td class="text-center">{{x.InsertBy}}</td>

                            <td>
                                <button type="button" class="btn btn-dark " ng-click="ctrl.a400032(x)">
                                    بدل فاقد
                                    <i class="fa-solid fa-eye"></i>
                                </button>
                            </td>

                            <td>
                                <button type="button" class="btn btn-primary" ng-click="ctrl.a400035(x)">
                                    تغير ملكية
                                    <i class="fa fa-file-text" aria-hidden="true"></i>

                                </button>
                            </td>

                            <td>
                                @*<button type="button" class="btn btn-primary" ng-click="ctrl.a400036(x)">
                                        تجديد
                                        <i class="fa fa-file-text" aria-hidden="true"></i>

                                    </button>*@
                            </td>

                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="ReNewDl" tabindex="-1" data-bs-backdrop="static" aria-labelledby="{{ctrl.Title}}" aria-hidden="true">
    <div class="modal-dialog  ">
        <form name="ReNewDl" class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">{{ctrl.DlTitle}}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row g-3">
                    <div class="col-12">
                        <label for="NoOfPassen">
                            رقم المطبوعة  <span ng-if="ReNewDl.DocNoTXT.$error.required" class="text-danger float-end  ">*</span>
                        </label>
                        <input required name="DocNoTXT" type="number"
                               ng-style="ReNewDl.DocNoTXT.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                               ng-model="ctrl.RenewObj.DocNoTXT" class="form-control text-center">
                        <span ng-if="ctrl.IsExistPayperNum == 1" class="text-danger float-end m-2">
                            الرقم غير
                            صحيح
                        </span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" ng-click="ctrl.a400033()">
                    إصدار بدل فاقد
                </button>
                <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">إلغاء الأمرا</button>
            </div>

        </form>
    </div>
</div>

<div class="modal fade cairo " id="ReplaceDoc" tabindex="-1" data-bs-backdrop="static" aria-labelledby="{{ctrl.Title}}" aria-hidden="true">
    <div class="modal-dialog modal-xl 	">
        <div class="modal-content gradentModelGray">
            <div class="modal-header">
                <h5 class="modal-title">{{ctrl.DlTitle}}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body cairo">
                <form name="ReplaceDocFrm" autocomplete="off" novalidate>
                    <div class="row">
                        <div class="col-9">



                            <fieldset class="row mt-2 mb-2 gradientModelBackg text-white" style="border:1px solid #808080">

                                <legend class="fs-5">  المؤمن له </legend>
                                <div class="col-4">
                                    <label for="city_ID"> جهة الترخيص <span ng-if="ReplaceDocFrm.city_ID.$error.required" class="text-danger">   * </span>  </label>

                                    <md-autocomplete flex required
                                                     md-require-match="true"
                                                     md-search-text="ctrl.searchCity"
                                                     md-input-name="city_ID"
                                                     md-selected-item="ctrl.selected_city_license_Item"
                                                     md-no-cache="true"
                                                     md-input-minlength="1"
                                                     md-input-maxlength="18"
                                                     ng-style=" ReplaceDocFrm.city_ID.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}"
                                                     md-selected-item-change="ctrl.Selected_City_Item(item)" md-items="item in ctrl.SearchCity(ctrl.searchCity)"
                                                     md-item-text="item.Name" placeholder="قم بإدخال اول حرف وإختر من القائمة" input-aria-labelledby="cityName">
                                        <md-item-template>
                                            <span md-highlight-text="ctrl.searchCity" md-highlight-flags="^i">{{item.Name}}</span>
                                        </md-item-template>
                                        <md-not-found>
                                            العنصر     "{{ctrl.searchCity}}"  غير موجود.

                                        </md-not-found>
                                    </md-autocomplete>

                                </div>
                                <div class="col-4">
                                    <label for="c_Name"> اسم الزبون  <span ng-show="ReplaceDocFrm.c_Name.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required ng-disabled="ctrl.selected_Comp_ID.IsDisabed" name="c_Name" type="text"
                                           ng-style="ReplaceDocFrm.c_Name.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.RePlaceObj.c_Name" class="form-control">

                                </div>

                                <div class="col-4">
                                    <label for="c_Address">العنوان <span ng-if="ReplaceDocFrm.c_Address.$error.required" class="text-danger">   * </span>  </label>

                                    <md-autocomplete flex required
                                                     md-require-match="true"
                                                     md-search-text="ctrl.SearchCity1"
                                                     md-input-name="c_Address"
                                                     md-selected-item="ctrl.c_Address"
                                                     md-no-cache="true"
                                                     md-input-minlength="1"
                                                     md-input-maxlength="18"
                                                     ng-style=" ReplaceDocFrm.c_Address.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}"
                                                     md-selected-item-change="ctrl.Selected_City_Item_Addres()" md-items="item in ctrl.SearchCityAddres(ctrl.SearchCity1)"
                                                     md-item-text="item.Name" placeholder="قم بإدخال اول حرف وإختر من القائمة" input-aria-labelledby="c_Address">
                                        <md-item-template>
                                            <span md-highlight-text="ctrl.SearchCity1" md-highlight-flags="^i">{{item.Name}}</span>
                                        </md-item-template>
                                        <md-not-found>
                                            العنصر     "{{ctrl.searchCity1}}"  غير موجود.

                                        </md-not-found>
                                    </md-autocomplete>

                                </div>
                                <div class="col-4">
                                    <label for="c_Address">العنوان <span ng-show="mForm.c_Address.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required name="c_Address" type="text"
                                           ng-style="ReplaceDocFrm.c_Address.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.RePlaceObj.c_Address" class="form-control ">

                                </div>

                                <div class="col-4 mb-2">
                                    <label for="c_Address">رقم الهاتف <span ng-show="mForm.c_phone.$error.required || mForm.c_phone.$error.c_phone" class="text-danger float-end ">*</span></label>
                                    <input required name="c_phone" type="number" @*pattern="^(?:0|\(?\+33\)?\s?|0033\s?)[1-79](?:[\.\-\s]?\d\d){4}$"*@
                                           ng-style="ReplaceDocFrm.c_phone.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.RePlaceObj.c_phone" class="form-control ">

                                </div>
                                <div class="col-4">
                                    <label for="Ib_num">  رقم  اللوحة المعدنية  <span ng-show="ReplaceDocFrm.Ib_num.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required name="Ib_num" type="text"
                                           ng-style="ReplaceDocFrm.Ib_num.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           pattern="^[0-9-]+$" ng-model="ctrl.RePlaceObj.Ib_num" class="form-control ">

                                </div>
                                <div class="col-4">
                                    <label for="NoOfPassen">
                                        رقم المطبوعة  <span ng-if="ReplaceDocFrm.DocNoTXT.$error.required" class="text-danger float-end  ">*</span>
                                    </label>
                                    <input required name="DocNoTXT" type="number"
                                           ng-style="ReplaceDocFrm.DocNoTXT.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.RePlaceObj.DocNoTXT" class="form-control text-center">
                                    <span ng-if="ctrl.IsExistPayperNum == 1" class="text-danger float-end m-2">
                                        الرقم غير
                                        صحيح
                                    </span>
                                </div>
                            </fieldset>






                        </div>
                        <div class="col-3">

                            <fieldset class="row g-1 justify-content-center text-center" style="border: 1px solid #808080; height: 75vh">

                                <legend class="fs-5">    إحتساب القسط   :</legend>
                                <label for="fname" class="col-12">صافي القسط</label>
                                <label style="height:50px" class="bg-dark border-1 border-white col-12 text-white text-center"
                                       ng-model="ctrl.NetPri" name="NetPri">{{ctrl.NetPri | number:'2'}}</label><br>
                                <label for="fname" class="col-12">الدمغة</label>
                                <label style="height:50px" class="bg-dark border-1 border-danger col-12 text-white text-center"
                                       name="NetPri">{{0.5 | number:'2'}}</label><br>
                                <label for="fname" class="col-12">الضريبة</label>
                                <label style="height:50px" class="bg-dark border-1 border-danger col-12 text-white text-center"
                                       name="NetPri">{{0 | number:'2'}}</label><br>
                                <label for="fname" class="col-12">رسوم إشراف</label>
                                <label style="height:50px" class="bg-dark border-1 border-danger col-12 text-white text-center"
                                       name="NetPri">{{0| number:'2'}}</label><br>
                                <label for="fname" class="col-12">م.الإصدار</label>
                                <label style="height:50px" class="bg-dark border-1 border-danger col-12 text-white text-center"
                                       name="NetPri">{{2 | number:'2'}}</label><br>
                                <label for="fname" class="col-12">المجموع</label>
                                <label style="height:100px;text-align:center" class="bg-dark border-1 border-danger col-12 text-white text-center fw-bolder fs-4"
                                       name="NetPri">{{2.5 | number:'2'}}</label><br>
                            </fieldset>
                        </div>


                    </div>


                </form>
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-success col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" ng-disabled="ReplaceDocFrm.$invalid" ng-click="ctrl.a400051()">
                    حفظ
                </button>
                <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">إلغاء الأمر</button>
            </div>

        </div>
    </div>
</div>


<div class="modal fade cairo " id="OldRenew" tabindex="-1" data-bs-backdrop="static" aria-labelledby="{{ctrl.Title}}" aria-hidden="true">
    <div class="modal-dialog modal-xl 	">
        <div class="modal-content gradentModelGray">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">{{ctrl.DlTitle}}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body cairo">
                <form name="OldRenew" autocomplete="off" novalidate>
                    <div class="row">
                        <div class="col-9">

                            <fieldset class="row text-white mt-1 gradientModelBackg" style="border:1px solid #808080">
                                <legend class="fs-5">التأمين</legend>

                                <div class="col-4 cairo ">
                                    <label for="Sdate">تاريخ بداية التأمين  <span ng-show="OldRenew.S_Date.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required name="S_Date" type="date" disabled readonly
                                           ng-style="OldRenew.S_Date.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj1.S_Date" class="form-control text-center" />



                                </div>
                                <div class="col-4 cairo ">
                                    <label for="Edate"> تاريخ نهاية التأمين    <span ng-show="OldRenew.E_date.$error.required" class="text-danger float-end ">*</span></label>
                                    <input required name="E_date" type="date"
                                           ng-style="OldRenew.E_date.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj1.E_date" class="form-control text-center" />


                                </div>

                                <div class="col-4 cairo ">
                                    <label for="c_masterID">الغرض من الترخيص  <span ng-show="OldRenew.c_masterID.$error.required" class="text-danger float-end  ">*</span></label>
                                    <select required name="c_masterID" class="form-select text-center" ng-change="ctrl.a4000666()"
                                            ng-style="OldRenew.c_masterID.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                            ng-model="ctrl.MainObj1.c_masterID"
                                            ng-options="x.ID as x.Name for x in ctrl.MasterObj"></select>

                                </div>
                                <div class="col-4 cairo ">
                                    <label for="c_DetailsID">مواصفات السيارة <span ng-if="OldRenew.c_DetailsID.$error.required" class="text-danger float-end  ">*</span></label>
                                    <select required name="c_DetailsID" class="form-select text-center" ng-change="ctrl.a40006123()"
                                            ng-style="OldRenew.c_DetailsID.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                            ng-model="ctrl.Selected_c_DetailsID"
                                            ng-options="x.ID as x.Name for x in ctrl.PricDeObj"></select>



                                </div>
                                <div class="col-2 cairo">

                                    <label for="NoOfPassen">
                                        عدد الركاب   <span ng-if="OldRenew.NoOfPassen.$error.required" class="text-danger float-end  ">*</span>
                                    </label>
                                    <input required name="NoOfPassen" type="number" min="{{ctrl.Selected_c_DetailsID.MinNoPass}}" max="{{ctrl.Selected_c_DetailsID.MaxNoPass}}"
                                           ng-style="OldRenew.NoOfPassen.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj1.NoOfPassen" class="form-control text-center" ng-change="ctrl.a400016()">
                                    <span ng-show="OldRenew.NoOfPassen.$error.max" class="text-danger float-end m-2">أقصى قيمة {{ctrl.Selected_c_DetailsID.MaxNoPass}}</span>
                                    <span ng-show="OldRenew.NoOfPassen.$error.min" class="text-danger float-end m-2">اقل قيمة {{ctrl.Selected_c_DetailsID.MinNoPass}}</span>

                                </div>
                                <div class="col-2 ">
                                    <label for="PassPrice"> سعر الراكب  <span ng-show="OldRenew.PassPrice.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required name="PassPrice" type="number" min="0"
                                           ng-style="OldRenew.PassPrice.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj1.PassPrice" class="form-control text-center" />


                                </div>
                                <div class="col-4 mb-2">

                                    <label for="c_load">الحمولة <span ng-show="OldRenew.c_load.$error.required" class="text-danger float-end ">*</span></label>
                                    <input required name="c_load" type="number" min="0"
                                           ng-style="OldRenew.c_load.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj1.c_load" max="" class="form-control text-center">


                                    <span ng-show="OldRenew.c_load.$error.max" class="text-danger float-end m-2">خطأ</span>

                                </div>

                            </fieldset>


                            <fieldset class="row mt-2 mb-2 gradientModelBackg text-white" style="border:1px solid #808080">

                                <legend class="fs-5">  المؤمن له </legend>
                                <div class="col-4">
                                    <label for="city_ID"> جهة الترخيص <span ng-if="OldRenew.city_ID.$error.required" class="text-danger">   * </span>  </label>

                                    <md-autocomplete flex required
                                                     md-require-match="true"
                                                     md-search-text="ctrl.searchCity"
                                                     md-input-name="city_ID"
                                                     md-selected-item="ctrl.selected_city_license_Item"
                                                     md-no-cache="true"
                                                     md-input-minlength="1"
                                                     md-input-maxlength="18"
                                                     ng-style=" OldRenew.city_ID.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}"
                                                     md-selected-item-change="ctrl.Selected_City_Item1(item)" md-items="item in ctrl.SearchCity(ctrl.searchCity)"
                                                     md-item-text="item.Name" placeholder="قم بإدخال اول حرف وإختر من القائمة" input-aria-labelledby="cityName">
                                        <md-item-template>
                                            <span md-highlight-text="ctrl.searchCity" md-highlight-flags="^i">{{item.Name}}</span>
                                        </md-item-template>
                                        <md-not-found>
                                            العنصر     "{{ctrl.searchCity}}"  غير موجود.

                                        </md-not-found>
                                    </md-autocomplete>

                                </div>
                                <div class="col-4">
                                    <label for="c_Name"> اسم الزبون  <span ng-show="OldRenew.c_Name.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required ng-disabled="ctrl.selected_Comp_ID.IsDisabed" name="c_Name" type="text"
                                           ng-style="OldRenew.c_Name.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj1.c_Name" class="form-control">

                                </div>

                                <div class="col-4">
                                    <label for="c_Address">العنوان <span ng-if="OldRenew.c_Address.$error.required" class="text-danger">   * </span>  </label>

                                    <md-autocomplete flex required
                                                     md-require-match="true"
                                                     md-search-text="ctrl.SearchCity1"
                                                     md-input-name="c_Address"
                                                     md-selected-item="ctrl.c_Address1"
                                                     md-no-cache="true"
                                                     md-input-minlength="1"
                                                     md-input-maxlength="18"
                                                     ng-style=" OldRenew.c_Address.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}"
                                                     md-selected-item-change="ctrl.Selected_City_Item_Addres1()" md-items="item in ctrl.SearchCityAddres(ctrl.SearchCity1)"
                                                     md-item-text="item.Name" placeholder="قم بإدخال اول حرف وإختر من القائمة" input-aria-labelledby="c_Address">
                                        <md-item-template>
                                            <span md-highlight-text="ctrl.SearchCity1" md-highlight-flags="^i">{{item.Name}}</span>
                                        </md-item-template>
                                        <md-not-found>
                                            العنصر     "{{ctrl.searchCity1}}"  غير موجود.

                                        </md-not-found>
                                    </md-autocomplete>

                                </div>
                                <div class="col-4">
                                    <label for="c_Address">العنوان <span ng-show="OldRenew.c_Address.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required name="c_Address" type="text"
                                           ng-style="OldRenew.c_Address.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj1.c_Address" class="form-control ">

                                </div>

                                <div class="col-4 mb-2">
                                    <label for="c_Address">رقم الهاتف <span ng-show="OldRenew.c_phone.$error.required || mForm.c_phone.$error.c_phone" class="text-danger float-end ">*</span></label>
                                    <input required name="c_phone" type="number"
                                           ng-style="OldRenew.c_phone.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj1.c_phone" class="form-control ">

                                </div>

                            </fieldset>
                            <fieldset class="row mt-2 text-white gradientModelBackg " style="border:1px solid #808080">

                                <legend class="fs-5">    بيانات المركبة :</legend>


                                <div class="col-4">
                                    <label for="Ib_num">  رقم  اللوحة المعدنية  <span ng-show="OldRenew.Ib_num.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required name="Ib_num" type="text"
                                           ng-style="OldRenew.Ib_num.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           pattern="^[0-9-]+$" ng-model="ctrl.MainObj1.Ib_num" class="form-control ">

                                </div>


                                <div class="col-4">
                                    <label for="car_ID">نوع السيارة  <span ng-show="OldRenew.car_ID.$error.required" class="text-danger">   * </span>  </label>

                                    <md-autocomplete flex required
                                                     md-require-match="true"
                                                     md-search-text="ctrl.searchCar"
                                                     md-selected-item="ctrl.selected_Car_Brand_Item"
                                                     md-input-name="car_ID"
                                                     md-no-cache="true"
                                                     md-input-minlength="1"
                                                     md-input-maxlength="18"
                                                     ng-style=" OldRenew.car_ID.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}"
                                                     md-selected-item-change="ctrl.SelectedCarChange1(item)" md-items="item in ctrl.SearchCar(ctrl.searchCar)"
                                                     md-item-text="item.Name" placeholder="قم بإدخال اول حرف وإختر من القائمة" input-aria-labelledby="Name">
                                        <md-item-template>
                                            <span md-highlight-text="ctrl.searchCar" md-highlight-flags="^i">{{item.Name}}</span>
                                        </md-item-template>
                                        <md-not-found>
                                            العنصر     "{{ctrl.searchCar}}"  غير موجود.

                                        </md-not-found>
                                    </md-autocomplete>

                                </div>
                                <div class="col-4">
                                    <label for="car_color">لون السيارة  <span ng-if="OldRenew.car_color.$error.required" class="text-danger">   * </span>  </label>

                                    <md-autocomplete flex required
                                                     md-search-text="ctrl.searchColor"
                                                     md-input-name="car_color"
                                                     md-no-cache="true"
                                                     md-require-match="true"
                                                     md-input-minlength="1"
                                                     md-input-maxlength="18"
                                                     md-selected-item="ctrl.selected_Car_Color_Item"
                                                     ng-style=" OldRenew.car_color.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}"
                                                     md-selected-item-change="ctrl.SelectedColorChange1(item)" md-items="item in ctrl.SearchColor(ctrl.searchColor)"
                                                     md-item-text="item.Name" md-min-length="0" placeholder="قم بإدخال اول حرف وإختر من القائمة" input-aria-labelledby="Name">
                                        <md-item-template>
                                            <span md-highlight-text="ctrl.searchColor" md-highlight-flags="^i">{{item.Name}}</span>
                                        </md-item-template>
                                        <md-not-found>
                                            العنصر     "{{ctrl.searchColor}}"  غير موجود.

                                        </md-not-found>
                                    </md-autocomplete>

                                </div>

                                <div class="col-4">
                                    <label for="chass_num"> رقم الهيكل <span ng-show="OldRenew.chass_num.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required name="chass_num" type="text"
                                           ng-style="OldRenew.chass_num.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj1.chass_num" class="form-control ">


                                </div>
                                <div class="col-4">
                                    <label for="Eng_num"> رقم المحرك <span ng-show="OldRenew.chass_num.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required name="Eng_num" type="text"
                                           ng-style="OldRenew.Eng_num.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj1.Eng_num" class="form-control ">


                                </div>
                                <div class="col-4">

                                    <label for="eng_cap">قوة المحرك  <span ng-show="OldRenew.eng_cap.$error.required" class="text-danger float-end  ">*</span></label>
                                    <input required name="eng_cap" type="number"
                                           ng-style="OldRenew.eng_cap.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj1.eng_cap" max="{{ctrl.Selected_c_DetailsID.MaxCap_eng}}" min="{{ctrl.Selected_c_DetailsID.MinCap_eng}}"
                                           class="form-control text-center">

                                    <span ng-show="OldRenew.eng_cap.$error.max" class="text-danger float-end m-2">أقصى قيمة {{ctrl.Selected_c_DetailsID.MaxCap_eng}}</span>
                                    <span ng-show="OldRenew.eng_cap.$error.min" class="text-danger float-end m-2">أقل قيمة {{ctrl.Selected_c_DetailsID.MinCap_eng}}</span>



                                </div>



                                <div class="col-4 ">
                                    <label for="m_year">سنة الصنع <span ng-show="OldRenew.m_year.$error.required" class="text-danger float-end  ">*</span></label>
                                    <select required name="m_year" class="form-select text-center"
                                            ng-style="OldRenew.m_year.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                            ng-model="ctrl.MainObj1.m_year"
                                            ng-options="x as x for x in ctrl.m_Years|orderBy:'-'"></select>
                                </div>
                                <div class="col-4">
                                    <label for="cityName">بلد الصنع  <span ng-show="OldRenew.con_ID.$error.required" class="text-danger">   * </span>  </label>

                                    <md-autocomplete flex required
                                                     md-require-match="true"
                                                     md-search-text="ctrl.searchContory"
                                                     md-selected-item="ctrl.selected_manfictor_Item"
                                                     md-input-name="con_ID"
                                                     md-no-cache="true"
                                                     md-input-minlength="1"
                                                     md-input-maxlength="18"
                                                     ng-style=" OldRenew.con_ID.$valid ? {'border':'green solid 1px'}:{'border':'red solid 1px '}"
                                                     md-selected-item-change="ctrl.selectedContryChange1(item)" md-items="item in ctrl.SearchContory(ctrl.searchContory)"
                                                     md-item-text="item.Name" placeholder="قم بإذخال اول حرف وإختر من القائمة" input-aria-labelledby="Name">
                                        <md-item-template>
                                            <span md-highlight-text="ctrl.searchContory" md-highlight-flags="^i">{{item.Name}}</span>
                                        </md-item-template>
                                        <md-not-found>
                                            العنصر     "{{ctrl.searchContory}}"  غير موجود.

                                        </md-not-found>
                                    </md-autocomplete>

                                </div>


                                <div class="col-4">

                                    <label for="pap_no">رقم المطبوعة<span ng-if="OldRenew.pap_no.$error.required" class="text-danger float-end ">*</span></label>
                                    <input required name="pap_no" type="number" min="0" ng-click="ctrl.IsExistPayperNum = 0"
                                           ng-style="OldRenew.pap_no.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj1.pap_no" max="" class="form-control text-center">
                                    <span ng-if="ctrl.IsExistPayperNum == 1" class="text-danger float-end m-2">
                                        الرقم غير
                                        صحيح
                                    </span>


                                </div>
                                <div class="col-4">

                                    <label for="oldDocNum">رقم الوثيقة السابقة <span ng-if="OldRenew.oldDocNum.$error.required" class="text-danger float-end ">*</span></label>
                                    <input required name="oldDocNum" type="text" ng-click="ctrl.IsExistPayperNum = 0"
                                           ng-style="OldRenew.oldDocNum.$valid ?{'border-color':'green'}:{'border-color':'red'}"
                                           ng-model="ctrl.MainObj1.OldDocNum" max="" class="form-control text-center">
                                    <span ng-if="ctrl.IsExistPayperNum == 1" class="text-danger float-end m-2">

                                    </span>


                                </div>
                            </fieldset>





                        </div>
                        <div class="col-3">

                            <fieldset class="row g-1 justify-content-center text-center" style="border: 1px solid #808080; height: 75vh">

                                <legend class="fs-5">    إحتساب القسط   :</legend>
                                <label for="fname" class="col-12">صافي القسط</label>
                                <label style="height:50px" class="bg-dark border-1 border-white col-12 text-white text-center"
                                       ng-model="ctrl.NetPri" name="NetPri">{{ctrl.NetPri | number:'2'}}</label><br>
                                <label for="fname" class="col-12">الدمغة</label>
                                <label style="height:50px" class="bg-dark border-1 border-danger col-12 text-white text-center"
                                       name="NetPri">{{ctrl.t_Stamp | number:'2'}}</label><br>
                                <label for="fname" class="col-12">الضريبة</label>
                                <label style="height:50px" class="bg-dark border-1 border-danger col-12 text-white text-center"
                                       name="NetPri">{{ctrl.t_Tax | number:'2'}}</label><br>
                                <label for="fname" class="col-12">رسوم إشراف</label>
                                <label style="height:50px" class="bg-dark border-1 border-danger col-12 text-white text-center"
                                       name="NetPri">{{ctrl.Esh | number:'2'}}</label><br>
                                <label for="fname" class="col-12">م.الإصدار</label>
                                <label style="height:50px" class="bg-dark border-1 border-danger col-12 text-white text-center"
                                       name="NetPri">{{ctrl.t_Insu | number:'2'}}</label><br>
                                <label for="fname" class="col-12">المجموع</label>
                                <label style="height:100px;text-align:center" class="bg-dark border-1 border-danger col-12 text-white text-center fw-bolder fs-4"
                                       name="NetPri">{{ctrl.Totoal | number:'2'}}</label><br>
                            </fieldset>
                        </div>


                    </div>


                </form>
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-success col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" ng-disabled="OldRenew.$invalid" ng-click="ctrl.a400052()">
                    حفظ
                </button>
                <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">إلغاء الأمر</button>
            </div>

        </div>
    </div>
</div>
