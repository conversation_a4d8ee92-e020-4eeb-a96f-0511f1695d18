﻿using Compulsory_insuApp.Models;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading;
using System.Web.Mvc;
namespace Compulsory_insuApp.Controllers
{
    public class a47Controller : Controller
    {
        Insh_AppsDBEntities db = new Insh_AppsDBEntities();
        //a40000
        public ActionResult a47001(Guid? Atu)
        {

            return View("Trav_IssuancePage");
            //if (db.AgUsers.Any(c => c.AgUserID == Atu))
            //{
            //    return View("Trav_IssuancePage");
            //}
            //else
            //{
            //    return Redirect("/#!/_LoginPage");
            //}

        }

        //a00053
        public ActionResult a47002(Guid UserID, Guid AgEncyID)
        { 
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.IsExsit(UserID.ToString()))
                    return Json(new { ErrorCode = 3 });
                Guid uid = UserID;
                var Objzn = db.Trav_Zoon.Where(c => c.Status == 1).Select(c => new
                {
                    ID = new{ c.ZoonID , c.ZoonText},
                    Name = c.ZoonName
                }).ToList();
                return Json(new { ErrorCode = 0, Objzn });

            }
            catch (Exception EX)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = EX.Message });
            }
        }

        //a00056Trav_InsuranceDocument
        public ActionResult a47003(int ByearDay)
        {
            try
            {
                Trav_insDocClass ic = new Trav_insDocClass();
                int Age = DateTime.Now.Year - ByearDay;
                var Ag = ic.Trav_GetAgeRow(Age);

                return Json(new { ErrorCode = 0, AgeVlue = Ag[0].AgEnd });

            }
            catch (Exception EX)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = EX.Message });
            }
        }



        //a00050
        public ActionResult a47004(Trav_insDocClass MainObj, Guid AgEncyID)
        {
            try
            {
               Insur_class ic = new Insur_class();
                Trav_insDocPrintClass trav_InsDocPrintClass = new Trav_insDocPrintClass();
                Guid uid = Guid.Parse(MainObj.UsID.ToString());
                var sn = db.Trav_SerialNums.Where(p => p.SerialNumID == MainObj.InsuNumber && p.Trav_SearStatus.ID == 1 && p.AgentID == AgEncyID).ToList();
                if (!sn.Any())
                    return Json(new { ErrorCode = 4 });
                var us = db.AgUsers.Where(p => p.AgUserID == uid && p.AgentId == AgEncyID
                && p.Status == 1).ToList();
                if (us.Count() != 1)
                    return Json(new { ErrorCode = 3 });
                var Agen = us[0].Agency;
                DateTime Bydate = new DateTime(MainObj.BDay, 01, 01);
                float ProftPer = (float?)us[0].Agency.ProftMargin ?? 0;
                var zo = db.Trav_Zoon.Find(MainObj.Zid);
                var vir = db.Trav_DocTax.Where(c => c.DocID == MainObj.InsID && c.Status == 1).ToList();
                int Age = DateTime.Now.Year - Bydate.Year;
                int dur = MainObj.Months;
                var Ag = MainObj.Trav_GetAgeRow(Age);
                var Dur = MainObj.Trav_GetDurtionRow(dur);
                var AgPer = Ag[0];
                var DurPer = Dur[0];
                float InsValue = (float?)zo.ZoonPrice ?? 0;
                float AgeValue = ((float)AgPer.AgePercentage * InsValue);
                float DurValue = (((float)DurPer.DurationPercentage) * (AgeValue));
                float Minp = Agen.IsAllowedDibt == false ? 0 : ((float)Agen.AllowedDibtValue * -1);
                float DocValue = DurValue;
                float ProfitVal = DocValue * (ProftPer / 100);
                Trav_InsuranceDocument du = db.Trav_InsuranceDocument.Find(MainObj.InsID);

                List<Trav_DocTax> DtaxList = new List<Trav_DocTax>();
                foreach (var it in vir)
                {
                    var v = it.Virebles;
                    Trav_DocTax obj = db.Trav_DocTax.Where(c => c.VirID == it.VirID).ToList()[0];
                    obj.Value = v.IsPer == false ? it.Virebles.Value : DocValue * (it.Virebles.Value);
                    obj.Value = v.IsrounsdedTo == 0.5 ? ic.RoudToPoint5((float)obj.Value) : obj.Value;
                    obj.Value = v.IsWithProfitMargin == true ? obj.Value + ProfitVal : obj.Value;
                    obj.IsPer = it.IsPer ?? false;
                    obj.Status = 1;
                    db.Trav_DocTax.Attach(obj);
                    db.Entry(obj).State = EntityState.Modified;
                    db.SaveChanges();
                    DtaxList.Add(obj);
                }
                float TaxValue = (float)DtaxList.Sum(c => c.Value);
                float DiffToround = ic.RondedValueTo5(DocValue + TaxValue);
                Trav_DocTax dtax = DtaxList.SingleOrDefault(c => c.VirID == Guid.Parse("0C4E4EB4-ED07-4192-8041-E3954E9792D2"));
                dtax.Value = dtax.Value + DiffToround;
                db.Trav_DocTax.Attach(dtax);
                db.SaveChanges();
                TaxValue = TaxValue + DiffToround;
                Agen.SoldValue = (Agen.SoldValue ?? 0) - (Agen.AgenCatID.ToString() == "DAC8AE95-9111-495E-911E-1DADB1AA3DE2" ? (du.TotWithTax ?? 0)
                     : Agen.AgenCatID.ToString() == "F74FCA70-2881-4385-864B-063D451708B9" ? (du.TotWithTax ?? 0) - (du.PfroftMargin ?? 0) : du.Total);
                Agen.Palance = ic.RondedValue((float)((Agen.AddPalance ?? 0) - (Agen.SoldValue ?? 0)));
                float pal = ((float?)Agen.Palance ?? 0) - (Agen.IsLocalCurrnecy == false ? DurValue + TaxValue : DocValue);
                //if (pal <= Minp)
                //{
                //    return Json(new { ErrorCode = 5, ErorrMessage = "رصيدك غير كافي لإجراء هذه المعاملة ،قم بمخاطبة مرؤسيك بالخصوص" });
                //}
                du.AgencyID = (Guid)us[0].AgentId;
                du.ZoonID = MainObj.Zid;
                du.AgeValue = AgeValue;
                du.DurValue = DurValue;
                du.TaxTotal = TaxValue;
                du.PfroftMargin = ProfitVal;
                du.PassportID = MainObj.PassportID;
                du.DurationMonths = dur;
                du.Total = DocValue;
                du.TotWithTax = (DocValue + TaxValue);
                du.NameCust = MainObj.CuName;
                du.Cus_Phone = MainObj.Cus_Phone;
                du.BitthDate = Bydate;
                du.IsInDeleteProgress = false;
                du.FromDate = MainObj.SDate;
                du.ToDate = MainObj.SDate.AddMonths((int)MainObj.Months);
                du.DurationTypeID = DurPer.DurationID;
                du.AgeTypeID = AgPer.AgeID;
                du.Age = Age;
                du.NameCustEN = MainObj.NameCustEN;
                du.Gender = MainObj.Gender;
                du.ClintDate = MainObj.clintDate;
                du.Status = 1;
                du.InsuNumber = MainObj.InsuNumber;
                du.StDucNum = du.InsertDate.Value.ToString("yyyy") + " " + du.Agency.AgNum + " " +
                du.InsuNumber.ToString() + " " + du.DocNum.ToString("D" + 5);
                du.UpdateDate = DateTime.Now;
                du.UpdatedBy =  MainObj.UsID;
                db.Entry(du).State = EntityState.Modified;
                db.SaveChanges();
                Agen.SoldValue = (Agen.SoldValue ?? 0) + (Agen.AgenCatID.ToString() == "DAC8AE95-9111-495E-911E-1DADB1AA3DE2" ? (du.TotWithTax ?? 0)
                : Agen.AgenCatID.ToString() == "F74FCA70-2881-4385-864B-063D451708B9" ? (du.TotWithTax ?? 0) - (du.PfroftMargin ?? 0) : du.Total);
                Agen.Palance = ic.RondedValue((float)((Agen.AddPalance ?? 0) - (Agen.SoldValue ?? 0)));
                db.Agency.Attach(Agen);
                db.Entry(Agen).State = EntityState.Modified;
                db.SaveChanges();
                Trav_SerialNums snobj = sn[0];
                snobj.Status = 8;
                db.Trav_SerialNums.Attach(snobj);
                db.Entry(snobj).State = EntityState.Modified;
                db.SaveChanges();
                var Doc = ic.Trav_PrintIns(du);
                var Taxs = ic.GetTax(du.InsuranceID);
                return Json(new { ErrorCode = 0, /*Doc, Taxs*/ });
            }
            catch (Exception EX)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = EX.Message });
            }


        }

        //a00049
        public ActionResult a47005(Guid InsID, Guid UserID, Guid AgEncyID)
        {
            try
            {
                var us = db.AgUsers.Where(p => p.AgUserID == UserID && p.AgentId == AgEncyID
                 && p.Status == 1).ToList();
                if (us.Count() != 1)
                    return Json(new { ErrorCode = 3 });
                var obj = db.Trav_InsuranceDocument.Find(InsID);
                obj.IsInDeleteProgress = true;
                db.Trav_InsuranceDocument.Attach(obj);
                db.Entry(obj).State = EntityState.Modified;
                db.SaveChanges();
                return Json(new { ErrorCode = 0, });

            }
            catch (Exception EX)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = EX.Message });
            }
        }







        //a00048
        public ActionResult a47006(Trav_CalTable CalObj, Guid AgEncyID, List<Trav_CalTable> Dtable)
        {
            try
            {

                Insur_class ic = new Insur_class();
                Trav_insDocClass idc = new Trav_insDocClass();
                Guid uid =CalObj.UsID;
                Guid rid = Guid.Parse("954af0b0-07f7-475d-837e-4dc5a7bd9c1b");
                Guid wid = Guid.Parse("6822405d-3c27-4fd6-b5a3-9fa7ce49d72e");
                var liID  = ic.GetLibyaID();
                var us = db.AgUsers.Where(p => p.AgUserID == uid && p.AgentId == AgEncyID
                && p.Status == 1).ToList();
                if (us.Count() != 1)
                    return Json(new { ErrorCode = 3 });
                var Agen = us[0].Agency;
                bool IsLocalAg = (bool?)Agen.IsLocalCurrnecy ?? false;
                float ProftPer = (float?)us[0].Agency.ProftMargin ?? 0;
                var vir = db.Virebles.Where(c => c.ConntryID == liID && c.Status == 1).ToList();
                int Age = DateTime.Now.Year - CalObj.BDay;
                int dur = (int)CalObj.Months;
                var Ag = ic.GetAgeRow(Age);
                var Dur = ic.GetDurtionRow(dur);
                var AgPer = Ag[0];
                var DurPer = Dur[0];
                float InsValue = (float?)db.Trav_Zoon.Find(CalObj.Zid).ZoonPrice ?? 0;
                float AgeValue = ((float)AgPer.AgePercentage * InsValue);
                float DurValue = (((float)DurPer.DurationPercentage) * (AgeValue));
                float Minp = Agen.IsAllowedDibt == false ? 0 : ((float)Agen.AllowedDibtValue * -1);
                float DocValue = DurValue;
                float ProfitVal = DocValue * (ProftPer / 100);
                List<DocTax> dt = new List<DocTax>();
                foreach (var it in vir)
                {
                    DocTax obj = new DocTax();
                    obj.VirID = it.VirD;
                    obj.Value = it.IsPer == false ? it.Value : DocValue * (it.Value);
                    obj.Value = it.IsrounsdedTo == 0.5 ? ic.RoudToPoint5((float)obj.Value) : obj.Value;
                    obj.Value = it.IsWithProfitMargin == true ? obj.Value + ProfitVal : obj.Value;
                    obj.IsPer = it.IsPer ?? false;
                    obj.Status = 1;

                    dt.Add(obj);
                }
                float TaxValue = (float)dt.Sum(c => c.Value);
                float DiffToround = ic.RondedValueTo5(DocValue + TaxValue);
                TaxValue = TaxValue + DiffToround;
                dt.SingleOrDefault(c => c.VirID == Guid.Parse("0C4E4EB4-ED07-4192-8041-E3954E9792D2")).Value = dt.SingleOrDefault(c => c.VirID == Guid.Parse("0C4E4EB4-ED07-4192-8041-E3954E9792D2")).Value
                    + DiffToround;
                var DocC = new
                {
                    DocVal = DocValue,
                    Totoal = (DocValue + TaxValue) * CalObj.Count,
                    AgeDesc = AgPer.AgeDesc,
                    Count = CalObj.Count,
                    YearOfBirth = CalObj.BDay,
                    Duration = dur,
                };
                var TaxCal = vir.Select(c => new
                {
                    c.VirDesc,
                    Value = dt.SingleOrDefault(p => p.VirID == c.VirD).Value
                }).ToList();
                Trav_CalTable rt = new Trav_CalTable();
                rt.DocVal = DocC.DocVal;
                rt.Count = DocC.Count;
                rt.YearOfBirth = DocC.YearOfBirth;
                rt.AgeDesc = DocC.AgeDesc;
                rt.Duration = (int)DocC.Duration;
                rt.Totoal = DocC.Totoal;
                rt.Stamp = (float)TaxCal[0].Value;
                rt.Eshraf = (float)TaxCal[1].Value;
                rt.Tax = (float)TaxCal[2].Value;
                rt.Esdar = (float)TaxCal[3].Value;

                if (Dtable == null)
                {
                    Dtable = new List<Trav_CalTable>();
                }
                Dtable.Add(rt);
                var DocCla = Dtable.GroupBy(c => new { c.DocVal, c.Esdar, c.YearOfBirth, c.AgeDesc, c.Duration, c.Stamp, c.Eshraf, c.Tax }).Select(c => new
                {
                    c.Key.DocVal,
                    c.Key.AgeDesc,
                    c.Key.YearOfBirth,
                    c.Key.Duration,
                    c.Key.Stamp,
                    c.Key.Eshraf,
                    c.Key.Tax,
                    c.Key.Esdar,
                    Count = c.Sum(a => a.Count),
                    Totoal = c.Sum(a => a.Totoal),
                }).ToList();
                var Summ = new
                {
                    Totoal = DocCla.Sum(c => c.Totoal),
                    Count = DocCla.Sum(c => c.Count),
                };

                return Json(new { ErrorCode = 0, DocCla, Summ });

            }
            catch (Exception EX)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = EX.Message });
            }
        }



        //a00046
        public ActionResult a47007(Guid InsID, Guid UserID, Guid AgEncyID)
        {
            try
            {

                var Us = db.AgUsers.Find(UserID);
                var agen = Us.Agency;
                if (Us.AgentId != AgEncyID || Us == null)
                    return Json(new { ErrorCode = 3 });
                var InsDoc = db.Trav_InsuranceDocument.Find(InsID);
                InsDoc.IsInDeleteProgress = true;
                db.Trav_InsuranceDocument.Attach(InsDoc);
                db.Entry(InsDoc).State = EntityState.Modified;
                db.SaveChanges();
                return Json(new { ErrorCode = 0 });

            }
            catch (Exception EX)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = EX.Message });
            }
        }
        


        //a00045
        public ActionResult a47008(Guid InsID, Guid UserID, Guid AgEncyID)
        {
            try
            {

                var Us = db.AgUsers.Find(UserID);
                if (Us.AgentId != AgEncyID || Us == null)
                    return Json(new { ErrorCode = 3 });
                int ID = 2;
                var AgenPer = db.AgencyPermission.Where(c => c.AgencyID == AgEncyID && c.AgPermissionID == ID).Count() > 1 ? true: true;
                var InsDoc = (AgenPer) ?
                    db.Trav_InsuranceDocument.Where(c => c.InsuranceID == InsID).ToList() :
                    db.Trav_InsuranceDocument.Where(c => c.AgencyID == AgEncyID
                && c.InsuranceID == InsID).ToList();
                if (InsDoc.Count() != 1)
                    return Json(new { ErrorCode = 5, Message = "الوثيقة غير موجودة" });
                var Tax = db.Virebles.ToList();
                Insur_class ic = new Insur_class();
                var Doc = ic.Trav_PrintIns(InsDoc[0]);
                var Taxs = ic.GetTax(InsID).ToList();
                return Json(new { ErrorCode = 0, Doc, Taxs });

            }
            catch (Exception EX)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = EX.Message });
            }
        }


        //a00044
        public ActionResult a47009(Guid UserID, Guid AgEncyID)
        {
            try
            {
                var us = db.AgUsers.Where(p => p.AgUserID == UserID && p.AgentId == AgEncyID
                  && p.Status == 1).ToList();
                if (us.Count() != 1)
                    return Json(new { ErrorCode = 3 });
                var Contries = db.Country.Where(p => p.Status == 1).Select(c => new
                {
                    Name = c.CountryName,
                    ID = c.CountryID
                }).ToList();

                return Json(new { ErrorCode = 0, Contries });

            }
            catch (Exception EX)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = EX.Message });
            }
        }



        //a00043
        public ActionResult a470010(Guid AgEncyID, Guid UserID)
        {
            try
            {
                //chang here
                string dp = DateTime.Now.ToString("yyyyMMdd");
                DateTime dt = DateTime.Now.AddDays(-30);
                var Us = db.AgUsers.Find(UserID);
                if (Us.AgentId != AgEncyID || Us == null)
                    return Json(new { ErrorCode = 3 });
                var Result = db.Trav_InsuranceDocument.Where(c => c.Status == 1 && c.InsertDate > dt
                && c.AgencyID == AgEncyID).Select(c => new
                {
                    InsID = c.InsuranceID,
                    CuName = c.NameCust,
                    c.ZoonID,
                    c.TotWithTax,
                    c.Age,
                    c.Cus_Phone,
                    c.Gender,
                    c.DocNum,
                    c.PassportID,
                    Months = c.DurationMonths,
                    SDate = c.FromDate,
                    EDate = c.ToDate,
                    c.Status,
                    c.Total,
                    c.Trav_Zoon.ZoonName,
                    c.NameCustEN,
                    c.InsuNumber,
                    c.InsertedBy,
                    c.InsertDate,
                    c.StDucNum,
                    c.ClintDate,
                    c.IsInDeleteProgress,
                    c.BitthDate,

                }).ToList().OrderByDescending(c => c.InsertDate);
                var InsDoc = Result.Select(c => new
                {
                    c.InsID,
                    c.CuName,
                    c.InsuNumber,
                    zid = c.ZoonID,
                    c.Age,
                    c.Gender,
                    c.ZoonName,
                    c.Months,
                    c.PassportID,
                    c.IsInDeleteProgress,
                    c.Cus_Phone,
                    c.NameCustEN,
                    c.Status,
                    SFullDate = c.SDate.ToString("yyyy-MM-ddTHH:mm:ss"),
                    EFullDate = c.EDate.ToLongDateString(),
                    DocNum = c.DocNum.ToString("D" + 5),
                    SDate = c.SDate.ToString("yyyy-MM-dd"),
                    bDate = c.BitthDate.Year,
                    Total = c.TotWithTax ?? 0,
                    db.AgUsers.Find(c.InsertedBy).AgUserName,
                    InsertDate = c.ClintDate.Value.ToString("yy-MM-dd  hh:mm tt"),
                    DucNum = c.StDucNum,

                }).ToList();
                var Users = InsDoc.Select(c => new { ID = c.AgUserName, Name = c.AgUserName }).Distinct();

                return Json(new { ErrorCode = 0, InsDoc, Users });

            }
            catch (Exception EX)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = EX.Message });
            }
        }


        //a00042
        public ActionResult a470011(Trav_insDocClass MainObj, Guid AgEncyID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                var LyId = ic.GetLibyaID();
                Guid uid = MainObj.UsID;
                Guid wid = Guid.Parse("6822405d-3c27-4fd6-b5a3-9fa7ce49d72e");
                var sn = db.Trav_SerialNums.Where(p => p.SerialNumID == MainObj.InsuNumber && p.Trav_SearStatus.ID == 1 && p.AgentID == AgEncyID).ToList();
                if (!sn.Any())
                    return Json(new { ErrorCode = 4 });
                DateTime Bydate = new DateTime(MainObj.BDay, 01, 01);
                var us = db.AgUsers.Where(p => p.AgUserID == uid && p.AgentId == AgEncyID
                && p.Status == 1).ToList();
                if (us.Count() != 1)
                    return Json(new { ErrorCode = 3 });
                var Agen = us[0].Agency;
                float ProftPer = (float?)us[0].Agency.ProftMargin ?? 0;
                var vir = db.Virebles.Where(c => c.ConntryID == LyId && c.Status == 1).ToList();
                int Age = DateTime.Now.Year - Bydate.Year;
                int dur = MainObj.Months;
                var Ag = ic.GetAgeRow(Age);
                var Dur = ic.GetDurtionRow(dur);
                var AgPer = Ag[0];
                var DurPer = Dur[0];
                float InsValue = (float?)db.Trav_Zoon.Find(MainObj.Zid).ZoonPrice ?? 0;
                float AgeValue = ((float)AgPer.AgePercentage * InsValue);
                float DurValue = (((float)DurPer.DurationPercentage) * (AgeValue));
                float Minp = Agen.IsAllowedDibt == false ? 0 : ((float)Agen.AllowedDibtValue * -1);
                float DocValue = DurValue;
                float ProfitVal = DocValue * (ProftPer / 100);
                Trav_InsuranceDocument du = new Trav_InsuranceDocument();
                du.InsuranceID = Guid.NewGuid();
                List<Trav_DocTax> dt = new List<Trav_DocTax>();
                foreach (var it in vir)
                {
                    Trav_DocTax obj = new Trav_DocTax();
                    obj.DocTaxID = Guid.NewGuid();
                    obj.DocID = du.InsuranceID;
                    obj.VirID = it.VirD;
                    obj.Value = it.IsPer == false ? it.Value : DocValue * ((float)it.Value);
                    obj.Value = it.IsrounsdedTo == 0.5 ? ic.RoudToPoint5((float)obj.Value) : obj.Value;
                    obj.Value = it.IsWithProfitMargin == true ? obj.Value + ProfitVal : obj.Value;
                    obj.IsPer = it.IsPer ?? false;
                    obj.Status = 1;
                    dt.Add(obj);
                }
                float TaxValue = (float)dt.Sum(c => c.Value);
                float DiffToround = ic.RondedValueTo5(DocValue + TaxValue);
                TaxValue = TaxValue + DiffToround;
                dt.SingleOrDefault(c => c.VirID == Guid.Parse("0C4E4EB4-ED07-4192-8041-E3954E9792D2")).Value = dt.SingleOrDefault(c => c.VirID == Guid.Parse("0C4E4EB4-ED07-4192-8041-E3954E9792D2")).Value
                    + DiffToround;
                float pal = ((float?)Agen.Palance ?? 0) - (Agen.IsLocalCurrnecy == false ? DurValue + TaxValue : DocValue);

                //if (pal <= Minp)
                //{
                //    return Json(new { ErrorCode = 5, ErorrMessage = "رصيدك غير كافي لإجراء هذه المعاملة ،قم بمخاطبة مرؤسيك بالخصوص" });
                //}
                du.AgencyID = (Guid)us[0].AgentId;
                du.ZoonID = MainObj.Zid;
                du.AgeValue = AgeValue;
                du.DurValue = DurValue;
                du.TaxTotal = TaxValue;
                du.PfroftMargin = ProfitVal;
                du.DurationMonths = dur;
                du.Total = DocValue;
                du.TotWithTax = DocValue + TaxValue;
                du.NameCust = MainObj.CuName;
                du.PassportID = MainObj.PassportID;
                du.BitthDate = Bydate;
                du.IsInDeleteProgress = false;
                du.FromDate = MainObj.SDate;
                du.ToDate = MainObj.SDate.AddMonths(((int)MainObj.Months));
                du.DurationTypeID = DurPer.DurationID;
                du.AgeTypeID = AgPer.AgeID;
                du.Age = Age;
                du.NameCustEN = MainObj.NameCustEN;
                du.ClintDate = MainObj.clintDate;
                du.Status = 1;
                du.InsertedBy =  MainObj.UsID;
                du.InsertDate = DateTime.Now;
                du.Cus_Phone = MainObj.Cus_Phone;
                du.InsuNumber = MainObj.InsuNumber;
                du.Gender = MainObj.Gender;
                db.Trav_InsuranceDocument.Add(du);
                db.SaveChanges();
                du.StDucNum = du.InsertDate.Value.ToString("yyyy") + " " + du.Agency.AgNum + " " +
                   du.InsuNumber.ToString() + " " + du.DocNum.ToString("D" + 5);
                db.Entry(du).State = EntityState.Modified;
                db.SaveChanges();
                foreach (var it in dt)
                    db.Trav_DocTax.Add(it);
                db.SaveChanges();
                Agen.SoldValue = (Agen.SoldValue ?? 0) + (Agen.AgenCatID.ToString() == "DAC8AE95-9111-495E-911E-1DADB1AA3DE2" ? (du.TotWithTax ?? 0)
                : Agen.AgenCatID.ToString() == "F74FCA70-2881-4385-864B-063D451708B9" ? (du.TotWithTax ?? 0) - (du.PfroftMargin ?? 0) : du.Total);
                Agen.Palance = ic.RondedValue((float)((Agen.AddPalance ?? 0) - (Agen.SoldValue ?? 0) + (Agen.ReplaValue ?? 0)));
                db.Agency.Attach(Agen);
                db.Entry(Agen).State = EntityState.Modified;
                db.SaveChanges();
                Trav_SerialNums snobj = sn[0];
                snobj.Status = 8;
                db.Trav_SerialNums.Attach(snobj);
                db.Entry(snobj).State = EntityState.Modified;
                db.SaveChanges();
                var Doc = ic.Trav_PrintIns(du);
                var Taxs = ic.GetTax(du.InsuranceID);
                return Json(new { ErrorCode = 0, Doc, Taxs });

            }
            catch (Exception EX)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = EX.Message });
            }
        }




        //a00051
        public ActionResult a470012(Guid UserID)
        {
            try
            {
                var att = db.AgUsers.Where(p => p.AgUserID == UserID && p.Status == 1).ToList();
                if (att.Count() != 1)
                    return Json(new { ErrorCode = 3 });
                var AgeTypesList = db.Trav_AgesTypes.Select(c => new { Name = c.AgeDesc, Val = c.AgEnd }).ToList().OrderBy(c => c.Val);

                return Json(new { ErrorCode = 0, AgeTypesList });

            }
            catch (Exception EX)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = EX.Message });
            }
        }

        //a41
        public ActionResult a470014(Guid UserID, Guid AgEncyID)
        {
            try
            {
                var att = db.AgUsers.Where(p => p.AgUserID == UserID && p.AgentId == AgEncyID).ToList();
                if (att.Count() != 1)
                    return Json(new { ErrorCode = 3 });
                string MinDate = DateTime.Now.Date.ToString();
                int RptTypes = 1;
                List<PrintRptType> pt = new List<PrintRptType>();

                if (RptTypes == 1 || RptTypes == 3)
                {
                    PrintRptType dd = new PrintRptType();
                    dd.ID = 1;
                    dd.Name = "طباعة ورقة واحدة";
                    pt.Add(dd);
                }
                if (RptTypes == 2 || RptTypes == 3)
                {
                    PrintRptType dd = new PrintRptType();
                    dd.ID = 2;
                    dd.Name = "طباعة ورقتين";
                    pt.Add(dd);
                }

                //var Dtypes = db.DocumentType.Where(p => p.Status == 1).Select(p => new
                //{
                //    ID = p.DocumentTypeID,
                //    desc = p.DocumentTypeDecs,
                //}).ToList();



                return Json(new { ErrorCode = 0, MinDate, RptTypes = pt });

            }
            catch (Exception EX)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = EX.Message });
            }
        }

    }
}