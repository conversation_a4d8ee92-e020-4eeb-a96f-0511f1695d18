﻿(function () {
    'use strict';
    angular.module("App").factory("DataService", ["$http", DataService]);
    function DataService($http) {


        return {

            a490010: a490010,
            a490009: a490009,
            a490008: a490008,
            a490007: a490007,
            a490006: a490006,
            a490005: a490005,
            a490004: a490004,
            a490003: a490003,
            a490002: a490002,

            a48007: a48007,
            a48006: a48006,
            a48005: a48005,
            a48004: a48004,
            a48003: a48003,
            a48002: a48002,
            a470014: a470014,
            a470013: a470013,
            a470012: a470012,
            a470011: a470011,
            a470010: a470010,
            a47009: a47009,
            a47008: a47008,
            a47007: a47007,
            a47006: a47006,
            a47005: a47005,
            a47004: a47004,
            a47003: a47003,
            a47002: a47002,

            a27015: a27015,
            a26011: a26011,
            a27011: a27011,
            a26010: a26010,
            a25006: a25006,
            IsExisit: IsExisit,
            a25005: a25005,
            a25003: a25003,
            a25004: a25004,
            a29001: a29001,
            a40004: a40004,
            a40005: a40005,
            a45006: a45006,
            a40007: a40007,
            a400015: a400015,
            a44005: a44005,
            a44008: a44008,
            a44007: a44007,
            a44006: a44006,
            a44004: a44004,
            a41004: a41004,
            a400017: a400017,
            a400031: a400031,
            a400033: a400033,
            a400050: a400050,
            a400051: a400051,
            a400052: a400052,
            a400053: a400053,
            a50004: a50004,
            a50007: a50007,
            a50008: a50008,
            a500010: a500010,
            a270072: a270072,
            a44009: a44009,
            a44002: a44002,
            a60001: a60001,
            a50006: a50006,
            a60002: a60002,
            a60005: a60005,
            a60006: a60006,
            a60007: a60007,
            a60008: a60008,
            a60009: a60009,
            a400061: a400061,
            a500060: a500060,
            a600015: a600015,
            a60011: a60011,
            a61005: a61005,
            a61006: a61006,
            a61007: a61007,
            a61010: a61010,
            a61011: a61011,
            a61012: a61012,
            a61013: a61013,
            a61014: a61014,
         
            a600050: a600050,

        }
       
        function a600050(ID, UserID) {
            return $http.post("/a60/a600050 ", { ID, UserID })
        }
        function a600015(MainObj) {
            return $http.post("/a60/a600015", { MainObj})
        }
        function a60011(UserID, AgencyID) {
            return $http.post("/a60/a60011", { AgencyID, UserID })
        }
        function a60001(UserID, AgencyID) {
            return $http.post("/a60/a60001", { AgencyID, UserID })
        }
        function a60002(MainObj) {
            return $http.post("/a60/a60002", { MainObj })
        }
        function a60005(UserID ,AgencyID) {
            return $http.post("/a60/a60005", { AgencyID, UserID })
        }
        function a60006(UserID, AgencyID) {
            return $http.post("/a60/a60006", { AgencyID, UserID })
        }
        function a60007(UserID, AgencyID) {
            return $http.post("/a60/a60007", { AgencyID, UserID })
        }
        function a60008(UserID, AgencyID) {
            return $http.post("/a60/a60008", { AgencyID, UserID })
        }
        function a60009(UserID, AgencyID) {
            return $http.post("/a60/a60009", { AgencyID, UserID })
        }
        function a44002(UserID, AgencyID, MainObj) {
            return $http.post("/a44/a44002 ", { UserID, AgencyID, MainObj })
        }
        function a44009(UserID) {
            return $http.post("/a44/a44009 ", { UserID})
        }
        function a44009(UserID) {
            return $http.post("/a44/a44009 ", { UserID})
        }

        function a500010(CardNo, UserID) {
            return $http.post('/a50/a500010', { CardNo, UserID }, {
                responseType: 'blob' 
            });
        }
        function a500060(UserID, AgencyID) {
            return $http.post("/a50/a500060", { AgencyID, UserID })
        }
    
        function a270072(AgencyID, UserID) {
            return $http.post("/a27/a270072  ", { AgencyID, UserID })
        }
        function a50008(ID, UserID, Note) {
            return $http.post("/a50/a50008 ", { ID, Note, UserID })
        }
        function a50006(ID, Note, UserID) {
            return $http.post("/a50/a50006 ", { ID, Note, UserID })
        }
        function a50007(MainObj) {
            return $http.post("/a50/a50007 ", { MainObj })
        }
        function a50004(UserID ,AgEncyID) {
            return $http.post("/a50/a50004 ", { AgEncyID, UserID })
        }
        function a490010(AgEncyID, UserID, Search) {
            return $http.post("/a49/a490010 ", { AgEncyID, UserID, Search })
        }
        function a490009(AgEncyID, UserID, DocID) {
            return $http.post("/a49/a490009 ", { AgEncyID, UserID, DocID })
        }
        function a490008(AgEncyID, UserID, DocID, DocSnNum) {
            return $http.post("/a49/a490008 ", { AgEncyID, UserID, DocID, DocSnNum })
        }
        function a490007(ID, AgEncyID, UserID) {
            return $http.post("/a49/a490007 ", { ID, AgEncyID, UserID })
        }
        function a490006(ID, UserID, AgEncyID) {
            return $http.post("/a49/a490006 ", { ID, UserID, AgEncyID })
        }
        function a490005(ID, AgEncyID, UserID) {
            return $http.post("/a49/a490005 ", { ID, AgEncyID, UserID })
        }
        function a490004(ID, AgEncyID, UserID) {
            return $http.post("/a49/a490004 ", { ID, AgEncyID, UserID })
        }
        function a490003(ID, AgEncyID, UserID) {
            return $http.post("/a49/a490003 ", { ID, AgEncyID, UserID })
        }
        function a490002(ID, AgEncyID, UserID) {
            return $http.post("/a49/a490002 ", { ID, AgEncyID, UserID })
         }
        //----------a48-----------
        function a48007(UserID, AgEncyID, MainObj) {
            return $http.post("/a48/a48007 ", { UserID, AgEncyID, MainObj })
        }
        function a48006(UserID, AgEncyID, MainObj) {
            return $http.post("/a48/a48006 ", { UserID, AgEncyID, MainObj })
        }
        function a48005(UserID, AgEncyID, MainObj) {
            return $http.post("/a48/a48005 ", { UserID, AgEncyID, MainObj })
        }
        function a48004(UserID, AgEncyID, MainObj) {
            return $http.post("/a48/a48004 ", { UserID, AgEncyID, MainObj })
        }
        function a48003(UserID) {
            return $http.post("/a48/a48003 ", { UserID })
        }
        function a48002(UserID, Year) {
            return $http.post("/a48/a48002 ", { UserID, Year })
        }
        //----------a47-----------
        function a470014(UserID, AgEncyID) {
            return $http.post("/a47/a470014 ", { UserID, AgEncyID })
        }
        function a470013(UserID, AgEncyID) {
            return $http.post("/a47/a470013 ", { UserID, AgEncyID })
        }
        function a470012(UserID) {
            return $http.post("/a47/a470012 ", { UserID })
        }
        function a470011(MainObj, AgEncyID) {
            return $http.post("/a47/a470011 ", { MainObj, AgEncyID })
        }
        function a470010(AgEncyID, UserID) {
            return $http.post("/a47/a470010 ", { AgEncyID, UserID })
        }
        function a47009(AgEncyID,UserID) {
            return $http.post("/a47/a47009", { UserID, AgEncyID })
        }
        function a47008(AgEncyID, UserID, InsID) {
            return $http.post("/a47/a47008 ", { AgEncyID, UserID, InsID })
        }
        function a47007(AgEncyID, UserID, InsID) {
            return $http.post("/a47/a47007 ", { AgEncyID, UserID, InsID })
        }
        function a47006(CalObj, AgEncyID, Dtable) {
            return $http.post("/a47/a47006 ", { CalObj, AgEncyID, Dtable })
        }
        function a47005(InsID, UserID, AgEncyID) {
            return $http.post("/a47/a47005 ", { InsID, UserID, AgEncyID })
        }
  
        function a47004(MainObj, AgEncyID) {
            return $http.post("/a47/a47004 ", { MainObj, AgEncyID })
        }
        function a47003(ByearDay) {
            return $http.post("/a47/a47003 ", { ByearDay })
        }
        function a47002(UserID, AgEncyID) {
            return $http.post("/a47/a47002 ", { UserID, AgEncyID })
        }
        //---------------------
        function a400053(AgEncyID, UserID) {
            return $http.post("/a40/a400053 ", { AgEncyID,UserID })
        }
        function a400061(UserID, AgencyID) {
            return $http.post("/a40/a400061", { AgencyID, UserID })
        }
        function a400052(MainObj) {
            return $http.post("/a40/a400052 ", { MainObj })
        }
        function a400051(RePlaceObj) {
            return $http.post("/a40/a400051 ", { RePlaceObj })
        }
        function a400050(ID, UserID) {
            return $http.post("/a40/a400050 ", { ID, UserID })
        }
        function a400033(RenewObj) {
            return $http.post("/a40/a400033 ", { RenewObj })
        }
        function a400031(SearchTxt, UserID) {
            return $http.post("/a40/a400031 ", { SearchTxt,UserID })
        }
        function a400017(UserID) {
            return $http.post("/a40/a400017 ", { UserID })
        }
        function a44008(UserID) {
            return $http.post("/a44/a44008 ", { UserID })
        }
        function a41004(ID, UserID) {
            return $http.post("/a41/a41004 ", { ID, UserID })
        }
        function a44007(UserID, Year) {
            return $http.post("/a44/a44007 ", { UserID, Year })
        }
        function a44006(UserID) {
            return $http.post("/a44/a44006 ", { UserID })
        }
        function a44005(UserID, AgEncyID) {
            return $http.post("/a44/a44005 ", { UserID, AgEncyID })
        }
        function a400015(Ins_Type, UserID) {
            return $http.post("/a40/a400015 ", { Ins_Type, UserID })
        }
        function a44004(UserID, AgEncyID, MainObj) {
            return $http.post("/a44/a44004", { UserID, AgEncyID, MainObj })
        }
        function a45006(ID, UserID, DelNote) {
            return $http.post("/a40/a45006 ", { ID, UserID, DelNote })
        }
        function a40007(MainObj) {
            return $http.post("/a40/a40007 ", { MainObj })
        }
        function a40005(c_masterID, UserID) {
            return $http.post("/a40/a40005 ", { UserID, c_masterID })
        }
        function a40004(UserID, AgencyID) {
            return $http.post("/a40/a40004 ", { UserID, AgencyID })
        }
        function a27015(NewPassword, OldPassword, UserID) {
            return $http.post("/a27/a27015 ", { NewPassword, OldPassword, UserID })
        }
        function a26011(UserID) {
            return $http.post("/a27/a26011 ", { UserID })
        }
        function a27011(ProfitMargin, Exchange, UserID) {
            return $http.post("/a27/a27011 ", { ProfitMargin, Exchange, UserID })
        }
        function a26010(UserID) {
            return $http.post("/a27/a26010 ", { UserID })
        }

        function a25006(SelectedUserID, Status, UserID) {
            return $http.post("/a26/a25006 ", { SelectedUserID, Status, UserID })
        }
        function IsExisit(Parm1, Param2) {
            return $http.post("/a26/IsExisit ", { Parm1, Param2 })
        }
        function a25005(MainObj) {
            return $http.post("/a26/a25005 ", { MainObj })
        }

        function a25003(mainObj) {
            return $http.post("/a26/a25003 ", { mainObj })
        }
        function a25004(IsAll, UserID) {
            return $http.post("/a26/a25004", { IsAll, UserID })
        }

        function a29001(Parm1, Parm2) {
            return $http.post("/a27/a29001", { Parm1, Parm2 })
        }

        // a61 - Stock Requests Functions
        function a61005(AgencyID, UserID) {
            return $http.post("/a61/a61005", { AgencyID, UserID })
        }
        function a61006(AgencyID, UserID) {
            return $http.post("/a61/a61006", { AgencyID, UserID })
        }
        function a61007(AgencyID, UserID) {
            return $http.post("/a61/a61007", { AgencyID, UserID })
        }
        function a61010(AgencyID, UserID, RequestTypeID, SysTypeID, RequestedQuantity, SerialNumber, CurrentStatusID, RequestedStatusID, ChangeReason, Notes) {
            return $http.post("/a61/a61010", { AgencyID, UserID, RequestTypeID, SysTypeID, RequestedQuantity, SerialNumber, CurrentStatusID, RequestedStatusID, ChangeReason, Notes })
        }
        function a61011(AgencyID, UserID) {
            return $http.post("/a61/a61011", { AgencyID, UserID })
        }
        function a61012(RequestID, UserID, ReceiptNotes) {
            return $http.post("/a61/a61012", { RequestID, UserID, ReceiptNotes })
        }
        function a61013(RequestID, UserID) {
            return $http.post("/a61/a61013", { RequestID, UserID })
        }
        function a61014(RequestID, UserID) {
            return $http.post("/a61/a61014", { RequestID, UserID })
        }
    }

})();