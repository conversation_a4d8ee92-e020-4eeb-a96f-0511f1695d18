﻿(function () {
    'use strict';

    var controllerId = 'a60';
    angular.module('App').controller(controllerId, ["DataService", '$filter', "Notification", "blockUI", "$stateParams", "$state", "$http", Func]);
    function Func(DataService, $filter, Notification, blockUI, $stateParams, $state, $http) {
        var vm = this;
        vm.ShowFlag = 0;


        if ($stateParams.UserID == undefined || $stateParams.UserID == null || $stateParams.UserID == '')
            $state.go('LoginPage');
        else {
            vm.UserID = $stateParams.UserID;
            vm.UserName = $stateParams.UserName;
            vm.AgEncyID = $stateParams.AgEncyID;
            vm.AgenName = $stateParams.AgenName;
            vm.AgNum = $stateParams.AgNum;
            a60011();
            a60005();
            a60006();
            a60007();
            a60008();
            a60009();
        }


        vm.MainObj = {
            Insu_ID: '',
            AgencyID: '',
            Date_From: '',
            Date_To: '',
            Duration: '',
            DealerName: '',
            CustomerName: '',
            DealerCustomerReleation: '',
            NationalityID: '',
            BirthDate: '',
            BirthPlace: '',
            AddressID: '',
            MaritalStatusID: '',
            ProfessionID: '',
            AcademicQualificationID: '',
            Percentage: '',
            installment: '',
            Daily_installment: '',
            Stamp: '',
            Tax: '',
            Supervision: '',
            Version: '',
            Total: '',
            InsertedBy: vm.UserID,
        };


        vm.prview = function (x) {
            blockUI.start();
            DataService.a600050(x.ID, vm.UserID).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
                    vm.printObj = Response.data.doc;
                    vm.ShowFlag = 1;
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 1) {
                    Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 4) {
                    vm.IsExistPayperNum = 1;
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 5) {
                    Notification.error({ message: Response.data.ErorrMessage, title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 3) {
                    $state.go('LoginPage');
                    blockUI.stop();
                }
            }, function (error) {
                Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
                blockUI.stop();
            });
             
        };
    

        vm.BtnHomePage = function () {
            $state.go('HomePage', { 'UserID': vm.UserID, 'UserName': vm.UserName, 'AgEncyID': vm.AgEncyID, 'AgenName': vm.AgenName, "AgNum": vm.AgNum })
        }

        // معاينة الوثيقة
        vm.previewDoc = function (doc) {
            vm.Doc = doc;
            vm.ShowFlag = 1;
            $('#previewModal').modal('show');
        };

        // طباعة الوثيقة
        vm.printDoc = function (doc) {
            vm.Doc = doc;
            setTimeout(function () {
                window.print();
            }, 100);
        };

        // طباعة الوثيقة الحالية من نافذة المعاينة
        vm.printCurrentDoc = function () {
            window.print();
        };

        vm.SelectedIsSalaryChanged = function () {
            vm.MainObj.IsSalary = vm.IsSalary == 1 ? true : false;
            CalIns();
        }
        vm.St_CalIns = function () {
            CalIns();
        }
        //vm.SelectedcQualificationChange = function () {
        //    if (vm.AcademicQualificationID == undefined || vm.AcademicQualificationID == null) {
        //        return;
        //    }
        //    vm.MainObj.AcademicQualificationID = vm.AcademicQualificationID.ID.ID;
         
        //}
        vm.CalEndDate = function () {
            vm.MainObj.Date_To =new Date( new Date(vm.MainObj.Date_From).setMonth(vm.MainObj.Date_From.getMonth() + vm.MainObj.Duration));
            CalIns();
        }
        function CalIns() {
            if (!vm.MainObj.IsSalary)
                vm.MainObj.installment = (vm.MainObj.Daily_installment ?? 0) * (vm.MainObj.Duration ?? 0);
            else
                vm.MainObj.installment = ((vm.MainObj.Salary ?? 0) * ((vm.MainObj.Percentage ?? 0)/100) )* (vm.MainObj.Duration ?? 0);
            if ((vm.MainObj.installment ?? 0) > 0) {
                vm.MainObj.Stamp = vm.StampVal ?? 0;
                vm.MainObj.Tax = roundToPont5((vm.MainObj.installment ?? 0) * 0.01)
                vm.MainObj.Version = vm.InsValue;
                vm.MainObj.Supervision = ((vm.MainObj.installment ?? 0) * 0.005)
                var tot = vm.MainObj.installment + vm.MainObj.Version + vm.MainObj.Stamp + vm.MainObj.Tax + vm.MainObj.Supervision;
                var diff = 0;
                vm.MainObj.Total = roundTo1(tot)
                diff = vm.MainObj.Total - tot;
                vm.MainObj.Version = vm.MainObj.Version + diff;
            } else {
                vm.MainObj.Stamp = 0;
                vm.MainObj.Tax = 0
                vm.MainObj.Version = 0;
                vm.MainObj.Supervision = 0;
                vm.MainObj.Total = 0;
            }

        }
        function roundToPont5(t_Tax) {
            var t = t_Tax % 1;
            if (t >= 0 && t <= 0.5)

                t = 0.5;
            else if (t > 0.5 && t < 1)
                t = Math.floor(t_Tax) + 1;
            return t;
        }
        function roundTo1(val) {
            var t = val % 1;
            if (t > 0 && t < 1)
                t = Math.floor(val) + 1;
            else
                t = val;
            return t;
        }

        // Search functions for md-autocomplete
        vm.searchNationalities = function(query) {
            if (!vm.NationalityList) return [];
            return vm.NationalityList.filter(function(item) {
                return item.Name.toLowerCase().includes(query.toLowerCase());
            });
        };

        vm.searchAddresses = function(query) {
            if (!vm.AddressList) return [];
            return vm.AddressList.filter(function(item) {
                return item.Name.toLowerCase().includes(query.toLowerCase());
            });
        };

        vm.MaritalStatuseslist = function(query) {
            if (!vm.MaritalStatusList) return [];
            return vm.MaritalStatusList.filter(function(item) {
                return item.Name.toLowerCase().includes(query.toLowerCase());
            });
        };

        vm.searchAcademicQualifications = function(query) {
            if (!vm.AcademicQualificationList) return [];
            return vm.AcademicQualificationList.filter(function(item) {
                return item.Name.toLowerCase().includes(query.toLowerCase());
            });
        };

        vm.searchProfessions = function(query) {
            if (!vm.ProfessionList) return [];
            return vm.ProfessionList.filter(function(item) {
                return item.Name.toLowerCase().includes(query.toLowerCase());
            });
        };

        vm.searchPercentages = function(query) {
            if (!vm.PercentageList) return [];
            return vm.PercentageList.filter(function(item) {
                return item.Name.toLowerCase().includes(query.toLowerCase());
            });
        };
        vm.SelectedMaritalStatusesChange = function () {
            vm.MainObj.MaritalStatusID = vm.MaritalStatusID.ID;
        }
        vm.SelectedNationalityChange = function () {
            vm.MainObj.NationalityID = vm.NationalityID.ID;
        }
        vm.SelectedNationalityChange = function () {
            vm.MainObj.NationalityID = vm.NationalityID.ID;
        }
        vm.SelectedAddressesChange = function () {
            vm.MainObj.AddressID = vm.AddressID.ID;
        }
        vm.SelectedAcademicQualificationChange = function () {
            vm.MainObj.AcademicQualificationID = vm.AcademicQualificationID.ID;
        }
        vm.SelectedProffistionChange = function () {
            vm.MainObj.ProfessionID = vm.ProfessionID.ID;
            vm.MainObj.Daily_installment = vm.ProfessionID.Value;
            CalIns();
        }
        vm.clearMainObj = function() {
            vm.MainObj.Insu_ID = '';
            vm.MainObj.AgencyID =  vm.AgEncyID;
            vm.MainObj.Date_From = new Date();
            vm.MainObj.Date_To = '';
            vm.MainObj.Duration = '';
            vm.MainObj.DealerName = '';
            vm.MainObj.CustomerName = '';
            vm.MainObj.DealerCustomerReleation = '';
            vm.MainObj.NationalityID = '';
            vm.NationalityID = '';
            vm.MainObj.BirthDate = '';
            vm.MainObj.BirthPlace = '';
            vm.MainObj.AddressID = '';
            vm.AddressID = '';
            vm.MainObj.MaritalStatusID = '';
            vm.MaritalStatusID = '';
            vm.MainObj.AcademicQualificationID = '';
            vm.AcademicQualificationID = '';
            vm.MainObj.Percentage = '';
            vm.MainObj.Daily_installment = '';
            vm.MainObj.installment = '';
            vm.MainObj.Supervision = '';
            vm.MainObj.ProfessionID = '';
            vm.ProfessionID = '';
            vm.MainObj.Version = '';
            vm.MainObj.Total = '';
            vm.MainObj.Tax = '';
            vm.MainObj.Stamp = '';
            vm.MainObj.InsertedBy = vm.UserID
            vm.IsExistPayperNum = 0;
            vm.IsSalary = 0;
        };
        vm.SelectedPerValueChanged = function () {
            vm.MainObj.Percentage = vm.PerValue ?? 0;
            CalIns();
        }
        vm.a600012 = function () {
            blockUI.start();
            DataService.a600012(vm.UserID , vm.AgEncyID).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
                    vm.printObj = Response.data.doc;
                    vm.ss = 1;
                    blockUI.stop();
                    vm.print2();
                    a60011();

                }
                if (Response.data.ErrorCode == 1) {
                    Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 1) {
                    Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 4) {
                    vm.IsExistPayperNum = 1;
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 5) {
                    Notification.error({ message: Response.data.ErorrMessage, title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 3) {
                    blockUI.stop();
                    $state.go('LoginPage');

                }
            }, function (error) {
                Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
                blockUI.stop();
            });
        }
        vm.a600015 = function () {
            blockUI.start();
            DataService.a600015(vm.MainObj).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
                    Notification.success({ message: "تمت عملية الاضافة بنجاح", title: 'نجاح العملية' });
                    $('#NewItem').modal('hide');
                    vm.printObj = Response.data.doc;
                    vm.ss = 1;
                    blockUI.stop();
                    vm.print2();
                    a60011();

                }
                if (Response.data.ErrorCode == 1) {
                    Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 1) {
                    Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 4) {
                    vm.IsExistPayperNum = 1;
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 5) {
                    Notification.error({ message: Response.data.ErorrMessage, title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 3) {
                    blockUI.stop();
                    $state.go('LoginPage');

                }
            }, function (error) {
                Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
                blockUI.stop();
            });
        }
    
       

        function a60011() {
            blockUI.start();
            DataService.a60011(vm.UserID, vm.AgEncyID).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
                    vm.InsObj = Response.data.Obj;
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 3) {
                    $state.go('LoginPage');
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 1) {
                    Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
                    blockUI.stop();
                }
            }, function (error) {
                Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
                blockUI.stop();
            });
        }
        function a60005() {
            blockUI.start();
            DataService.a60005(vm.UserID, vm.AgEncyID).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
                    vm.AcademicQualificationList = Response.data.Data;
                    vm.MaritalStatusList = Response.data.MaritalStatusList;
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 3) {
                    $state.go('LoginPage');
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 1) {
                    Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
                    blockUI.stop();
                }
            }, function (error) {
                Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
                blockUI.stop();
            });
        }

        function a60006() {
            blockUI.start();
            DataService.a60006(vm.UserID, vm.AgEncyID).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
                    vm.AddressList = Response.data.Data;
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 3) {
                    $state.go('LoginPage');
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 1) {
                    Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
                    blockUI.stop();
                }
            }, function (error) {
                Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
                blockUI.stop();
            });
        }

        function a60007() {
            blockUI.start();
            DataService.a60007(vm.UserID, vm.AgEncyID).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
                    vm.NationalityList = Response.data.Data;
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 3) {
                    $state.go('LoginPage');
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 1) {
                    Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
                    blockUI.stop();
                }
            }, function (error) {
                Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
                blockUI.stop();
            });
        }

        function a60008() {
            blockUI.start();
            DataService.a60008(vm.UserID, vm.AgEncyID).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
                    vm.ProfessionList = Response.data.Data;
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 3) {
                    $state.go('LoginPage');
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 1) {
                    Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
                    blockUI.stop();
                }
            }, function (error) {
                Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
                blockUI.stop();
            });
        }

        function a60009() {
            blockUI.start();
            DataService.a60009(vm.UserID, vm.AgEncyID).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
                    vm.PercentageList = Response.data.Data;
                    vm.InsValue = Response.data.MangSettings.InsValue;
                    vm.StampVal = Response.data.MangSettings.StampVal;
                    vm.IsFreePer = Response.data.MangSettings.IsFreePer;
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 3) {
                    $state.go('LoginPage');
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 1) {
                    Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
                    blockUI.stop();
                }
            }, function (error) {
                Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
                blockUI.stop();
            });
        }
    }
}) ();