//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Compulsory_insuApp
{
    using System;
    using System.Collections.Generic;
    
    public partial class Agency
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Agency()
        {
            this.AgencyPermission = new HashSet<AgencyPermission>();
            this.AgenSysProfit = new HashSet<AgenSysProfit>();
            this.AgUsers = new HashSet<AgUsers>();
            this.Balance = new HashSet<Balance>();
            this.CompulsoryInsurenceTB = new HashSet<CompulsoryInsurenceTB>();
            this.CoNews = new HashSet<CoNews>();
            this.GivenSearils = new HashSet<GivenSearils>();
            this.MidcRes_GivenSearils = new HashSet<MidcRes_GivenSearils>();
            this.MidcRes_Seariles = new HashSet<MidcRes_Seariles>();
            this.MidcRes_SerialNums = new HashSet<MidcRes_SerialNums>();
            this.MidcRes_UsersSearils = new HashSet<MidcRes_UsersSearils>();
            this.Orange_Insurance_Policy = new HashSet<Orange_Insurance_Policy>();
            this.ResAgencies = new HashSet<ResAgencies>();
            this.Seariles = new HashSet<Seariles>();
            this.SerialNums = new HashSet<SerialNums>();
            this.Trav_Balance = new HashSet<Trav_Balance>();
            this.Trav_GivenSearils = new HashSet<Trav_GivenSearils>();
            this.Trav_InsuranceDocument = new HashSet<Trav_InsuranceDocument>();
            this.Trav_Seariles = new HashSet<Trav_Seariles>();
            this.Trav_SerialNums = new HashSet<Trav_SerialNums>();
            this.Trav_UsersSearils = new HashSet<Trav_UsersSearils>();
            this.UsersSearils = new HashSet<UsersSearils>();
            this.MidRes_InsuranceDocument = new HashSet<MidRes_InsuranceDocument>();
            this.Oran_GivenSearils = new HashSet<Oran_GivenSearils>();
            this.Oran_PrintedSerals = new HashSet<Oran_PrintedSerals>();
            this.Oran_Seariles = new HashSet<Oran_Seariles>();
            this.Oran_UsersSearils = new HashSet<Oran_UsersSearils>();
            this.SerialHistory = new HashSet<SerialHistory>();
            this.UnifiedRequests = new HashSet<UnifiedRequests>();
            this.AgencyEquipment = new HashSet<AgencyEquipment>();
            this.Comp_Agency = new HashSet<Comp_Agency>();
            this.Oran_SerialNums = new HashSet<Oran_SerialNums>();
        }
    
        public System.Guid AgencyID { get; set; }
        public string AgencyName { get; set; }
        public Nullable<System.Guid> AgenCatID { get; set; }
        public System.DateTime Del_Date { get; set; }
        public System.Guid CityID { get; set; }
        public string PhoneNum { get; set; }
        public Nullable<byte> Status { get; set; }
        public string InsertedBy { get; set; }
        public Nullable<System.DateTime> InsertDate { get; set; }
        public string UpdatedBy { get; set; }
        public Nullable<System.DateTime> UpdateDate { get; set; }
        public System.Guid ContryID { get; set; }
        public Nullable<double> ProftMargin { get; set; }
        public long AgNum { get; set; }
        public Nullable<byte> AgencyType { get; set; }
        public Nullable<bool> IsAllowedDibt { get; set; }
        public Nullable<double> AllowedDibtValue { get; set; }
        public Nullable<double> Palance { get; set; }
        public Nullable<double> SoldValue { get; set; }
        public Nullable<double> SoldOrange { get; set; }
        public Nullable<double> SoldTravel { get; set; }
        public Nullable<double> AddPalance { get; set; }
        public Nullable<bool> IsLocalCurrnecy { get; set; }
        public string AgImg { get; set; }
        public Nullable<System.Guid> ResponblityUsID { get; set; }
        public Nullable<double> Tax { get; set; }
        public Nullable<double> ReplaValue { get; set; }
        public string AgenNameEN { get; set; }
        public Nullable<bool> Trav_IsAllowedDibt { get; set; }
        public Nullable<double> Trav_AllowedDibtValue { get; set; }
        public Nullable<double> Trav_Palance { get; set; }
        public Nullable<double> Trav_SoldValue { get; set; }
        public Nullable<double> Trav_AddPalance { get; set; }
        public Nullable<System.Guid> ContGAenID { get; set; }
        public Nullable<double> OrangeTotal { get; set; }
        public Nullable<double> IntiaiPalance { get; set; }
        public Nullable<double> UsedValue { get; set; }
        public Nullable<double> AddValue { get; set; }
        public Nullable<double> Plance { get; set; }
        public Nullable<int> UnusedDocsLimt { get; set; }
        public Nullable<bool> Orang_IsADibt { get; set; }
        public Nullable<bool> MidRes_IsADibt { get; set; }
        public Nullable<double> Orang_AllowedDibtValue { get; set; }
        public Nullable<double> MidRes_AllowedDibtValue { get; set; }
        public Nullable<double> SoldMidRes { get; set; }
        public Nullable<int> LemitDayesPaym { get; set; }
    
        public virtual AgenCat AgenCat { get; set; }
        public virtual Cities Cities { get; set; }
        public virtual ContryMangments ContryMangments { get; set; }
        public virtual Country Country { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<AgencyPermission> AgencyPermission { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<AgenSysProfit> AgenSysProfit { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<AgUsers> AgUsers { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Balance> Balance { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<CompulsoryInsurenceTB> CompulsoryInsurenceTB { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<CoNews> CoNews { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<GivenSearils> GivenSearils { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<MidcRes_GivenSearils> MidcRes_GivenSearils { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<MidcRes_Seariles> MidcRes_Seariles { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<MidcRes_SerialNums> MidcRes_SerialNums { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<MidcRes_UsersSearils> MidcRes_UsersSearils { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Orange_Insurance_Policy> Orange_Insurance_Policy { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<ResAgencies> ResAgencies { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Seariles> Seariles { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<SerialNums> SerialNums { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Trav_Balance> Trav_Balance { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Trav_GivenSearils> Trav_GivenSearils { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Trav_InsuranceDocument> Trav_InsuranceDocument { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Trav_Seariles> Trav_Seariles { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Trav_SerialNums> Trav_SerialNums { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Trav_UsersSearils> Trav_UsersSearils { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<UsersSearils> UsersSearils { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<MidRes_InsuranceDocument> MidRes_InsuranceDocument { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Oran_GivenSearils> Oran_GivenSearils { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Oran_PrintedSerals> Oran_PrintedSerals { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Oran_Seariles> Oran_Seariles { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Oran_UsersSearils> Oran_UsersSearils { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<SerialHistory> SerialHistory { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<UnifiedRequests> UnifiedRequests { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<AgencyEquipment> AgencyEquipment { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Comp_Agency> Comp_Agency { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Oran_SerialNums> Oran_SerialNums { get; set; }
    }
}
