﻿using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Globalization;
using System.Linq;

namespace Compulsory_insuApp.Models
{
    public class Med_ResCl
    {
        public Guid Insu_ID { get; set; }
        public Guid AgencyID { get; set; }
        public DateTime Date_From { get; set; }
        public int Duration { get; set; }
        public int Cus_Phone { get; set; }
        public string DealerName { get; set; }
        public string CustomerName { get; set; }
        public string DealerCustomerReleation { get; set; }
        public Guid NationalityID { get; set; }
        public int BirthDate { get; set; }
        public int Doc_Num { get; set; }
        public string BirthPlace { get; set; }
        public Guid AddressID { get; set; }
        public Guid MaritalStatusID { get; set; }
        public Guid AcademicQualificationID { get; set; }
        public Guid ProfessionID { get; set; }
        public bool IsSalary { get; set; }
        public decimal Percentage { get; set; }
        public Guid InsertedBy { get; set; }
    }
    public class MedResDoc_PrintCl
    {
        public string CardNo { get; set; }
        public string Cus_Name { get; set; } 
        public string ag_name {get; set; } 
        public string ag_adress {get; set; } 
        public string Dela_Name { get; set; } 
        public string Dela_cus_relation { get; set; } 
        public string Cus_adress { get; set; }
        //public string Cus_Phone { get; set; }
        public string ins_date { get; set; }
        public string ins_Time { get; set; }
        public string ins_day { get; set; }
        public string ins_dom { get; set; }
        public string ins_month { get; set; }
        public string ins_year { get; set; }
        public string Sdate { get; set; }
        public string Sday { get; set; }
        public string Stime { get; set; }
        public string Edate { get; set; }
        public string Eday { get; set; }
        public string Etime { get; set; }
        public string Nationality { get; set; }
        public string BirthDate { get; set; }
        public string Profession { get; set; }
        public string BirthPlace { get; set; }
        public string MaritalStatus { get; set; }
        public string dur { get; set; }
        public string AcademicQualification { get; set; }
        public string Cal_type { get; set; }
        public float Percentage { get; set; }
        public float Insat { get; set; }
        public float Tax { get; set; }
        public float Virsion { get; set; }
        public float Supervision { get; set; }
        public float Stamp { get; set; }
        public float Total { get; set; }
        public float Taxtotal { get; set; }



        public MedResDoc_PrintCl Doc_print(MidRes_InsuranceDocument Objmd)
        {
            Insh_AppsDBEntities db = new Insh_AppsDBEntities();
            var objQual = db.MidRes_AcademicQualification.Find(Objmd.AcademicQualificationID);
            var objAdress = db.MidcRes_Address.Find(Objmd.AddressID);
            var objNat = db.MidcRes_Nationality.Find(Objmd.NationalityID);
            MedResDoc_PrintCl Objmdd = new MedResDoc_PrintCl();
            Objmdd.CardNo = Objmd.SN;
            Objmdd.ag_name = db.Agency.Find(Objmd.AgencyID).AgencyName;
            Objmdd.ag_adress = db.Agency.Find(Objmd.AgencyID).Cities.CityName;
            Objmdd.Cus_Name = Objmd.CustomerName;
            Objmdd.Cus_adress = db.MidcRes_Address.Find(Objmd.AddressID).AddressName;
            Objmdd.Dela_Name = Objmd.DealerName;
            Objmdd.dur = Objmd.Duration.Value.ToString();
            Objmdd.Dela_cus_relation = Objmd.DealerCustomerReleation;
            Objmdd.ins_day = Objmd.InsertedDate.Value.ToString("dddd");
            Objmdd.ins_dom = Objmd.InsertedDate.Value.ToString("dd");
            Objmdd.ins_month = Objmd.InsertedDate.Value.ToString("MMMM");
            Objmdd.ins_year = Objmd.InsertedDate.Value.ToString("yyyy");
            Objmdd.Sday = Objmd.Date_From.Value.ToString("dddd");
            Objmdd.Stime = Objmd.Date_From.Value.ToString("hh:mm");
            Objmdd.ins_Time = Objmd.Date_To.Value.ToString("hh:mm");
            Objmdd.Sdate = Objmd.Date_From.Value.ToString("yyyy-MM-dd");
            Objmdd.Edate = Objmd.Date_To.Value.ToString("yyyy-MM-dd");
            Objmdd.Eday = Objmd.Date_To.Value.ToString("dddd");
            Objmdd.Etime = Objmd.Date_To.Value.ToString("hh:mm");
            Objmdd.Nationality = db.MidcRes_Nationality.Find(Objmd.NationalityID).NationalityName;
            Objmdd.BirthDate = Objmd.BirthDate.Value.ToString("yyyy-MM-dd");
            Objmdd.BirthPlace = Objmd.BirthPlace;
            Objmdd.Profession = db.MidcRes_Profession.Find(Objmd.ProfessionID).ProfessionName;
            Objmdd.MaritalStatus = db.MidcRes_MaritalStatus.Find(Objmd.MaritalStatusID).MaritalStatus;
            Objmdd.AcademicQualification = db.MidRes_AcademicQualification.Find(Objmd.AcademicQualificationID).AcademicQualificationName;
            Objmdd.Cal_type = (Objmd.IsSalary ?? false) ? "نسبة مرتب" : "حسب المهنة";
            Objmdd.Insat = (float) Objmd.Sattlement;
            Objmdd.Tax = (float) Objmd.Tax;
            Objmdd.Virsion = (float) Objmd.Version;
            Objmdd.Supervision = (float) Objmd.Supervision;
            Objmdd.Stamp = (float) Objmd.Stamp;
            Objmdd.Total = (float) Objmd.Total;
            Objmdd.Taxtotal = (float) Objmd.Taxtotal;
            Objmdd.Percentage = (float)Objmd.Percentage;
            return Objmdd;
        }

    }
    public class Doc_rptCl
    {
        public long? DocNum { get; set; }
        public Guid Sys_ID { get; set; }
        public string CustName { get; set; }
        public string FromDate { get; set; }
        public string ToDate { get; set; }
        public string Type { get; set; }
        public string InsertedBy { get; set; }
        public string sn { get; set; }
        public string InsertDate { get; set; }
        public double Total { get; set; }
        public double Pfroft { get; set; }
        public int Duration { get; set; }
        public int? InsuNumber { get; set; }
        public int D_type { get; set; }
        public byte Status { get; set; }
        public double TaxTotal { get; set; }
        public double TotWithTax { get; set; }
        public double Tax_Stamp { get; set; }
        public double Tax_Insu { get; set; }
        public double Tax_SuperVision { get; set; }
        public double Tax_Tax { get; set; }
        public DateTime? BitthDate { get; set; }
    }
        //public class AgPermClass
    public class System
    {
        public Guid Sys_ID { get; set; }
        public string SysName { get; set; }
    }

    public class ReportsClass
    {
        public Guid DocID { get; set; }
        public string docnumb { get; set; }
        public string DocTypeName { get; set; }
        public string name { get; set; }
        public string Sn { get; set; }
        public string Color { get; set; }
        public string CarName { get; set; }
        public string Sdate { get; set; }
        public string DateEnd { get; set; }
        public int Dur { get; set; }
        public int DocTypeID { get; set; }
        public string Col_name { get; set; }
        public string intime { get; set; }
        public string AgName { get; set; }
        public string FullName { get; set; }
        public float Val { get; set; }
        public float Total { get; set; }
        public Guid AgID { get; set; }
        public Guid InsertBy { get; set; }

    }
    public class AgPermClass
    {
        public bool Cump_Sys { get; set; }
        public bool Cump_ChangeOwnership { get; set; }
        public bool Cump_AdditionalServices { get; set; }
        public bool Cump_OldInterface { get; set; }
        public bool Orange_Sys { get; set; }
        public bool Travel_Sys { get; set; }
        public bool Trav_Calculation { get; set; }
        public bool Trav_ShowAllDocs { get; set; }
        public bool medical_sys { get; set; }
        public bool print_normal { get; set; }
        public bool paper_A4 { get; set; }
        public bool inventory_requests { get; set; }
        public bool colored_paper { get; set; }
        public Guid selecteAgencyID { get; set; }
        public Guid InsertBy { get; set; }
        public AgPermClass getPerbyAgencyID(Guid selecteAgencyID)
        {
            Insh_AppsDBEntities db = new Insh_AppsDBEntities();
            AgPermClass Agcl = new AgPermClass();
            var Agperm = db.AgencyPermission.Where(c => c.Status == 1 && c.AgencyID == selecteAgencyID);
            Agcl.Cump_Sys = Agperm.Where(c => c.AgPermissionsList.EngName == "Cump_Sys").Count() == 1;
            Agcl.Cump_ChangeOwnership = Agperm.Where(c => c.AgPermissionsList.EngName == "Cump_ChangeOwnership").Count() == 1;
            Agcl.Cump_AdditionalServices = Agperm.Where(c => c.AgPermissionsList.EngName == "Cump_AdditionalServices").Count() == 1;
            Agcl.Cump_OldInterface = Agperm.Where(c => c.AgPermissionsList.EngName == "Cump_OldInterface").Count() == 1;
            Agcl.Orange_Sys = Agperm.Where(c => c.AgPermissionsList.EngName == "Orange_Sys").Count() == 1;
            Agcl.Travel_Sys = Agperm.Where(c => c.AgPermissionsList.EngName == "Travel_Sys").Count() == 1;
            Agcl.Trav_Calculation = Agperm.Where(c => c.AgPermissionsList.EngName == "Trav_Calculation").Count() == 1;
            Agcl.Trav_ShowAllDocs = Agperm.Where(c => c.AgPermissionsList.EngName == "Trav_ShowAllDocs").Count() == 1;
            Agcl.medical_sys = Agperm.Where(c => c.AgPermissionsList.EngName == "medical_sys").Count() == 1;
            Agcl.print_normal = Agperm.Where(c => c.AgPermissionsList.EngName == "print_normal").Count() == 1;
            Agcl.paper_A4 = Agperm.Where(c => c.AgPermissionsList.EngName == "paper_A4").Count() == 1;
            Agcl.colored_paper = Agperm.Where(c => c.AgPermissionsList.EngName == "colored_paper").Count() == 1;
            Agcl.inventory_requests = Agperm.Where(c => c.AgPermissionsList.EngName == "inventory_requests").Count() == 1;
            return Agcl;
        }

    }
    public class OrangApiAuth
    {
        public string user_name { get; set; }
        public string pass_word { get; set; }

        public OrangApiAuth get()
        {
            OrangApiAuth obj = new OrangApiAuth();
            obj.user_name = "adminalatahadiya";
            obj.pass_word = "ALA-2025";
            return obj;
        }
    }
    public class OrangDoc_Cl
    {
        public string CardNo { get; set; }
        public string Cus_Name { get; set; }
        public string Cus_adress { get; set; }
        public string Cus_Phone { get; set; }
        public string Via_Type { get; set; }
        public string Via_nat { get; set; }
        public string Via_Made_Year { get; set; }
        public string Chas_no { get; set; }
        public string Pa_no { get; set; }
        public string Motor_No { get; set; }
        public string UseType { get; set; }
        public string F_Time { get; set; }
        public string To_Time { get; set; }
        public string F_date { get; set; }
        public string To_Date { get; set; }
        public string F_DayOfweek { get; set; }
        public string To_DayOfweek { get; set; }
        public int Ins_Day { get; set; }
        public string Ins_DayOfWeek { get; set; }
        public string Ins_ArMonthName { get; set; }
        public int Ins_Year { get; set; }
        public float DailyInsat { get; set; }
        public float Insat { get; set; }
        public float  Tax { get; set; }
        public float  Virsion { get; set; }
        public float Supervision { get; set; }
        public float  Stamp { get; set; }
        public float  Total { get; set; }
        


        public OrangDoc_Cl OrangDoc_print(Orange_Insurance_Policy Doc)
        {
            Insh_AppsDBEntities db = new Insh_AppsDBEntities();
            var objCars = db.OrangeCars.Find(Doc.CarID);
            var objCarsNat = db.VehicleNationality.Find(Doc.Vehicle_Nationality_ID);
            var objClu = db.Orange_Insurance_Clause.Find(Doc.InsuranceClauseID);
            OrangDoc_Cl obj = new OrangDoc_Cl();
            obj.Cus_Name = Doc.Insurance_Name;
            obj.Cus_adress = Doc.Insurance_Location;
            obj.Cus_Phone = Doc.Insurance_Phone;
            obj.Via_Type = objCars.carName;
            obj.Via_nat = objCarsNat.vehicle_nationality_name;
            obj.Via_Made_Year = Doc.Car_Made_Date.ToString();
            obj.Chas_no = Doc.Chassis_Number;
            obj.Pa_no = Doc.Plate_Number;
            obj.Motor_No = Doc.motor_number;
            obj.UseType = objClu.Slug;
            obj.F_Time = Doc.insurance_day_from.Value.ToString("hh:mm");
            obj.To_Time = Doc.insurance_day_to.Value.ToString("hh:mm");
            obj.F_date = Doc.insurance_day_from.Value.ToString("yyyy-MMM-dd");
            obj.To_Date = Doc.insurance_day_to.Value.ToString("yyyy-MMM-dd");
            obj.F_DayOfweek = Doc.insurance_day_from.Value.ToString("dddd");
            obj.To_DayOfweek = Doc.insurance_day_to.Value.ToString("dddd");
            obj.Ins_Day = Doc.Insurance_Date.Value.Day;
            obj.Ins_DayOfWeek = Doc.Insurance_Date.Value.ToString("dddd");
            obj.Ins_ArMonthName = Doc.Insurance_Date.Value.ToString("MMMM");
            obj.CardNo = Doc.sr_Card_No;
            CultureInfo arabicCulture = new CultureInfo("ar-SA");
            obj.Ins_ArMonthName = Doc.Insurance_Date.Value.ToString("MMMM" , arabicCulture);
            obj.Ins_Year = Doc.Insurance_Date.Value.Year;
            obj.DailyInsat =(float?) Doc.insurance_installment_daily ?? 0;
            obj.Insat =((float?) Doc.Insurance_Installment ?? 0) + ((float ?)Doc.ContValue ?? 0);
            obj.Tax = (float?) Doc.Insurance_Tax ?? 0;
            obj.Virsion = (float?) Doc.Insurance_Version ?? 0;
            obj.Supervision = (float?) Doc.Insurance_Supervision ?? 0;
            obj.Stamp = (float?) Doc.Insurance_Stamp ?? 0;
            obj.Total = (float?) Doc.Insurance_Total ?? 0;
            return obj;
        }
      
    }
    public class Orange_InsCl
    {
        public Guid ID { get; set; }
        public DateTime Insh_S_Date { get; set; }
        public DateTime Insh_E_Date { get; set; }
        public int DurInDays { get; set; }
        public int pap_no { get; set; }
        public string CusName { get; set; }
        public string Adrees { get; set; }
        public string Phone_num { get; set; }
        public string ins_clause { get; set; }
        public string motor_no { get; set; }
        public string ReqText { get; set; }
        public Guid ins_clause_ID { get; set; }
        public string chassis_no { get; set; }
        public string Plate_No { get; set; }
        public string Eng_num { get; set; }
        public Guid carID { get; set; }
        public int CarMade_date { get; set; }
        public string Country_Symbol { get; set; }
        public Guid Visited_Cout_ID { get; set; }
        public Guid Car_Nationality_ID { get; set; }
        public Guid UsID { get; set; }
        public Guid AgEncyID { get; set; }
        public float installment { get; set; }
        public float Daily_installment { get; set; }
        public float ContryValue { get; set; }
        public int ContryCount { get; set; }
        public float Supervision { get; set; }
        public float Stamp { get; set; }
        public float Tax { get; set; }
        public float Version { get; set; }
       

    }
     
  
    public class Trav_CalTable
    {
        public int Count { get; set; }
        public int BDay { get; set; }
        public string NameWorkPlace { get; set; }
        public string AgeDesc { get; set; }
        public int Months { get; set; }
        public int YearOfBirth { get; set; }
        public int Duration { get; set; }
        public Guid UsID { get; set; }
        public Guid Zid { get; set; }
        public float? DocVal { get; set; }
        public float ? Totoal { get; set; }
        public float? Stamp { get; set; }
        public float ? Eshraf { get; set; }
        public float ? Tax { get; set; }
        public float ? Esdar { get; set; }
    }
    public class CalCl
    {
        public int Count { get; set; }
        public string UsID { get; set; }
        public int BDay { get; set; }
        public int Months { get; set; }
        public int ByearDay { get; set; }
        public Guid ZoonID { get; set; }
   
    }

    public class Trav_insDocClass
        {
            public Guid? InsID { get; set; }
            public Guid Zid { get; set; }
            public int Months { get; set; }
            public byte Gender { get; set; }
            public string CuName { get; set; }
            public string PassportID { get; set; }
            public string NameCustEN { get; set; }
            public int BDay { get; set; }
            public DateTime SDate { get; set; }
            public Guid UsID { get; set; }
            public string Cus_Phone { get; set; }
            public int InsuNumber { get; set; }
            public DateTime clintDate { get; set; }
            public List<Trav_AgesTypes> Trav_GetAgeRow(int Age)

            {
                Insh_AppsDBEntities db = new Insh_AppsDBEntities();
                return db.Trav_AgesTypes.Where(p => p.Status == 1 && Age >= p.AgStart && Age <= p.AgEnd).ToList();
            }
            public List<Trav_DurationTypes> Trav_GetDurtionRow(int dur)

            {
                Insh_AppsDBEntities db = new Insh_AppsDBEntities();
                return db.Trav_DurationTypes.Where(p => p.Status == 1 && dur >= p.NoOfmonths && dur <= p.EndDu).ToList(); ;
            }
            public int RoudToPoint1(float num)
            {

                float x = (num % 1);
                int y = (int)num;
                if (x > 0 && x < 1)
                    x = 1;

                int number = y + (int)x;
                return number;
            }

            public float GetLeftDocValue(int Bydate, DateTime Sdate, float WorkInsPrice)
            {
                Insh_AppsDBEntities db = new Insh_AppsDBEntities();
                Trav_insDocClass ic = new Trav_insDocClass();
                float RealDura = (float)(DateTime.Now - SDate).TotalDays / 30;
                int Age = DateTime.Now.Year - Bydate;
                int dur = ic.RoudToPoint1(RealDura);
                var Ag = db.Trav_AgesTypes.Where(p => p.Status == 1 && Age >= p.AgStart && Age <= p.AgEnd).ToList();
                var Dur = db.Trav_DurationTypes.Where(p => p.Status == 1 && dur >= p.NoOfmonths && dur <= p.EndDu).ToList();
                var AgPer = Ag[0];
                var DurPer = Dur[0];
                float InsValue = WorkInsPrice;
                float AgeValue = ((float)AgPer.AgePercentage * InsValue);
                float DurValue = (((float)DurPer.DurationPercentage) * (AgeValue));
                return DurValue;
            }
            public Trav_InsuranceDocument Trav_InsertNewInsu(Trav_insDocClass MainObj, Trav_InsuranceDocument du, List<AgUsers> us, ref bool HasPlance, ref Agency Agen)
            {
                Insh_AppsDBEntities db = new Insh_AppsDBEntities();
                Insur_class ic = new Insur_class();
                Guid uid =MainObj.UsID;
                //Guid wid = Guid.Parse("6822405d-3c27-4fd6-b5a3-9fa7ce49d72e");
                DateTime Bydate = new DateTime(MainObj.BDay, 01, 01);
                HasPlance = true;
                Guid IDins = Guid.NewGuid();
                Agen = db.Agency.Find(us[0].AgentId);
                var Contry = db.Country.Find(Agen.ContryID);

                float ProftPer = (float?)Agen.ProftMargin ?? 0;
                var vir = db.Virebles.Where(c => c.ConntryID == Contry.CountryID && c.Status == 1).ToList();
                int Age = DateTime.Now.Year - Bydate.Year;
                int dur = MainObj.Months;
                var Ag = db.Trav_AgesTypes.Where(p => p.Status == 1 && Age >= p.AgStart && Age <= p.AgEnd).ToList();
                var Dur = db.Trav_DurationTypes.Where(p => p.Status == 1 && dur >= p.NoOfmonths && dur <= p.EndDu).ToList();
                var AgPer = Ag[0];
                var DurPer = Dur[0];
                float InsValue = (float?)db.Trav_Zoon.Find(MainObj.Zid).ZoonPrice ?? 0;
                float AgeValue = ((float)AgPer.AgePercentage * InsValue);
                float DurValue = (((float)DurPer.DurationPercentage) * (AgeValue));
                float Minp = Agen.IsAllowedDibt == false ? 0 : ((float)Agen.AllowedDibtValue * -1);
                float DocValue = DurValue;
                float ProfitVal = DocValue * (ProftPer / 100);
                List<Trav_DocTax> dt = new List<Trav_DocTax>();
                List<Trav_DocTax> DtaxList = new List<Trav_DocTax>();

                foreach (var it in vir)
                {
                    Trav_DocTax obj = new Trav_DocTax();
                    obj.DocTaxID = Guid.NewGuid();
                    obj.DocID = du.InsuranceID;
                    obj.VirID = it.VirD;
                    obj.Value = it.IsPer == false ? it.Value : DocValue * ((float)it.Value);
                    obj.Value = it.IsrounsdedTo == 0.5 ? ic.RoudToPoint5((float)obj.Value) : obj.Value;
                    obj.Value = it.IsWithProfitMargin == true ? obj.Value + ProfitVal : obj.Value;
                    obj.IsPer = it.IsPer ?? false;
                    obj.Status = 1;
                    dt.Add(obj);
                }
                float TaxValue = (float)dt.Sum(c => c.Value);
                float DiffToround = ic.RondedValueTo5(DocValue + TaxValue);
                TaxValue = TaxValue + DiffToround;
                dt.SingleOrDefault(c => c.VirID == Guid.Parse("0C4E4EB4-ED07-4192-8041-E3954E9792D2")).Value = dt.SingleOrDefault(c => c.VirID == Guid.Parse("0C4E4EB4-ED07-4192-8041-E3954E9792D2")).Value
                    + DiffToround;
                TaxValue = TaxValue + DiffToround;

                float pal = ((float?)Agen.Palance ?? 0) - (Agen.IsLocalCurrnecy == false ? DurValue + TaxValue : DocValue);
                if (pal <= Minp)
                {
                    HasPlance = false;

                }
                du.InsuranceID = IDins;
                du.AgencyID = (Guid)us[0].AgentId;
                du.AgeValue = AgeValue;
                du.DurValue = DurValue;
                du.TaxTotal = TaxValue;
                du.PfroftMargin = ProfitVal;
                du.DurationMonths = dur;
                du.Total = DocValue;
                du.TotWithTax = (DocValue + TaxValue);
                du.NameCust = MainObj.CuName;
                du.PassportID = MainObj.PassportID;
                du.BitthDate = Bydate;
                du.IsInDeleteProgress = false;
                du.FromDate = MainObj.SDate;
                du.ToDate = MainObj.SDate.AddMonths(((int)MainObj.Months));
                du.DurationTypeID = DurPer.DurationID;
                du.AgeTypeID = AgPer.AgeID;
                du.Age = Age;
                du.ClintDate = MainObj.clintDate;
                du.Status = 1;
                du.InsertedBy =  MainObj.UsID;
                du.InsertDate = DateTime.Now;
                du.Cus_Phone = MainObj.Cus_Phone;
                du.InsuNumber = MainObj.InsuNumber;
                du.Gender = MainObj.Gender;
                db.Trav_InsuranceDocument.Add(du);
                db.SaveChanges();
                du.StDucNum = du.InsertDate.Value.ToString("yyyy") + " " + Agen.AgNum + " " +
                          du.InsuNumber.ToString() + " " + du.DocNum.ToString("D" + 5);
                db.Entry(du).State = EntityState.Modified;
                db.SaveChanges();
                foreach (var it in dt)
                    db.Trav_DocTax.Add(it);
                db.SaveChanges();
                return du;
            }

        }

        public class RePlaceCl
        {
            public int DocNoTXT { get; set; }
            public Guid OldID { get; set; }
            public Guid city_ID { get; set; }
            public string c_Name { get; set; }
            public string c_address { get; set; }
            public string Ib_num { get; set; }
            public string c_phone { get; set; }
            public Guid? AgEncyID { get; set; }
            public Guid InsertBy { get; set; }
        }
        public class Extend
        {
            public int DocNoTXT { get; set; }
            public Guid AgEncyID { get; set; }
            public Guid? OldID { get; set; }
            public Guid InsertBy { get; set; }
            public string f_date { get; set; }
            public string to_date { get; set; }
        }
        public class RenewCl
        {
            public int DocNoTXT { get; set; }
            public Guid AgEncyID { get; set; }
            public Guid? OldID { get; set; }
            public Guid InsertBy { get; set; }
        }
        public class Doc_Print {
            public float Tax { get; set; }
            public int DocType_ID { get; set; }
            public string DocType_Desc { get; set; }
            public string Com_Name { get; set; }
            public float Tax_supervision { get; set; }
            public float Tax_stamp { get; set; }
            public float Tax_insu { get; set; }
            public float Paper_No { get; set; }
            public float NomOfPassengers { get; set; }
            public string agencyCityAddres { get; set; }
            public string Chassis_number { get; set; }
            public string PhoneNum { get; set; }
            public string Address { get; set; }
            public string Agency { get; set; }
            public int Manufacturing_Year { get; set; }
            public int PayLoad { get; set; }
            public float Engine_Capacity { get; set; }
            public string Engine_number { get; set; }
            public string contry { get; set; }
            public bool IsDelProg { get; set; }
            public string c_name { get; set; }
            public string f_date { get; set; }
            public string to_date { get; set; }
            public string color { get; set; }
            public int dur { get; set; }
            public string iron_bord { get; set; }
            public string durtype { get; set; }
            public string Ins_SN { get; set; }
            public string mas_name { get; set; }
            public string car_name { get; set; }
            public float ins_val { get; set; }
            public float TaxVal { get; set; }
            public byte status { get; set; }
            public float total_tax { get; set; }
            public string ValInletters { get; set; }
            public string InsuAdress { get; set; }
            public string OldDoc { get; set; }
            public string InsertBy { get; set; }
            public string InsertDate { get; set; }


            public Doc_Print print(CompulsoryInsurenceTB obj)
            {
                Insh_AppsDBEntities db = new Insh_AppsDBEntities();
                Doc_Print doc = new Doc_Print();
                var Old = db.OtherServHis.Where(a => a.NewDocID == obj.Insu_ID && obj.DocType == a.Type).
                      Select(a => new { a.OldDoc, a.CompulsoryInsurenceTB1.Ins_SN }).ToList();
                doc.DocType_ID = obj.DocType;
                doc.DocType_Desc = obj.DocTypes == null ? db.DocTypes.Find(obj.DocType).DocTypeDesc : obj.DocTypes.DocTypeDesc;
                doc.Address = obj.Address;
                doc.Tax = (float?)obj.Tax ?? 0;
                doc.Tax_supervision = (float?)obj.Tax_supervision ?? 0;
                doc.Tax_stamp = (float?)obj.Tax_stamp ?? 0;
                doc.Tax_insu = (float?)obj.Tax_insu ?? 0;
                doc.Paper_No = obj.Paper_No;
                doc.NomOfPassengers = obj.NomOfPassengers;
                doc.agencyCityAddres = obj.Agency.Cities.CityName;
                doc.InsuAdress = obj.Cities == null ? db.Cities.Find(obj.WorckPlaceInsuID).CityName : obj.Cities.CityName;
                doc.Chassis_number = obj.Chassis_number;
                doc.PhoneNum = obj.PhoneNum;
                doc.Address = obj.Address;
                doc.Agency = obj.Agency.AgencyName;
                doc.Manufacturing_Year = obj.Manufacturing_Year;
                doc.PayLoad = (int)obj.PayLoad;
                doc.Engine_Capacity = (int)obj.Engine_Capacity;
                doc.Engine_number = obj.Engine_number;
                doc.contry = obj.Country == null ? db.Country.Find(obj.Manfactire_ContryID).CountryName : obj.Country.CountryName;
                doc.Ins_SN = obj.Ins_SN;
                doc.c_name = obj.CusName;
                doc.f_date = obj.DateFrom.ToString("yyyy-MM-ddTHH:mm:ss");
                doc.to_date = obj.DateTo.ToString("yyyy-MM-ddTHH:mm:ss");
                doc.color = obj.Colors == null ? db.Colors.Find(obj.ColorID).ColorNaame : obj.Colors.ColorNaame;
                doc.dur = (int)obj.Durations;
                doc.iron_bord = obj.IronBoard_num;
                doc.mas_name = obj.CompulsoryPriceMaster == null ? db.CompulsoryPriceMaster.Find(obj.CompMasterID).CompMasterName :
                    obj.CompulsoryPriceMaster.CompMasterName;
                doc.car_name = obj.CarsBrand == null ? db.CarsBrand.Find(obj.CarBarndID).CarNaame : obj.CarsBrand.CarNaame;
                doc.durtype = obj.InsTypePerDurID == 1 ? "m" : "d";
                doc.ins_val = (float?)obj.InsValue ?? 0;
                doc.TaxVal = (float?)obj.Tax ?? 0;
                doc.total_tax = (float?)obj.TotalWithTax ?? 0;
                doc.ValInletters = new ToWord(Convert.ToDecimal(obj.TotalWithTax), new CurrencyInfo(CurrencyInfo.Currencies.Libya)).ConvertToArabic();
                doc.InsertBy = db.AgUsers.Find(obj.InsertBy).AgUserName;
                doc.InsertDate = obj.InsertDate.ToUniversalTime().AddHours(2).ToString("yyyy-MM-ddTHH:mm:ss");
                doc.OldDoc = Old.Count() == 0 ? " - " : Old[0].Ins_SN;
                return doc;
            }



            public Doc_Print printOldDoc(CompulsoryInsurenceTB obj)
            {
                Insh_AppsDBEntities db = new Insh_AppsDBEntities();
                Doc_Print doc = new Doc_Print();
                var Old = db.oldOwner.Where(a => a.newDocId == obj.Insu_ID && obj.DocType == 4).
                      Select(a => new { a.oldDocNum, a.CompulsoryInsurenceTB.Ins_SN }).ToList();
                doc.DocType_ID = obj.DocType;
                doc.DocType_Desc = obj.DocTypes == null ? db.DocTypes.Find(obj.DocType).DocTypeDesc : obj.DocTypes.DocTypeDesc;
                doc.Address = obj.Address;
                doc.Tax = (float?)obj.Tax ?? 0;
                doc.Tax_supervision = (float?)obj.Tax_supervision ?? 0;
                doc.Tax_stamp = (float?)obj.Tax_stamp ?? 0;
                doc.Tax_insu = (float?)obj.Tax_insu ?? 0;
                doc.Paper_No = obj.Paper_No;
                doc.NomOfPassengers = obj.NomOfPassengers;
                doc.agencyCityAddres = obj.Agency.Cities.CityName;
                doc.InsuAdress = obj.Cities == null ? db.Cities.Find(obj.WorckPlaceInsuID).CityName : obj.Cities.CityName;
                doc.Chassis_number = obj.Chassis_number;
                doc.PhoneNum = obj.PhoneNum;
                doc.Address = obj.Address;
                doc.Agency = obj.Agency.AgencyName;
                doc.Manufacturing_Year = obj.Manufacturing_Year;
                doc.PayLoad = (int)obj.PayLoad;
                doc.Engine_Capacity = (int)obj.Engine_Capacity;
                doc.Engine_number = obj.Engine_number;
                doc.contry = obj.Country == null ? db.Country.Find(obj.Manfactire_ContryID).CountryName : obj.Country.CountryName;
                doc.Ins_SN = obj.Ins_SN;
                doc.c_name = obj.CusName;
                doc.f_date = obj.DateFrom.ToString("yyyy-MM-ddTHH:mm:ss");
                doc.to_date = obj.DateTo.ToString("yyyy-MM-ddTHH:mm:ss");
                doc.color = obj.Colors == null ? db.Colors.Find(obj.ColorID).ColorNaame : obj.Colors.ColorNaame;
                doc.dur = (int)obj.Durations;
                doc.iron_bord = obj.IronBoard_num;
                doc.mas_name = obj.CompulsoryPriceMaster == null ? db.CompulsoryPriceMaster.Find(obj.CompMasterID).CompMasterName :
                    obj.CompulsoryPriceMaster.CompMasterName;
                doc.car_name = obj.CarsBrand == null ? db.CarsBrand.Find(obj.CarBarndID).CarNaame : obj.CarsBrand.CarNaame;
                doc.durtype = obj.InsTypePerDurID == 1 ? "m" : "d";
                doc.ins_val = (float?)obj.InsValue ?? 0;
                doc.TaxVal = (float?)obj.Tax ?? 0;
                doc.total_tax = (float?)obj.TotalWithTax ?? 0;
                doc.ValInletters = new ToWord(Convert.ToDecimal(obj.TotalWithTax), new CurrencyInfo(CurrencyInfo.Currencies.Libya)).ConvertToArabic();
                doc.InsertBy = db.AgUsers.Find(obj.InsertBy).AgUserName;
                doc.InsertDate = obj.InsertDate.ToUniversalTime().AddHours(2).ToString("yyyy-MM-ddTHH:mm:ss");
                doc.OldDoc = Old.Count() == 0 ? " - " : Old[0].oldDocNum;
                return doc;
            }


        }

        public class RptDatesList
        {
            public string TextDate { get; set; }
            public string Dateform { get; set; }
            public string DateTo { get; set; }

            public int MyProperty { get; set; }

        }

        public class reportesClass
        {
            public DateTime? Datefrom { get; set; }
            public DateTime? DateTo { get; set; }
            public int selectredio { get; set; }
            public int? selectedYear { get; set; }
            public int? selectedMonth { get; set; }
            public int RadioBut { get; set; }

            public string[] GuID { get; set; }
            public string[] SysIds { get; set; }
            public List<string> AlwaysFilled { get; set; }


        }
        public class a45cl
        {
            public Guid? ins_ID { get; set; }

            public Guid? car_color { get; set; }
            public Guid? AgEncyID { get; set; }

            public Guid? car_ID { get; set; }
            public Guid? Com_ID { get; set; }
            public Guid? c_DetailsID { get; set; }
            public Guid? c_masterID { get; set; }
            public Guid? con_ID { get; set; }

            public Guid? Ins_SN { get; set; }
            public Guid? city_ID { get; set; }
            public int Ins_Type { get; set; }
            public int Ins_duration { get; set; }
            public int c_load { get; set; }
            public int pap_no { get; set; }
            public string c_Name { get; set; }
            public string Ib_num { get; set; }
            public string chass_num { get; set; }
            public string OldDocNum { get; set; }
            public string Eng_num { get; set; }
            public float eng_cap { get; set; }
            public DateTime S_Date { get; set; }
            public DateTime E_date { get; set; }
            public int NoOfPassen { get; set; }
            public int m_year { get; set; }
            public string c_Address { get; set; }
            public string c_phone { get; set; }
            public Guid InsertBy { get; set; }
            //public string c_NameEN { get; set; }

        }
        public class CompInsuTypes
        {
            public List<CompInsTypeID> ID { get; set; }
            public string Name { get; set; }

            public List<CompInsuTypes> GetDurations()
            {
                List<CompInsuTypes> ct_li = new List<CompInsuTypes>();
                CompInsuTypes ct = new CompInsuTypes();
                CompInsTypeID ct_ID = new CompInsTypeID();
                ct.Name = "التأمين الإجباري السنوي";
                ct_ID.TypeID = 1;
                ct_ID.Desc = "سنة";
                ct_ID.value = 1;
                ct_ID.StartDate = DateTime.Now.ToUniversalTime().AddHours(2).Date.ToString("yyyy-MM-ddTHH:mm:ss");
                ct_ID.EndDate = DateTime.Now.ToUniversalTime().AddHours(2).Date.AddYears(ct_ID.value).ToString("yyyy-MM-ddTHH:mm:ss");
                ct_ID.Duration = 12;
                ct_ID.Duration_type = 'y';
                ct_ID.MinDuration = 12;
                ct_ID.MaxDuration = 12;
                ct.ID.Add(ct_ID);
                ct_ID = new CompInsTypeID();
                ct_ID.TypeID = 2;
                ct_ID.Desc = "سنتين";
                ct_ID.value = 2;
                ct_ID.StartDate = DateTime.Now.ToUniversalTime().AddHours(2).Date.ToString("yyyy-MM-ddTHH:mm:ss");
                ct_ID.EndDate = DateTime.Now.ToUniversalTime().AddHours(2).Date.AddYears(ct_ID.value).ToString("yyyy-MM-ddTHH:mm:ss");
                ct_ID.Duration = 12;
                ct_ID.Duration_type = 'y';
                ct_ID.MinDuration = 24;
                ct_ID.MaxDuration = 24;
                ct_ID = new CompInsTypeID();
                ct_ID.TypeID = 3;
                ct_ID.Desc = "ثلاث سنوات";
                ct_ID.value = 3;
                ct_ID.StartDate = DateTime.Now.ToUniversalTime().AddHours(2).Date.ToString("yyyy-MM-ddTHH:mm:ss");
                ct_ID.EndDate = DateTime.Now.ToUniversalTime().AddHours(2).Date.AddYears(ct_ID.value).ToString("yyyy-MM-ddTHH:mm:ss");
                ct_ID.Duration = 12;
                ct_ID.Duration_type = 'y';
                ct_ID.MinDuration = 36;
                ct_ID.MaxDuration = 36;
                ct = new CompInsuTypes();
                ct.Name = "فترة قصيرة (سياحية)";
                ct_ID = new CompInsTypeID();
                ct_ID.TypeID = 4;
                ct_ID.Desc = "إسبوعين";
                ct_ID.value = 15;
                ct_ID.StartDate = DateTime.Now.ToUniversalTime().AddHours(2).Date.ToString("yyyy-MM-ddTHH:mm:ss");
                ct_ID.EndDate = DateTime.Now.ToUniversalTime().AddHours(2).Date.AddDays(ct_ID.value).ToString("yyyy-MM-ddTHH:mm:ss"); ;
                ct_ID.Duration_type = 'd';
                ct_ID.MinDuration = 15;
                ct_ID.MaxDuration = 15;
                ct.ID.Add(ct_ID);
                ct_ID = new CompInsTypeID();
                ct_ID.TypeID = 5;
                ct_ID.Desc = "شهر";
                ct_ID.value = 1;
                ct_ID.StartDate = DateTime.Now.ToUniversalTime().AddHours(2).Date.ToString("yyyy-MM-ddTHH:mm:ss");
                ct_ID.EndDate = DateTime.Now.ToUniversalTime().AddHours(2).Date.AddMonths(ct_ID.value).ToString("yyyy-MM-ddTHH:mm:ss"); ;
                ct_ID.Duration_type = 'm';
                ct_ID.MinDuration = 1;
                ct_ID.MaxDuration = 1;
                ct_ID = new CompInsTypeID();
                ct_ID.TypeID = 6;
                ct_ID.Desc = "شهرين";
                ct_ID.value = 2;
                ct_ID.StartDate = DateTime.Now.ToUniversalTime().AddHours(2).Date.ToString("yyyy-MM-ddTHH:mm:ss");
                ct_ID.EndDate = DateTime.Now.ToUniversalTime().AddHours(2).Date.AddMonths(ct_ID.value).ToString("yyyy-MM-ddTHH:mm:ss"); ;
                ct_ID.Duration_type = 'm';
                ct_ID.MinDuration = 60;
                ct_ID.MaxDuration = 60;
                ct_ID = new CompInsTypeID();
                ct_ID.TypeID = 7;
                ct_ID.Desc = "ثلاث شهور";
                ct_ID.value = 3;
                ct_ID.StartDate = DateTime.Now.ToUniversalTime().AddHours(2).Date.ToString("yyyy-MM-ddTHH:mm:ss");
                ct_ID.EndDate = DateTime.Now.ToUniversalTime().AddHours(2).Date.AddMonths(ct_ID.value).ToString("yyyy-MM-ddTHH:mm:ss"); ;
                ct_ID.Duration_type = 'm';
                ct_ID.MinDuration = 90;
                ct_ID.MaxDuration = 60;
                ct.ID.Add(ct_ID);
                ct_li.Add(ct);
                return ct_li;
            }
        }
        public class CompInsTypeID
        {
            public int TypeID { get; set; }
            public int value { get; set; }
            public string Desc { get; set; }
            public string StartDate { get; set; }
            public string EndDate { get; set; }
            public int Duration { get; set; }
            public char Duration_type { get; set; }
            public int MaxDuration { get; set; }
            public int MinDuration { get; set; }
        }
        public class UserClass
        {
            public string UsID { get; set; }
            public string UsName { get; set; }
            public string FullName { get; set; }
            public string Password { get; set; }
            public string PhoneNum { get; set; }
            public int Status { get; set; }
            public int UsTypeID { get; set; }
            public string InsertBy { get; set; }
        }

        //
        public class PrintRptType
        {
            public string Name { get; set; }
            public int ID { get; set; }

        }

        public class CompulsoryInsurTB
        {
            public string Insu_ID { get; set; }
            public string AgencyID { get; set; }
            public int Status { get; set; }
            public DateTime UpdateDate { get; set; }
            public string UpdateBy { get; set; }
        }

    public partial class DocTax
    {
        public Guid DocTaxID { get; set; }
        public Nullable<Guid> DocID { get; set; }
        public Nullable<Guid> VirID { get; set; }
        public Nullable<double> Value { get; set; }
        public Nullable<bool> IsPer { get; set; }
        public Nullable<byte> Status { get; set; }
        public Nullable<Guid> Updateby { get; set; }
        public Nullable<DateTime> UpdateDate { get; set; }

        //public virtual Virebles Virebles { get; set; }
        //public virtual Trav_InsuranceDocument InsuranceDocument { get; set; }
    }


    public class TaxsList
        {
            public float Value { get; set; }
            public string VirDesc { get; set; }
            public string VirDescEn { get; set; }

        }
    public class Insur_class
    {
        Insh_AppsDBEntities db = new Insh_AppsDBEntities();

        public Guid GetDefultClass()
        {
            return Guid.Parse("2EE1C04F-87AC-49DA-9B50-E0D58CF5F62E");
        }
        public List<TaxsList> GetTax(Guid InsID)
        {
            var TaxsnewList = db.Trav_DocTax.Where(c => c.DocID == InsID && c.Status == 1).Select(c => new
            {
                c.Virebles.VirDesc,
                c.Value,
                c.Virebles.Eng_Name,
                c.Virebles.InsertDate
            }).ToList();

            List<TaxsList> li = new List<TaxsList>();

            var Taxs = TaxsnewList.OrderBy(x => x.InsertDate);
            foreach (var it in Taxs)
            {
                TaxsList tl = new TaxsList();
                tl.VirDesc = it.VirDesc;
                tl.VirDescEn = it.Eng_Name;
                tl.Value = (float?)it.Value ?? 0;
                li.Add(tl);
            }
            return li;

        }
        public List<Trav_AgesTypes> GetAgeRow(int Age)

        {
            Insh_AppsDBEntities db = new Insh_AppsDBEntities();
            return db.Trav_AgesTypes.Where(p => p.Status == 1 && Age >= p.AgStart && Age <= p.AgEnd).ToList();
        }
        public List<Trav_DurationTypes> GetDurtionRow(int dur)

        {
            Insh_AppsDBEntities db = new Insh_AppsDBEntities();
            return db.Trav_DurationTypes.Where(p => p.Status == 1 && dur >= p.NoOfmonths && dur <= p.EndDu).ToList(); ;
        }
        public float RoudToPoint5(float num)
        {

            float x = (num % 1);
            int y = (int)num;
            if (x >= 0 && x <= 0.50)
                x = (float)0.50;
            if (x > 0.50 && x < 1)
                x = 1;
            float number = y + x;
            return number;
        }
        public float RoudTo1(float num)
        {

            float x = (num % 1);
            int y = (int)num;
            if (x > 0 && x < 1)
                y = y + 1;
            return y;
        }




        public bool HasPer(string UserID)
        {
            Guid xx = Guid.Parse(UserID);
            if (UserID == "")
                return false;
            var us = db.AgencyPermission.Where(p => p.AgencyID == xx && p.Status == 1).ToList();
            if (us.Count() == 0 || us.Count() > 1)
                return false;
            else
                return true;
        }

        public float RondedValueTo5(float num)
        {
            float x = num;
            float flo = x % 1;
            int IntNum = (int)x;
            if (flo > 0)
                IntNum = IntNum + 1;
            string xxx = IntNum.ToString();
            string LastNumSt = xxx.Substring(xxx.Length - 1);
            int LastDigt = int.Parse(LastNumSt);
            if (LastDigt > 0 && LastDigt <= 5)
                IntNum = (IntNum - LastDigt) + 5;
            if (LastDigt > 5 && LastDigt <= 9)
                IntNum = (IntNum - LastDigt) + 10;
            return IntNum - num;
        }
        public float RondedValue(float num)
        {
            float x = (num % 1);
            int y = (int)num;
            if (x > 0 && x < 0.25)
                x = (float)0.25;
            if (x > 0.25 && x < 0.50)
                x = (float)0.5;
            if (x > 0.75 && x < 1)
                x = 1;
            if (x > 0.50 && x < 0.75)
                x = (float)0.75;
            if (x > 0.75)
                x = 1;
            float number = y + x;
            return number;
        }
        public bool IsExsit(string UserID)
        {
            Guid xx = Guid.Parse(UserID);
            if (UserID == "")
                return false;
            var us = db.AgUsers.Where(p => p.AgUserID == xx && p.Status == 1).ToList();
            if (us.Count() == 1)
                return true;
            else
                return false;


        }
        public class Trav_insDocPrintClass
        {
            public Guid? InsID { get; set; }

            public string zoon { get; set; }
            public string zoonTXt { get; set; }
            public string zoonTXtEn { get; set; }
            public int durationInDays { get; set; }
            public string InsDocNum { get; set; }
            public string docnume { get; set; }
            public string Gender { get; set; }
            public string CuName { get; set; }
            public string NameCustEN { get; set; }
            public string PassportID { get; set; }
            public string birthDay { get; set; }
            public string SDate { get; set; }
            public string EDate { get; set; }
            public string clintDate { get; set; }
            public string Cus_Phone { get; set; }
            public Guid UsID { get; set; }
            public string Wherfroma { get; set; }
            public string UserName { get; set; }
            public string PrintDate { get; set; }
            public string WorkPlace { get; set; }
            public float Totoal { get; set; }
            public float TotTax { get; set; }
            public float DocVal { get; set; }
            public string DuToStDsc { get; set; }
            public string insert_date { get; set; }
            public float OldTotal { get; set; }
            public float CurrenTValue { get; set; }


        }
        public Trav_insDocPrintClass Trav_PrintIns(Trav_InsuranceDocument InsDoc)
        {
            Insh_AppsDBEntities db = new Insh_AppsDBEntities();
            var agency = InsDoc.Agency == null ? db.Agency.Find(InsDoc.AgencyID) : InsDoc.Agency;
            var zoon = InsDoc.Trav_Zoon == null ? db.Trav_Zoon.Find(InsDoc.ZoonID) : InsDoc.Trav_Zoon;
            Trav_insDocPrintClass doc = new Trav_insDocPrintClass();
            doc.InsDocNum = InsDoc.StDucNum;
            doc.docnume = InsDoc.DocNum.ToString();
            doc.zoon = zoon.ZoonName;
            doc.zoonTXt = zoon.ZoonText;
            doc.zoonTXtEn = zoon.ZoonTextEN;
            doc.Gender = InsDoc.Gender == 1 ?"دكر":"أنتى";
            doc.PassportID = InsDoc.PassportID;
            doc.durationInDays = (int)(InsDoc.FromDate - InsDoc.ToDate).TotalDays * -1;
            doc.birthDay = InsDoc.BitthDate.ToString("yyy-MM-dd ");
            doc.CuName = InsDoc.NameCust;
            doc.Cus_Phone = InsDoc.Cus_Phone;
            doc.Wherfroma = agency.AgencyName;
            var uid = InsDoc.InsertedBy;
            doc.UserName = db.AgUsers.Find(uid).AgUserName;
            doc.insert_date = InsDoc.InsertDate.Value.ToString();
            doc.CuName = InsDoc.NameCust;
            doc.SDate = InsDoc.FromDate.ToString("yyyy-MM-ddTHH:mm:ss");
            doc.EDate = InsDoc.ToDate.ToString("yyyy-MM-ddTHH:mm:ss");
            doc.PrintDate = InsDoc.InsertDate.Value.ToString("yyyy-MM-ddTHH:mm:ss");
            doc.Totoal = ((float)InsDoc.Total);
           
            doc.TotTax = (float)InsDoc.TotWithTax;
            return doc;
        }
        public Guid GetLibyaID()
        {
            return Guid.Parse("86a60dfb-dce1-4916-993d-a3394cd23ea6");
        }


    }
        class ToWord
        {
            /// Group Levels: 987,654,321.234
            /// 234 : Group Level -1
            /// 321 : Group Level 0
            /// 654 : Group Level 1
            /// 987 : Group Level 2

            #region Varaibles & Properties

            /// <summary>
            /// integer part
            /// </summary>
            private long _intergerValue;

            /// <summary>
            /// Decimal Part
            /// </summary>
            private int _decimalValue;

            /// <summary>
            /// Number to be converted
            /// </summary>
            public Decimal Number { get; set; }

            /// <summary>
            /// Currency to use
            /// </summary>
            public CurrencyInfo Currency { get; set; }

            /// <summary>
            /// English text to be placed before the generated text
            /// </summary>
            public String EnglishPrefixText { get; set; }

            /// <summary>
            /// English text to be placed after the generated text
            /// </summary>
            public String EnglishSuffixText { get; set; }

            /// <summary>
            /// Arabic text to be placed before the generated text
            /// </summary>
            public String ArabicPrefixText { get; set; }

            /// <summary>
            /// Arabic text to be placed after the generated text
            /// </summary>
            public String ArabicSuffixText { get; set; }
            #endregion

            #region General

            /// <summary>
            /// Constructor: short version
            /// </summary>
            /// <param name="number">Number to be converted</param>
            /// <param name="currency">Currency to use</param>
            /// 

            public ToWord(Decimal number, CurrencyInfo currency)
            {
                InitializeClass(number, currency, String.Empty, "only.", "فقط", "لا غير");
            }



            /// <summary>
            /// Constructor: Full Version
            /// </summary>
            /// <param name="number">Number to be converted</param>
            /// <param name="currency">Currency to use</param>
            /// <param name="englishPrefixText">English text to be placed before the generated text</param>
            /// <param name="englishSuffixText">English text to be placed after the generated text</param>
            /// <param name="arabicPrefixText">Arabic text to be placed before the generated text</param>
            /// <param name="arabicSuffixText">Arabic text to be placed after the generated text</param>
            public ToWord(Decimal number, CurrencyInfo currency, String englishPrefixText, String englishSuffixText, String arabicPrefixText, String arabicSuffixText)
            {
                InitializeClass(number, currency, englishPrefixText, englishSuffixText, arabicPrefixText, arabicSuffixText);
            }

            /// <summary>
            /// Initialize Class Varaibles
            /// </summary>
            /// <param name="number">Number to be converted</param>
            /// <param name="currency">Currency to use</param>
            /// <param name="englishPrefixText">English text to be placed before the generated text</param>
            /// <param name="englishSuffixText">English text to be placed after the generated text</param>
            /// <param name="arabicPrefixText">Arabic text to be placed before the generated text</param>
            /// <param name="arabicSuffixText">Arabic text to be placed after the generated text</param>
            private void InitializeClass(Decimal number, CurrencyInfo currency, String englishPrefixText, String englishSuffixText, String arabicPrefixText, String arabicSuffixText)
            {
                Number = number;
                Currency = currency;
                EnglishPrefixText = englishPrefixText;
                EnglishSuffixText = englishSuffixText;
                ArabicPrefixText = arabicPrefixText;
                ArabicSuffixText = arabicSuffixText;

                ExtractIntegerAndDecimalParts();
            }

            /// <summary>
            /// Get Proper Decimal Value
            /// </summary>
            /// <param name="decimalPart">Decimal Part as a String</param>
            /// <returns></returns>
            private string GetDecimalValue(string decimalPart)
            {
                string result = String.Empty;

                if (Currency.PartPrecision != decimalPart.Length)
                {
                    int decimalPartLength = decimalPart.Length;

                    for (int i = 0; i < Currency.PartPrecision - decimalPartLength; i++)
                    {
                        decimalPart += "0"; //Fix for 1 number after decimal ( 10.5 , 1442.2 , 375.4 ) 
                    }

                    result = String.Format("{0}.{1}", decimalPart.Substring(0, Currency.PartPrecision), decimalPart.Substring(Currency.PartPrecision, decimalPart.Length - Currency.PartPrecision));

                    result = (Math.Round(Convert.ToDecimal(result))).ToString();
                }
                else
                    result = decimalPart;

                for (int i = 0; i < Currency.PartPrecision - result.Length; i++)
                {
                    result += "0";
                }

                return result;
            }

            /// <summary>
            /// Eextract Interger and Decimal parts
            /// </summary>
            private void ExtractIntegerAndDecimalParts()
            {
                String[] splits = Number.ToString().Split('.');

                _intergerValue = Convert.ToInt32(splits[0]);

                if (splits.Length > 1)
                    _decimalValue = Convert.ToInt32(GetDecimalValue(splits[1]));
            }
            #endregion

            #region English Number To Word

            #region Varaibles

            private static string[] englishOnes =
               new string[] {
            "Zero", "One", "Two", "Three", "Four", "Five", "Six", "Seven", "Eight", "Nine",
            "Ten", "Eleven", "Twelve", "Thirteen", "Fourteen", "Fifteen", "Sixteen", "Seventeen", "Eighteen", "Nineteen"
            };

            private static string[] englishTens =
                new string[] {
            "Twenty", "Thirty", "Forty", "Fifty", "Sixty", "Seventy", "Eighty", "Ninety"
            };

            private static string[] englishGroup =
                new string[] {
            "Hundred", "Thousand", "Million", "Billion", "Trillion", "Quadrillion", "Quintillion", "Sextillian",
            "Septillion", "Octillion", "Nonillion", "Decillion", "Undecillion", "Duodecillion", "Tredecillion",
            "Quattuordecillion", "Quindecillion", "Sexdecillion", "Septendecillion", "Octodecillion", "Novemdecillion",
            "Vigintillion", "Unvigintillion", "Duovigintillion", "10^72", "10^75", "10^78", "10^81", "10^84", "10^87",
            "Vigintinonillion", "10^93", "10^96", "Duotrigintillion", "Trestrigintillion"
            };
            #endregion

            /// <summary>
            /// Process a group of 3 digits
            /// </summary>
            /// <param name="groupNumber">The group number to process</param>
            /// <returns></returns>
            private string ProcessGroup(int groupNumber)
            {
                int tens = groupNumber % 100;

                int hundreds = groupNumber / 100;

                string retVal = String.Empty;

                if (hundreds > 0)
                {
                    retVal = String.Format("{0} {1}", englishOnes[hundreds], englishGroup[0]);
                }
                if (tens > 0)
                {
                    if (tens < 20)
                    {
                        retVal += ((retVal != String.Empty) ? " " : String.Empty) + englishOnes[tens];
                    }
                    else
                    {
                        int ones = tens % 10;

                        tens = (tens / 10) - 2; // 20's offset

                        retVal += ((retVal != String.Empty) ? " " : String.Empty) + englishTens[tens];

                        if (ones > 0)
                        {
                            retVal += ((retVal != String.Empty) ? " " : String.Empty) + englishOnes[ones];
                        }
                    }
                }

                return retVal;
            }

            /// <summary>
            /// Convert stored number to words using selected currency
            /// </summary>
            /// <returns></returns>
            public string ConvertToEnglish()
            {
                Decimal tempNumber = Number;

                if (tempNumber == 0)
                    return "Zero";

                string decimalString = ProcessGroup(_decimalValue);

                string retVal = String.Empty;

                int group = 0;

                if (tempNumber < 1)
                {
                    retVal = englishOnes[0];
                }
                else
                {
                    while (tempNumber >= 1)
                    {
                        int numberToProcess = (int)(tempNumber % 1000);

                        tempNumber = tempNumber / 1000;

                        string groupDescription = ProcessGroup(numberToProcess);

                        if (groupDescription != String.Empty)
                        {
                            if (group > 0)
                            {
                                retVal = String.Format("{0} {1}", englishGroup[group], retVal);
                            }

                            retVal = String.Format("{0} {1}", groupDescription, retVal);
                        }

                        group++;
                    }
                }

                String formattedNumber = String.Empty;
                formattedNumber += (EnglishPrefixText != String.Empty) ? String.Format("{0} ", EnglishPrefixText) : String.Empty;
                formattedNumber += (retVal != String.Empty) ? retVal : String.Empty;
                //formattedNumber += (retVal != String.Empty) ? (_intergerValue == 1 ? Currency.EnglishCurrencyName : Currency.EnglishPluralCurrencyName) : String.Empty;
                formattedNumber += (decimalString != String.Empty) ? " and " : String.Empty;
                formattedNumber += (decimalString != String.Empty) ? decimalString : String.Empty;
                //formattedNumber += (decimalString != String.Empty) ? " " + (_decimalValue == 1 ? Currency.EnglishCurrencyPartName : Currency.EnglishPluralCurrencyPartName) : String.Empty;
                //formattedNumber += (EnglishSuffixText != String.Empty) ? String.Format(" {0}", EnglishSuffixText) : String.Empty;

                return formattedNumber;
            }

            public string CovertToArabicNumbers()
            {
                Decimal tempNumber = Number;
                string StrArb = tempNumber.ToString();


                StrArb = StrArb.ToString().Replace("0", "٠");
                StrArb = StrArb.ToString().Replace("1", "١");
                StrArb = StrArb.ToString().Replace("2", "٢");
                StrArb = StrArb.ToString().Replace("3", "٣");
                StrArb = StrArb.ToString().Replace("4", "٤");
                StrArb = StrArb.ToString().Replace("5", "٥");
                StrArb = StrArb.ToString().Replace("6", "٦");
                StrArb = StrArb.ToString().Replace("7", "٧");
                StrArb = StrArb.ToString().Replace("8", "٨");
                StrArb = StrArb.ToString().Replace("9", "٩");

                return StrArb;

            }

            #endregion

            #region Arabic Number To Word

            #region Varaibles

            private static string[] arabicOnes =
               new string[] {
            String.Empty, "واحد", "اثنان", "ثلاثة", "أربعة", "خمسة", "ستة", "سبعة", "ثمانية", "تسعة",
            "عشرة", "أحد عشر", "اثنا عشر", "ثلاثة عشر", "أربعة عشر", "خمسة عشر", "ستة عشر", "سبعة عشر", "ثمانية عشر", "تسعة عشر"
            };

            private static string[] arabicFeminineOnes =
               new string[] {
            String.Empty, "إحدى", "اثنتان", "ثلاث", "أربع", "خمس", "ست", "سبع", "ثمان", "تسع",
            "عشر", "إحدى عشرة", "اثنتا عشرة", "ثلاث عشرة", "أربع عشرة", "خمس عشرة", "ست عشرة", "سبع عشرة", "ثماني عشرة", "تسع عشرة"
            };

            private static string[] arabicTens =
                new string[] {
            "عشرون", "ثلاثون", "أربعون", "خمسون", "ستون", "سبعون", "ثمانون", "تسعون"
            };

            private static string[] arabicHundreds =
                new string[] {
            "", "مائة", "مئتان", "ثلاثمائة", "أربعمائة", "خمسمائة", "ستمائة", "سبعمائة", "ثمانمائة","تسعمائة"
            };

            private static string[] arabicAppendedTwos =
                new string[] {
            "مئتا", "ألفا", "مليونا", "مليارا", "تريليونا", "كوادريليونا", "كوينتليونا", "سكستيليونا"
            };

            private static string[] arabicTwos =
                new string[] {
            "مئتان", "ألفان", "مليونان", "ملياران", "تريليونان", "كوادريليونان", "كوينتليونان", "سكستيليونان"
            };

            private static string[] arabicGroup =
                new string[] {
            "مائة", "ألف", "مليون", "مليار", "تريليون", "كوادريليون", "كوينتليون", "سكستيليون"
            };

            private static string[] arabicAppendedGroup =
                new string[] {
            "", "ألفاً", "مليوناً", "ملياراً", "تريليوناً", "كوادريليوناً", "كوينتليوناً", "سكستيليوناً"
            };

            private static string[] arabicPluralGroups =
                new string[] {
            "", "آلاف", "ملايين", "مليارات", "تريليونات", "كوادريليونات", "كوينتليونات", "سكستيليونات"
            };
            #endregion

            /// <summary>
            /// Get Feminine Status of one digit
            /// </summary>
            /// <param name="digit">The Digit to check its Feminine status</param>
            /// <param name="groupLevel">Group Level</param>
            /// <returns></returns>
            private string GetDigitFeminineStatus(int digit, int groupLevel)
            {
                if (groupLevel == -1)
                { // if it is in the decimal part
                    if (Currency.IsCurrencyPartNameFeminine)
                        return arabicFeminineOnes[digit]; // use feminine field
                    else
                        return arabicOnes[digit];
                }
                else
                    if (groupLevel == 0)
                {
                    if (Currency.IsCurrencyNameFeminine)
                        return arabicFeminineOnes[digit];// use feminine field
                    else
                        return arabicOnes[digit];
                }
                else
                    return arabicOnes[digit];
            }

            /// <summary>
            /// Process a group of 3 digits
            /// </summary>
            /// <param name="groupNumber">The group number to process</param>
            /// <returns></returns>
            private string ProcessArabicGroup(int groupNumber, int groupLevel, Decimal remainingNumber)
            {
                int tens = groupNumber % 100;

                int hundreds = groupNumber / 100;

                string retVal = String.Empty;

                if (hundreds > 0)
                {
                    if (tens == 0 && hundreds == 2) // حالة المضاف
                        retVal = String.Format("{0}", arabicAppendedTwos[0]);
                    else //  الحالة العادية
                        retVal = String.Format("{0}", arabicHundreds[hundreds]);
                }

                if (tens > 0)
                {
                    if (tens < 20)
                    { // if we are processing under 20 numbers
                        if (tens == 2 && hundreds == 0 && groupLevel > 0)
                        { // This is special case for number 2 when it comes alone in the group
                            if (_intergerValue == 2000 || _intergerValue == 2000000 || _intergerValue == 2000000000 || _intergerValue == 2000000000000 || _intergerValue == 2000000000000000 || _intergerValue == 2000000000000000000)
                                retVal = String.Format("{0}", arabicAppendedTwos[groupLevel]); // في حالة الاضافة
                            else
                                retVal = String.Format("{0}", arabicTwos[groupLevel]);//  في حالة الافراد
                        }
                        else
                        { // General case
                            if (retVal != String.Empty)
                                retVal += " و ";

                            if (tens == 1 && groupLevel > 0 && hundreds == 0)
                                retVal += " ";
                            else
                                if ((tens == 1 || tens == 2) && (groupLevel == 0 || groupLevel == -1) && hundreds == 0 && remainingNumber == 0)
                                retVal += String.Empty; // Special case for 1 and 2 numbers like: ليرة سورية و ليرتان سوريتان
                            else
                                retVal += GetDigitFeminineStatus(tens, groupLevel);// Get Feminine status for this digit
                        }
                    }
                    else
                    {
                        int ones = tens % 10;
                        tens = (tens / 10) - 2; // 20's offset

                        if (ones > 0)
                        {
                            if (retVal != String.Empty)
                                retVal += " و ";

                            // Get Feminine status for this digit
                            retVal += GetDigitFeminineStatus(ones, groupLevel);
                        }

                        if (retVal != String.Empty)
                            retVal += " و ";

                        // Get Tens text
                        retVal += arabicTens[tens];
                    }
                }

                return retVal;
            }

            /// <summary>
            /// Convert stored number to words using selected currency
            /// </summary>
            /// <returns></returns>
            public string ConvertToArabic()
            {
                Decimal tempNumber = Number;

                if (tempNumber == 0)
                    return "صفر";

                // Get Text for the decimal part
                string decimalString = ProcessArabicGroup(_decimalValue, -1, 0);

                string retVal = String.Empty;
                Byte group = 0;
                while (tempNumber >= 1)
                {
                    // seperate number into groups
                    int numberToProcess = (int)(tempNumber % 1000);

                    tempNumber = tempNumber / 1000;

                    // convert group into its text
                    string groupDescription = ProcessArabicGroup(numberToProcess, group, Math.Floor(tempNumber));

                    if (groupDescription != String.Empty)
                    { // here we add the new converted group to the previous concatenated text
                        if (group > 0)
                        {
                            if (retVal != String.Empty)
                                retVal = String.Format("{0} {1}", "و", retVal);

                            if (numberToProcess != 2)
                            {
                                if (numberToProcess % 100 != 1)
                                {
                                    if (numberToProcess >= 3 && numberToProcess <= 10) // for numbers between 3 and 9 we use plural name
                                        retVal = String.Format("{0} {1}", arabicPluralGroups[group], retVal);
                                    else
                                    {
                                        if (retVal != String.Empty) // use appending case
                                            retVal = String.Format("{0} {1}", arabicAppendedGroup[group], retVal);
                                        else
                                            retVal = String.Format("{0} {1}", arabicGroup[group], retVal); // use normal case
                                    }
                                }
                                else
                                {
                                    retVal = String.Format("{0} {1}", arabicGroup[group], retVal); // use normal case
                                }
                            }
                        }

                        retVal = String.Format("{0} {1}", groupDescription, retVal);
                    }

                    group++;
                }

                String formattedNumber = String.Empty;
                formattedNumber += (ArabicPrefixText != String.Empty) ? String.Format("{0} ", ArabicPrefixText) : String.Empty;
                formattedNumber += (retVal != String.Empty) ? retVal : String.Empty;
                if (_intergerValue != 0)
                { // here we add currency name depending on _intergerValue : 1 ,2 , 3--->10 , 11--->99
                    int remaining100 = (int)(_intergerValue % 100);

                    if (remaining100 == 0)
                        formattedNumber += Currency.Arabic1CurrencyName;
                    else
                        if (remaining100 == 1)
                        formattedNumber += Currency.Arabic1CurrencyName;
                    else
                            if (remaining100 == 2)
                    {
                        if (_intergerValue == 2)
                            formattedNumber += Currency.Arabic2CurrencyName;
                        else
                            formattedNumber += Currency.Arabic1CurrencyName;
                    }
                    else
                                if (remaining100 >= 3 && remaining100 <= 10)
                        formattedNumber += Currency.Arabic310CurrencyName;
                    else
                                    if (remaining100 >= 11 && remaining100 <= 99)
                        formattedNumber += Currency.Arabic1199CurrencyName;
                }
                formattedNumber += (_decimalValue != 0) ? " و " : String.Empty;
                formattedNumber += (_decimalValue != 0) ? decimalString : String.Empty;
                if (_decimalValue != 0)
                { // here we add currency part name depending on _intergerValue : 1 ,2 , 3--->10 , 11--->99
                    formattedNumber += " ";

                    int remaining100 = (int)(_decimalValue % 100);

                    if (remaining100 == 0)
                        formattedNumber += Currency.Arabic1CurrencyPartName;
                    else
                        if (remaining100 == 1)
                        formattedNumber += Currency.Arabic1CurrencyPartName;
                    else
                            if (remaining100 == 2)
                        formattedNumber += Currency.Arabic2CurrencyPartName;
                    else
                                if (remaining100 >= 3 && remaining100 <= 10)
                        formattedNumber += Currency.Arabic310CurrencyPartName;
                    else
                                    if (remaining100 >= 11 && remaining100 <= 99)
                        formattedNumber += Currency.Arabic1199CurrencyPartName;
                }
                formattedNumber += (ArabicSuffixText != String.Empty) ? String.Format(" {0}", ArabicSuffixText) : String.Empty;
                ArabicSuffixText = "";

                formattedNumber += (ArabicSuffixText != String.Empty) ? String.Format(" {0}", ArabicSuffixText) : String.Empty;

                return formattedNumber;
            }
            #endregion
        }
        public class CurrencyInfo
        {
            public enum Currencies { UAE = 0, Syria, SaudiArabia, Libya, Gold };

            #region Constructors

            public CurrencyInfo(Currencies currency)
            {
                switch (currency)
                {
                    case Currencies.UAE:
                        CurrencyID = 0;
                        CurrencyCode = "AED";
                        IsCurrencyNameFeminine = false;
                        EnglishCurrencyName = "UAE Dirham";
                        EnglishPluralCurrencyName = "UAE Dirhams";
                        EnglishCurrencyPartName = "Fils";
                        EnglishPluralCurrencyPartName = "Fils";
                        Arabic1CurrencyName = "درهم إماراتي";
                        Arabic2CurrencyName = "درهمان إماراتيان";
                        Arabic310CurrencyName = "دراهم إماراتية";
                        Arabic1199CurrencyName = "درهماً إماراتياً";
                        Arabic1CurrencyPartName = "فلس";
                        Arabic2CurrencyPartName = "فلسان";
                        Arabic310CurrencyPartName = "فلوس";
                        Arabic1199CurrencyPartName = "فلساً";
                        PartPrecision = 2;
                        IsCurrencyPartNameFeminine = false;
                        break;


                    case Currencies.Syria:
                        CurrencyID = 1;
                        CurrencyCode = "SYP";
                        IsCurrencyNameFeminine = true;
                        EnglishCurrencyName = "Syrian Pound";
                        EnglishPluralCurrencyName = "Syrian Pounds";
                        EnglishCurrencyPartName = "Piaster";
                        EnglishPluralCurrencyPartName = "Piasteres";
                        Arabic1CurrencyName = "ليرة سورية";
                        Arabic2CurrencyName = "ليرتان سوريتان";
                        Arabic310CurrencyName = "ليرات سورية";
                        Arabic1199CurrencyName = "ليرة سورية";
                        Arabic1CurrencyPartName = "قرش";
                        Arabic2CurrencyPartName = "قرشان";
                        Arabic310CurrencyPartName = "قروش";
                        Arabic1199CurrencyPartName = "قرشاً";
                        PartPrecision = 2;
                        IsCurrencyPartNameFeminine = false;
                        break;

                    case Currencies.SaudiArabia:
                        CurrencyID = 2;
                        CurrencyCode = "SAR";
                        IsCurrencyNameFeminine = false;
                        EnglishCurrencyName = "Saudi Riyal";
                        EnglishPluralCurrencyName = "Saudi Riyals";
                        EnglishCurrencyPartName = "Halala";
                        EnglishPluralCurrencyPartName = "Halalas";
                        Arabic1CurrencyName = "ريال سعودي";
                        Arabic2CurrencyName = "ريالان سعوديان";
                        Arabic310CurrencyName = "ريالات سعودية";
                        Arabic1199CurrencyName = "ريالاً سعودياً";
                        Arabic1CurrencyPartName = "هللة";
                        Arabic2CurrencyPartName = "هللتان";
                        Arabic310CurrencyPartName = "هللات";
                        Arabic1199CurrencyPartName = "هللة";
                        PartPrecision = 2;
                        IsCurrencyPartNameFeminine = true;
                        break;

                    case Currencies.Libya:
                        CurrencyID = 3;
                        CurrencyCode = "LYD";
                        IsCurrencyNameFeminine = false;
                        EnglishCurrencyName = "Libyan Dinar";
                        EnglishPluralCurrencyName = "Libyan Dinars";
                        EnglishCurrencyPartName = "milim";
                        EnglishPluralCurrencyPartName = "millimes";
                        Arabic1CurrencyName = "دينار ";
                        Arabic2CurrencyName = "ديناران ";
                        Arabic310CurrencyName = "دنانير ";
                        Arabic1199CurrencyName = "ديناراً ";
                        Arabic1CurrencyPartName = "قرش";
                        Arabic2CurrencyPartName = "قرشان";
                        Arabic310CurrencyPartName = "قروش";
                        Arabic1199CurrencyPartName = "قرشا";
                        PartPrecision = 2;
                        IsCurrencyPartNameFeminine = false;
                        break;

                    case Currencies.Gold:
                        CurrencyID = 4;
                        CurrencyCode = "XAU";
                        IsCurrencyNameFeminine = false;
                        EnglishCurrencyName = "Gram";
                        EnglishPluralCurrencyName = "Grams";
                        EnglishCurrencyPartName = "Milligram";
                        EnglishPluralCurrencyPartName = "Milligrams";
                        Arabic1CurrencyName = "جرام";
                        Arabic2CurrencyName = "جرامان";
                        Arabic310CurrencyName = "جرامات";
                        Arabic1199CurrencyName = "جراماً";
                        Arabic1CurrencyPartName = "ملجرام";
                        Arabic2CurrencyPartName = "ملجرامان";
                        Arabic310CurrencyPartName = "ملجرامات";
                        Arabic1199CurrencyPartName = "ملجراماً";
                        PartPrecision = 2;
                        IsCurrencyPartNameFeminine = false;
                        break;

                }
            }

            #endregion

            #region Properties

            /// <summary>
            /// Currency ID
            /// </summary>
            public int CurrencyID { get; set; }

            /// <summary>
            /// Standard Code
            /// Syrian Pound: SYP
            /// UAE Dirham: AED
            /// </summary>
            public string CurrencyCode { get; set; }

            /// <summary>
            /// Is the currency name feminine ( Mua'anath مؤنث)
            /// ليرة سورية : مؤنث = true
            /// درهم : مذكر = false
            /// </summary>
            public Boolean IsCurrencyNameFeminine { get; set; }

            /// <summary>
            /// English Currency Name for single use
            /// Syrian Pound
            /// UAE Dirham
            /// </summary>
            public string EnglishCurrencyName { get; set; }

            /// <summary>
            /// English Plural Currency Name for Numbers over 1
            /// Syrian Pounds
            /// UAE Dirhams
            /// </summary>
            public string EnglishPluralCurrencyName { get; set; }

            /// <summary>
            /// Arabic Currency Name for 1 unit only
            /// ليرة سورية
            /// درهم إماراتي
            /// </summary>
            public string Arabic1CurrencyName { get; set; }

            /// <summary>
            /// Arabic Currency Name for 2 units only
            /// ليرتان سوريتان
            /// درهمان إماراتيان
            /// </summary>
            public string Arabic2CurrencyName { get; set; }

            /// <summary>
            /// Arabic Currency Name for 3 to 10 units
            /// خمس ليرات سورية
            /// خمسة دراهم إماراتية
            /// </summary>
            public string Arabic310CurrencyName { get; set; }

            /// <summary>
            /// Arabic Currency Name for 11 to 99 units
            /// خمس و سبعون ليرةً سوريةً
            /// خمسة و سبعون درهماً إماراتياً
            /// </summary>
            public string Arabic1199CurrencyName { get; set; }

            /// <summary>
            /// Decimal Part Precision
            /// for Syrian Pounds: 2 ( 1 SP = 100 parts)
            /// for Tunisian Dinars: 3 ( 1 TND = 1000 parts)
            /// </summary>
            public Byte PartPrecision { get; set; }

            /// <summary>
            /// Is the currency part name feminine ( Mua'anath مؤنث)
            /// هللة : مؤنث = true
            /// قرش : مذكر = false
            /// </summary>
            public Boolean IsCurrencyPartNameFeminine { get; set; }

            /// <summary>
            /// English Currency Part Name for single use
            /// Piaster
            /// Fils
            /// </summary>
            public string EnglishCurrencyPartName { get; set; }

            /// <summary>
            /// English Currency Part Name for Plural
            /// Piasters
            /// Fils
            /// </summary>
            public string EnglishPluralCurrencyPartName { get; set; }

            /// <summary>
            /// Arabic Currency Part Name for 1 unit only
            /// قرش
            /// هللة
            /// </summary>
            public string Arabic1CurrencyPartName { get; set; }

            /// <summary>
            /// Arabic Currency Part Name for 2 unit only
            /// قرشان
            /// هللتان
            /// </summary>
            public string Arabic2CurrencyPartName { get; set; }

            /// <summary>
            /// Arabic Currency Part Name for 3 to 10 units
            /// قروش
            /// هللات
            /// </summary>
            public string Arabic310CurrencyPartName { get; set; }

            /// <summary>
            /// Arabic Currency Part Name for 11 to 99 units
            /// قرشاً
            /// هللةً
            /// </summary>
            public string Arabic1199CurrencyPartName { get; set; }
            #endregion
        }

    }

    public class CompulsoryInsurTB
    {
        public string Insu_ID { get; set; }
        public string AgencyID { get; set; }
        public int Status { get; set; }
        public DateTime UpdateDate { get; set; }
        public string UpdateBy { get; set; }
    }

    public class Med_ResCl
    {
        public Guid Insu_ID { get; set; }
        public Guid AgencyID { get; set; }
        public DateTime Date_From { get; set; }
        public int Duration { get; set; }
        public string DealerName { get; set; }
        public string CustomerName { get; set; }
        public int pap_no { get; set; }
        public string DealerCustomerReleation { get; set; }
        public Guid NationalityID { get; set; }
        public int BirthDate { get; set; }
        public string BirthPlace { get; set; }
        public Guid AddressID { get; set; }
        public Guid ProfessionID { get; set; }
        public Guid ? MaritalStatusID { get; set; }
        public Guid AcademicQualificationID { get; set; }
        public bool IsSalary { get; set; }
        public decimal Percentage { get; set; }
        public decimal Salary { get; set; }
        public Guid InsertedBy { get; set; }
    }


