﻿using System.Web;
using System.Web.Optimization;
using System;

namespace Compulsory_insuApp
{
    public class BundleConfig
    {
        // For more information on bundling, visit https://go.microsoft.com/fwlink/?LinkId=301862
        public static void RegisterBundles(BundleCollection bundles)
        {
            bundles.Add(new ScriptBundle("~/bundles/jquery").Include(
                        "~/Scripts/jquery-{version}.js"));

            bundles.Add(new ScriptBundle("~/bundles/jqueryval").Include(
                        "~/Scripts/jquery.validate*"));

            // Use the development version of Modernizr to develop with and learn from. Then, when you're
            // ready for production, use the build tool at https://modernizr.com to pick only the tests you need.
            bundles.Add(new ScriptBundle("~/bundles/modernizr").Include(
                        "~/Scripts/modernizr-*"));

            bundles.Add(new Bundle("~/bundles/bootstrap").Include(
                      "~/Scripts/bootstrap.js"));

            bundles.Add(new StyleBundle("~/Content/css").Include(
                      "~/Content/bootstrap.css",
                      "~/Content/site.css"));

            // Create a version string
            var version = DateTime.Now.ToString("yyyyMMddHHmmss");

            // Create bundle with version in the path
            bundles.Add(new ScriptBundle($"~/bundles/app_v{version}").Include(
                "~/FrontEnd/a30.js",
                "~/FrontEnd/a25.js",
                "~/FrontEnd/a28.js",
                "~/FrontEnd/a27.js",
                "~/FrontEnd/a29.js",
                "~/FrontEnd/a26.js",
                "~/FrontEnd/a44.js",
                "~/FrontEnd/a40.js",
                "~/FrontEnd/a47.js",
                "~/FrontEnd/a48.js",
                "~/FrontEnd/a49.js",
                "~/FrontEnd/a50.js",
                "~/FrontEnd/a60.js"
            ));

            // Enable bundling and minification
            BundleTable.EnableOptimizations = true;
        }
    }
}
