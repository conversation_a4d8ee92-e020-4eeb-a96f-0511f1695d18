﻿
(function () {
    'use strict';

    var controllerId = 'a27';
    angular.module('App').controller(controllerId, ["DataService", '$filter', "Notification", "blockUI", "$stateParams", "$state",Func]);


    function Func(DataService, $filter, Notification, blockUI, $stateParams, $state) {
        var vm = this;
        if ($stateParams.UserID == undefined || $stateParams.UserID == null || $stateParams.UserID == '' || $stateParams.AgenName=='')
            $state.go('LoginPage');
        else {
            vm.UserID = $stateParams.UserID;
            vm.UserName = $stateParams.UserName;
            vm.AgEncyID = $stateParams.AgEncyID;
            vm.AgenName = $stateParams.AgenName;
            vm.AgNum = $stateParams.AgNum;
            vm.UserType = $stateParams.UserType;
            vm.CompID = $stateParams.CompID;
            /* a26010();*/
            a26011();
         
            a270072();
            vm.ShowFlag = 0;
        }
        vm.BtnUsers = function () {
            $state.go('UsersPage', { 'UserID': vm.UserID, 'UserName': vm.UserName, 'AgEncyID': vm.AgEncyID, 'AgNum': vm.AgNum, 'AgenName': vm.AgenName })
        }


        vm.changPassword = function () {

            vm.NewPassword = '',
                vm.OldPassword = '',
                vm.titel="تغير كلمة المرور"

        };

     
        function a270072() {
            blockUI.start();
            DataService.a270072(vm.AgEncyID, vm.UserID).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
                    vm.AgPerObj = Response.data.AgPer;
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 1) {
                    Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 3) {
                    Notification.error({ message: "لا تملك الصلاحيات المناسبة", title: 'خطأ' });
                    blockUI.stop();
                }
            }, function (error) {
                Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
                blockUI.stop();
            });
        };

    
        vm.a27015 = function () {
            blockUI.start();
            DataService.a27015(vm.NewPassword , vm.OldPassword ,vm.UserID).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
        
                    Notification.success({ message: "تمت العملية الحفظ بنجاح", title: 'تعديل كلمة المرور ' });
                    $('#changePassModel').modal('hide');
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 1) {
                    Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 5) {
                    Notification.error({ message: Response.data.ErorrMessage, title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 3) {
                    $state.go('LoginPage');
                    blockUI.stop();
                }
            }, function (error) {
                Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
                blockUI.stop();
            });
        }
        function a26011() {
            blockUI.start();
            DataService.a26011( vm.UserID).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
                    vm.News = Response.data.News;
                    vm.UserType = Response.data.UserType;
                    vm.AgEncyID = Response.data.AgEncyID;
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 1) {
                    Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 5) {
                    Notification.error({ message: Response.data.ErorrMessage, title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 3) {
                    $state.go('LoginPage');
                    blockUI.stop();
                }
            }, function (error) {
                Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
                blockUI.stop();
            });
           
        };
     
        vm.UpdatePasswordDl = function () {
            vm.NewPassword = '';
            vm.OldPassword = '';
            $('#UpdatePasswordDl').modal('show');
        }
        vm.a270066 = function () {
            $state.go('MidRes_IssurancePage', { 'UserID': vm.UserID, 'UserName': vm.UserName, 'AgEncyID': vm.AgEncyID, 'AgenName': vm.AgenName, "AgNum": vm.AgNum ,"AgPerObj": vm.AgPerObj })
        }
        vm.a270015 = function () {
            $state.go('OtherServices', { 'UserID': vm.UserID, 'UserName': vm.UserName, 'AgEncyID': vm.AgEncyID, 'AgenName': vm.AgenName, "AgNum": vm.AgNum })
        }
        vm.a270018 = function () {
            $state.go('Orange_IssurancePage', { 'UserID': vm.UserID, 'UserName': vm.UserName, 'AgEncyID': vm.AgEncyID, 'AgenName': vm.AgenName, "AgNum": vm.AgNum })
        }
        vm.a270012 = function () {
            $state.go('IssuancePage', { 'UserID': vm.UserID, 'UserName': vm.UserName, 'AgEncyID': vm.AgEncyID, 'AgenName': vm.AgenName, "AgNum": vm.AgNum, "AgPerObj": vm.AgPerObj })
        }
        vm.a270019 = function () {
            $state.go('MidRes_IssurancePage', { 'UserID': vm.UserID, 'UserName': vm.UserName, 'AgEncyID': vm.AgEncyID, 'AgenName': vm.AgenName, "AgNum": vm.AgNum, "AgPerObj": vm.AgPerObj })
        }
        vm.a270013 = function () {
            $state.go('ReportesPage', { 'UserID': vm.UserID, 'UserName': vm.UserName, 'AgEncyID': vm.AgEncyID, 'AgenName': vm.AgenName ,"AgNum": vm.AgNum})
        }
        vm.a270014 = function () {
            $state.go('Trav_IssuancePage', { 'UserID': vm.UserID, 'UserName': vm.UserName, 'AgEncyID': vm.AgEncyID, 'AgenName': vm.AgenName, "AgNum": vm.AgNum})
        }
        vm.a270017 = function () {
            $state.go('Trav_ReportesPage', { 'UserID': vm.UserID, 'UserName': vm.UserName, 'AgEncyID': vm.AgEncyID, 'AgenName': vm.AgenName, "AgNum": vm.AgNum })
        }
        vm.StockRequestsPage = function () {
            $state.go('StockRequestsPage', { 'UserID': vm.UserID, 'UserName': vm.UserName, 'AgEncyID': vm.AgEncyID, 'AgenName': vm.AgenName, "AgNum": vm.AgNum })
        }
        vm.exit = function () {
            $state.go('LoginPage')
        }
   
     }

})();