﻿
(function () {
    'use strict';
   
    angular.module("App")
           .config(["$stateProvider", "$urlRouterProvider", '$locationProvider', function ($stateProvider, $urlRouterProvider, $locationProvider) {
          
               $urlRouterProvider.otherwise("LoginPage");
               $stateProvider
                   .state('HomeRpt', {
                       url: "/HomeRpt",
                       templateUrl: "/a44/a440004",
                       controller: "a44",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                           CompID: '',
                       }
                   })
                   .state('MidRes_IssurancePage', {
                       url:"/MidRes_IssurancePage",
                       templateUrl: function (params) {
                           if (params.UserID == '' || params.UserID == '' || params.UserID == '')
                               window.location.href = '/#!/_LoginPage'
                           else
                               return "/a60/a60000?Atu=" + params.UserID;
                       },
                       controller: "a60",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                           AgEncyID: '',
                           AgenName: '',
                           AgNum: '',
                           AgPerObj: '',
                       }
                   })
                   //.state('MidRes_IssurancePage', {
                   //    url: "/MidRes_IssurancePage",
                   //    templateUrl: function (params) {

                   //        if (params.UserID == '' || params.UserID == '' || params.UserID == '')
                   //            window.location.href = '/#!/_LoginPage'
                   //        else
                   //            return "/a60/a60000?Atu=" + params.UserID;
                   //    },
                   //    controller: "a60",
                   //    controllerAs: "ctrl",
                   //    params: {
                   //        UserID: '',
                   //        UserName: '',
                   //        AgEncyID: '',
                   //        AgenName: '',


                   //    }
                   //})
                   .state('Orange_IssurancePage', {
                       url: "/Orange_IssurancePage",
                       templateUrl: function (params) {
                        
                           if (params.UserID == '' || params.UserID == '' || params.UserID == '')
                               window.location.href = '/#!/_LoginPage'
                           else
                               return "/a50/a50000?Atu=" + params.UserID; 
                       },
                       controller: "a50",
                       controllerAs: "ctrl",
                       params: {  
                           UserID: '',
                           UserName: '',
                           AgEncyID: '',
                           AgenName: '',
                           AgNum: '',
                           AgPerObj: '',
                          

                       }
                   })
                   .state('Trav_SearchPage', {
                       url: "/Trav_SearchPage",
                       templateUrl: function (params) {
                           if (params.UserID == '' || params.UserID == '' || params.UserID == '')
                               window.location.href = '/#!/_LoginPage'
                           else
                               return "/a49/a490001?Atu=" + params.UserID;
                       },
                       controller: "a49",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                           AgEncyID: '',
                           AgenName: '',
                           AgNum: '',
                           AgPerObj: '',
                          

                       }
                   })
                   .state('Trav_ReportesPage', {
                       url: "/Trav_ReportesPage",
                       templateUrl: function (params) {
                           if (params.UserID == '' || params.UserID == '' || params.UserID == '')
                               window.location.href = '/#!/LoginPage'
                           else
                               return "/a48/a48001?Atu=" + params.UserID;
                       },
                       controller: "a48",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                           AgEncyID: '',
                           AgenName: '',
                           AgenName: '',
                       }
                   })
                   .state('Trav_IssuancePage', {
                       url: "/Trav_IssuancePage",
                       templateUrl: function (params) {

                           if (params.UserID == '' || params.UserID == '' || params.UserID == '')
                               window.location.href = '/#!/LoginPage'
                           else
                               return "/a47/a47001?Atu=" + params.UserID; 
                       },
                       controller: "a47",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                           AgEncyID: '',
                           AgenName: '', 
                       }
                   })
        

                   .state('OtherServices', {
                       url: "/OtherServices",
                       templateUrl: function (params) {
                          
                           if (params.UserID == '' || params.UserID == '' || params.UserID == '')
                               window.location.href = '/#!/LoginPage'
                           else
                               return "/a46/a460001?Atu=" + params.UserID;
                       },
                       controller: "a46",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                           AgEncyID: '',
                           RepPer: '',
                           RenewPer: '',
                       
                       }
                   })
                   .state('InsuredCoPage', {
                       url: "/InsuredCoPage",
                       templateUrl: function (params) {

                           if (params.UserID == '' || params.UserID == '' || params.UserID == '')
                               window.location.href = '/#!/LoginPage'
                           else
                               return "/a45/a40002?Atu=" + params.UserID;
                       },
                       controller: "a45",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                           AgEncyID: '',
                          
                       }
                   })
                   .state('companiespage', {
                       url: "/companiespage",
                       templateUrl: function (params) {

                           if (params.UserID == '' || params.UserID == '' || params.UserID == '')
                               window.location.href = '/#!/LoginPage'
                           else
                               return "/a45/a40001?Atu=" + params.UserID;
                       },
                       controller: "a45",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                           AgEncyID: '',
                         
                       }
                   })
                   .state('IssuancePage', {
                       url: "/IssuancePage",
                       templateUrl: function (params) {

                           if (params.UserID == '' || params.UserID == '' || params.UserID == '')
                               window.location.href = '/#!/LoginPage' 
                           else
                               return "/a40/a40000?Atu=" + params.UserID ;
                       },
                       controller: "a40",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                           AgEncyID: '',
                           AgenName: '',
                           AgNum: '',
                           AgPerObj: '',
                       }
                   })
                   .state('UsersPage', {
                       url: "/UsersPage",
                       templateUrl:  function(params) {
                           if (params.UserID == '' || params.UserID == '' || params.UserID == '')
                                  window.location.href = '/#!/LoginPage' 
                           else
                               return "/a26/a25001?Atu=" + params.UserID;
                       },
                       controller: "a26",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                           AgEncyID: '',
                           AgenName: '',
                           AgNum :'',
                       }
                   })
                   .state('ReportesPage', {
                       url: "/ReportesPage",
                       templateUrl:  function(params  ) {
                           if (params.UserID == '' || params.UserID == '' || params.UserID == '')
                                  window.location.href = '/#!/LoginPage' 
                           else
                               return "/a44/a40100?Atu=" + params.UserID;
                       },
                       controller: "a44",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                           AgEncyID: '',
                           AgenName: '',
                       }
                   })
                   .state('HomePage', {
                       url: "/HomePage",
                       templateUrl: function (params) {
                           if (params.UserID == null || params.UserID == undefined || params.UserID == '')
                                  window.location.href = '/#!/LoginPage' 
                           else
                               return "/a27/a26001?Atu=" + params.UserID;
                       },
                       controller: "a27",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                           AgEncyID: '',
                           AgenName: '',
                           AgNum:'',
                           UserType:'',
                       }
                   })
                   .state('StockRequestsPage', {
                       url: "/StockRequestsPage",
                       templateUrl: function (params) {
                           if (params.UserID == null || params.UserID == undefined || params.UserID == '')
                               window.location.href = '/#!/LoginPage' 
                           else
                               return "/a61/a61000?Atu=" + params.UserID;
                       },
                       controller: "a61",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                           AgEncyID: '',
                           AgenName: '',
                           AgNum: '',
                       }
                   })
                   .state('LoginPage', {
                       url: "/LoginPage",
                       templateUrl: "/a27/a26000",
                       controller: "a29",
                       controllerAs: "ctrl"

                   })
                   
           }]);
})();

