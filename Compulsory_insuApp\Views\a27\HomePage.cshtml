﻿<link href="~/Content/NewsBar.css?v=*******.16" rel="stylesheet" />
<script src="~/Scripts/popper.min.js?v=*******.15"></script>
<link href="~/Content/Homestyle.css?v=*******.16" rel="stylesheet" />

<div class="container-fluid p-0">
    <!-- Navbar Section -->
    <nav class="navbar navbar-expand-lg navbar-dark cairo" style="background-color: #00468b">
        <div class="container-fluid">
            <button class="navbar-toggler ms-2" type="button" data-bs-toggle="collapse" data-bs-target="#navbarScroll"
                    aria-controls="navbarScroll" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarScroll">
                <!-- News Ticker -->
                <div class="flex-grow-1 mx-2">
                    <div class="d-flex news-container">
                        <div class="d-flex bg-danger py-2 text-white px-3 news-label">
                            <span class="cairo">الأخبار</span>
                        </div>
                        <div class="flex-grow-1 bg-white">
                            <marquee class="news-scroll cairo py-2" 
                                    behavior="scroll" 
                                    direction="right" 
                                    scrollamount="3"
                                    onmouseover="this.stop();" 
                                    onmouseout="this.start();">
                                <div dir="rtl" class="d-inline-block">
                                    <span ng-repeat="x in ctrl.News" class="news-item">
                                        {{x.NewsText}} <span class="dot"></span>
                                    </span>
                                </div>
                            </marquee>
                        </div>
                    </div>
                </div>
                <!-- User Dropdown -->
                <div class="ms-auto">
                    <div class="dropdown">
                        <button class="btn btn-secondary dropdown-toggle" type="button" id="dropdownMenuButton2" data-bs-toggle="dropdown"
                                aria-expanded="false">
                            <span class="bi bi-person me-1"></span>
                            {{ctrl.UserName}} 
                        </button>
                        <ul class="dropdown-menu dropdown-menu-sm-start" aria-labelledby="dropdownMenuButton2" dir="rtl">
                            <li><a class="dropdown-item" href="" ng-if="ctrl.UserType == 1" ng-click="ctrl.BtnUsers()">المستخدمين</a></li>
                            <li><a class="dropdown-item" href="" data-bs-toggle="modal" data-bs-target="#changePassModel">تغير كلمة المرور</a></li>
                            <li><a class="dropdown-item" ui-sref="LoginPage">تسجيل خروج</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <div class="row mb-4">
            <div class="col-12">
                <h4 class="fw-bolder cairo text-center">مركز الإصدار : {{ctrl.AgenName}}.</h4>
            </div>
        </div>

        <!-- Cards Grid -->
        <div class="row row-cols-1 row-cols-md-2 row-cols-lg-4 g-4">
            <!-- Compulsory Insurance Card -->
            <div class="col" ng-if="ctrl.AgPerObj.Cump_Sys">
                <div class="card h-100 shadow-sm border-0 rounded-3 hover-card">
                    <button class="btn btn-outline-primary h-100 border-0 p-3" ng-click="ctrl.a270012()">
                        <div class="d-flex flex-column align-items-center">
                            <div class="mb-3">
                                <img class="img-fluid svgStyle1" style="max-height: 120px;" ng-src="~/Imges/Car crash.gif" alt="Car Insurance" />
                            </div>
                            <h5 class="card-title fw-bold text-darkBlue cairo">إصدار وثيقة تأمين إجباري</h5>
                        </div>
                    </button>
                </div>
            </div>

            <!-- Orange System Card -->
            <div class="col" ng-if="ctrl.AgPerObj.Orange_Sys">
                <div class="card h-100 shadow-sm border-0 rounded-3 hover-card">
                    <button class="btn btn-outline-warning h-100 border-0 p-3" ng-click="ctrl.a270018()">
                        <div class="d-flex flex-column align-items-center">
                            <div class="mb-3">
                                <img class="img-fluid svgStyle1" style="max-height: 120px;" ng-src="~/Imges/Fast car.gif" alt="Orange System" />
                            </div>
                            <h5 class="card-title fw-bold text-darkBlue cairo">المنظومة البرتقالية</h5>
                        </div>
                    </button>
                </div>
            </div>

            <!-- Travel Insurance Card -->
            <div class="col" ng-if="ctrl.AgPerObj.Travel_Sys">
                <div class="card h-100 shadow-sm border-0 rounded-3 hover-card">
                    <button class="btn btn-outline-success h-100 border-0 p-3" ng-click="ctrl.a270014()">
                        <div class="d-flex flex-column align-items-center">
                            <div class="mb-3">
                                <img class="img-fluid svgStyle1" style="max-height: 120px;" ng-src="~/Imges/Travel insurance.gif" alt="Travel Insurance" />
                            </div>
                            <h5 class="card-title fw-bold text-darkBlue cairo">إصدار تأمين المسافرين</h5>
                        </div>
                    </button>
                </div>
            </div>
            <div class="col" ng-if="ctrl.AgPerObj.medical_sys">
                <div class="card h-100 shadow-sm border-0 rounded-3">
                    <button  class="btn btn-outline-danger h-100 border-0 p-3" ng-click="ctrl.a270066()">
                        <div class="d-flex flex-column align-items-center">
                            <div class="mb-3">
                                <img class="img-fluid svgStyle1" style="max-height: 120px;" ng-src="~/Imges/ChatGPT Image Apr 17, 2025, 09_45_42 PM.png" alt="Medical Liability" />
                            </div>
                            <h5 class="card-title fw-bold text-darkBlue cairo">إصدار وثيقة المسؤولية الطبية</h5>
                     
                        </div>
                    </button>
                </div>
            </div>
           
            <div class="col">
                <div class="card h-100 shadow-sm border-0 rounded-3">
                    <button class="btn btn-outline-info h-100 border-0 p-3" ng-click="ctrl.a270013()">
                        <div class="d-flex flex-column align-items-center">
                            <div class="mb-3">
                                <img class="img-fluid svgStyle1" style="max-height: 120px;" ng-src="~/Imges/30b001306e662f8c7ef387c2bf2edb77.jpg" alt="Medical Liability" />
                            </div>
                            <h5 class="card-title fw-bold text-darkBlue cairo">التقارير</h5>

                        </div>
                    </button>
                </div>
            </div>

            <!-- Stock Requests Card -->
            <div class="col" ng-if="ctrl.AgPerObj.inventory_requests">
                <div class="card h-100 shadow-sm border-0 rounded-3 hover-card">
                    <button class="btn btn-outline-secondary h-100 border-0 p-3" ng-click="ctrl.StockRequestsPage()">
                        <div class="d-flex flex-column align-items-center">
                            <div class="mb-3">
                                <img class="img-fluid svgStyle1" style="max-height: 120px;" ng-src="~/Imges/Filing system.gif" alt="Stock Requests" />
                            </div>
                            <h5 class="card-title fw-bold text-darkBlue cairo">طلبات المخزون</h5>
                        </div>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Change Password Modal -->
<div class="modal fade" id="changePassModel" tabindex="-1" aria-labelledby="changePassModelLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <form name="FrmUpdatePass" class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="changePassModelLabel">{{ctrl.titel}}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">كلمة المرور القديمة</label>
                    <input required type="password" ng-model="ctrl.OldPassword" class="form-control" name="OldPassword" placeholder="كلمة المرور القديمة">
                    <div class="form-text text-danger" ng-show="FrmUpdatePass.OldPassword.$error.required">يجب إدخال هذا الحقل</div>
                </div>
                <div class="mb-3">
                    <label class="form-label">كلمة المرور الجديدة</label>
                    <input required type="password" pattern="^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?!.*\s).*$" ng-model="ctrl.NewPassword" class="form-control" name="NewPassword" placeholder="كلمة المرور الجديدة">
                    <div class="form-text" ng-class="{'text-danger': FrmUpdatePass.NewPassword.$error.required || FrmUpdatePass.NewPassword.$error.pattern, 'text-success': FrmUpdatePass.NewPassword.$valid}">
                        <span ng-show="FrmUpdatePass.NewPassword.$error.required">يجب إدخال هذا الحقل</span>
                        <span ng-show="FrmUpdatePass.NewPassword.$error.pattern">يجب أن يحتوي على حروف كبيرة و حروف صغيرة وأرقام</span>
                        <span ng-show="FrmUpdatePass.NewPassword.$valid" class="bi-check-lg"></span>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">تأكيد كلمة المرور</label>
                    <input required type="password" ng-model="ctrl.ReNewPassword" class="form-control" name="ReNewPassword" placeholder="تأكيد كلمة المرور">
                    <div class="form-text text-danger" ng-show="!FrmUpdatePass.NewPassword.$error.required && ctrl.NewPassword != ctrl.ReNewPassword">
                        تأكيد كلمة المرور غير متطابق
                    </div>
                </div>
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-primary" ng-disabled="FrmUpdatePass.$invalid || (ctrl.NewPassword != ctrl.ReNewPassword)" ng-click="ctrl.a27015()">حفظ</button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </form>
    </div>
</div>