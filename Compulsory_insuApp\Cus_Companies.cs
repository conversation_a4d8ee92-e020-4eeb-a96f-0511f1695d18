//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Compulsory_insuApp
{
    using System;
    using System.Collections.Generic;
    
    public partial class Cus_Companies
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Cus_Companies()
        {
            this.CompulsoryInsurenceTB = new HashSet<CompulsoryInsurenceTB>();
            this.Comp_Agency = new HashSet<Comp_Agency>();
        }
    
        public System.Guid Comp_ID { get; set; }
        public string CompName { get; set; }
        public bool IsTax { get; set; }
        public bool IsStamp { get; set; }
        public bool IsInsu { get; set; }
        public bool IsSupfees { get; set; }
        public byte Status { get; set; }
        public System.DateTime InsertDate { get; set; }
        public Nullable<System.DateTime> UpdateDate { get; set; }
        public System.Guid InsertBy { get; set; }
        public Nullable<System.Guid> UpdateBy { get; set; }
        public Nullable<double> SupfeesValue { get; set; }
        public Nullable<bool> CanModfyName { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<CompulsoryInsurenceTB> CompulsoryInsurenceTB { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Comp_Agency> Comp_Agency { get; set; }
    }
}
