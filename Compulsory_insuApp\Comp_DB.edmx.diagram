<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
 <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <edmx:Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <!-- Diagram content (shape and connector positions) -->
    <edmx:Diagrams>
      <Diagram DiagramId="1c34a0ad2c964700bd957ffe156526bb" Name="Diagram1" ZoomLevel="63">
        <EntityTypeShape EntityType="Insh_AppsDBModel.AgenCat" Width="1.5" PointX="3" PointY="83.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.AgencyPermission" Width="1.5" PointX="7.5" PointY="79.375" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.AgencySystems" Width="1.5" PointX="5.75" PointY="0.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.AgUsers" Width="1.5" PointX="7.5" PointY="141.125" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Balance" Width="1.5" PointX="7.5" PointY="83.625" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.CarsBrand" Width="1.5" PointX="14.25" PointY="75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Cities" Width="1.5" PointX="3" PointY="104.5" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Colors" Width="1.5" PointX="14.25" PointY="106.125" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.CompulsoryInsurenceTB" Width="1.5" PointX="16.5" PointY="80.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.CompulsoryPriceDetails" Width="1.5" PointX="14.25" PointY="96.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.CompulsoryPriceMaster" Width="1.5" PointX="14.25" PointY="102.375" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.CoNews" Width="1.5" PointX="7.5" PointY="68" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.ConMangUsers" Width="1.5" PointX="5.25" PointY="72" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.ContryMangments" Width="1.5" PointX="3" PointY="87.25" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Country" Width="1.5" PointX="0.75" PointY="66" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Country_Condition" Width="1.5" PointX="5.75" PointY="4.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Cus_Companies" Width="1.5" PointX="11.25" PointY="71" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.DocTypes" Width="1.5" PointX="14.25" PointY="72.5" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.DurationsCat" Width="1.5" PointX="0.75" PointY="5.5" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.DuratuionTB" Width="1.5" PointX="3" PointY="5" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.GivenSearils" Width="1.5" PointX="9.75" PointY="99.5" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.MidcRes_Address" Width="1.5" PointX="7.75" PointY="0.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.MidcRes_GivenSearils" Width="1.5" PointX="9.75" PointY="117.625" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.MidcRes_MaritalStatus" Width="1.5" PointX="7.75" PointY="4.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.MidcRes_Nationality" Width="1.5" PointX="0.75" PointY="8.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.MidcRes_PrintedSerals" Width="1.5" PointX="2.75" PointY="8.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.MidcRes_PrinterTB" Width="1.5" PointX="4.75" PointY="8.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.MidcRes_Profession" Width="1.5" PointX="6.75" PointY="8.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.MidcRes_Seariles" Width="1.5" PointX="7.5" PointY="155.25" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.MidcRes_SearStatus" Width="1.5" PointX="9.75" PointY="128.5" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.MidcRes_SerialNums" Width="1.5" PointX="12" PointY="117.125" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.MidcRes_UsersSearils" Width="1.5" PointX="15" PointY="115.5" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.NotesTB" Width="1.5" PointX="18.75" PointY="85.125" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Offices" Width="1.5" PointX="8.75" PointY="8.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.oldOwner" Width="1.5" PointX="18.75" PointY="82.625" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Orang_Country" Width="1.5" PointX="5.25" PointY="102.875" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Orange_Insurance_Clause" Width="1.5" PointX="5.25" PointY="107.125" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Orange_Insurance_Policy" Width="1.5" PointX="7.5" PointY="87.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Orange_nots_replays" Width="1.5" PointX="12" PointY="75.875" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Orange_poli_Nots" Width="1.5" PointX="9.75" PointY="75.625" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.OrangeCars" Width="1.5" PointX="5.25" PointY="95.5" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.OtherServHis" Width="1.5" PointX="18.75" PointY="88.875" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.OtherSettings" Width="1.5" PointX="3" PointY="1" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.PrintedSerals" Width="1.5" PointX="9.75" PointY="0.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.PrinterTB" Width="1.5" PointX="9.75" PointY="3.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.ResAgencies" Width="1.5" PointX="7.5" PointY="164.375" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Seariles" Width="1.5" PointX="7.5" PointY="119.25" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.SearStatus" Width="1.5" PointX="7.5" PointY="123.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.SerialNums" Width="1.5" PointX="9.75" PointY="122.625" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Systems" Width="1.5" PointX="0.75" PointY="1.25" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_AgesTypes" Width="1.5" PointX="8.25" PointY="58.625" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_Balance" Width="1.5" PointX="7.5" PointY="107.25" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_Cities" Width="1.5" PointX="10.5" PointY="63.625" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_Country" Width="1.5" PointX="8.25" PointY="63.25" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_DocReNew" Width="1.5" PointX="12.75" PointY="91.5" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_DocReplacment" Width="1.5" PointX="12.75" PointY="79" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_DocTax" Width="1.5" PointX="18.75" PointY="78.375" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_DocumentType" Width="1.5" PointX="10.75" PointY="9.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_DurationTypes" Width="1.5" PointX="8.25" PointY="54.5" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_GivenSearils" Width="1.5" PointX="9.75" PointY="107.875" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_OtherServPrices" Width="1.5" PointX="11.75" PointY="0.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_PrintedSerals" Width="1.5" PointX="2.75" PointY="11.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_PrinterTB" Width="1.5" PointX="11.75" PointY="3.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_Questions" Width="1.5" PointX="4.75" PointY="12.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_Seariles" Width="1.5" PointX="7.5" PointY="136.625" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_SearStatus" Width="1.5" PointX="9.75" PointY="104.625" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_SerialNums" Width="1.5" PointX="12" PointY="110.375" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_UsersSearils" Width="1.5" PointX="15" PointY="110.5" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_Virebles" Width="1.5" PointX="12.75" PointY="6.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_Zoon" Width="1.5" PointX="8.25" PointY="113.625" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.UsersSearils" Width="1.5" PointX="7.5" PointY="159.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.VehicleNationality" Width="1.5" PointX="5.25" PointY="99.25" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Virebles" Width="1.5" PointX="16.5" PointY="76.125" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidcRes_SerialNums_AgUsers1" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidcRes_UsersSearils_AgUsers" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_SerialNums_AgUsers1" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_UsersSearils_AgUsers" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_CompulsoryInsurenceTB_CarsBrand" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Cities_Country" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_CompulsoryInsurenceTB_Cities" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_CompulsoryInsurenceTB_Colors" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_CompulsoryInsurenceTB_CompulsoryPriceDetails" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_CompulsoryInsurenceTB_CompulsoryPriceMaster" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_CompulsoryInsurenceTB_Country" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_CompulsoryInsurenceTB_Cus_Companies" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_CompulsoryInsurenceTB_DocTypes" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_NotesTB_CompulsoryInsurenceTB" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_oldOwner_CompulsoryInsurenceTB" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_OtherServHis_CompulsoryInsurenceTB" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_OtherServHis_CompulsoryInsurenceTB1" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_ConMangUsers_ContryMangments" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_DuratuionTB_DurationsCat" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_GivenSearils_Seariles" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidcRes_GivenSearils_MidcResSeariles" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidcRes_SerialNums_MidcRes_Seariles" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidcRes_SerialNums_MidcRes_SearStatus" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidcRes_SerialNums_Trav_GivenSearils" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidcRes_UsersSearils_Trav_GivenSearils" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Orange_Insurance_Policy_Orang_Country" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Orange_Insurance_Policy_Orange_Insurance_Clause" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Orange_Insurance_Policy_OrangeCars" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Orange_Insurance_Policy_VehicleNationality" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Orange_poli_Nots_Orange_Insurance_Policy" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Orange_nots_replays_Orange_poli_Nots" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_OtherSettings_Systems" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_SerialNums_SearStatus" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_SerialNums_UsersSearils" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_Cities_Trav_Country" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_DocTax_Virebles" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_GivenSearils_Trav_Seariles" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_SerialNums_Trav_GivenSearils" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_UsersSearils_Trav_GivenSearils" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_SerialNums_Trav_Seariles" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_SerialNums_Trav_SearStatus" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_InsuranceDocument" Width="1.5" PointX="10.5" PointY="80.625" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_InsuranceDocument_Trav_AgesTypes" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_DocReNew_Trav_InsuranceDocument" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_DocReNew_Trav_InsuranceDocument1" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_DocReNew_Trav_InsuranceDocument2" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_DocReplacment_Trav_InsuranceDocument" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_DocReplacment_Trav_InsuranceDocument1" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_DocTax_Trav_InsuranceDocument" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_InsuranceDocument_Trav_DurationTypes" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_InsuranceDocument_Trav_Zoon" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.MidRes_AcademicQualification" Width="1.5" PointX="12.125" PointY="15.375" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.MidRes_Percentage" Width="1.5" PointX="0.75" PointY="16.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Smart_page_Cus" Width="1.5" PointX="13.375" PointY="11.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.AgPermissionsList" Width="1.5" PointX="7.375" PointY="15" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_AgencyPermission_AgPermissionsList" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Agency" Width="1.5" PointX="5.25" PointY="79.25" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.AgenSysProfit" Width="1.5" PointX="7.5" PointY="46.25" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Agency_AgenCat" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Agency_Cities" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Agency_ContryMangments" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Agency_Country" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_AgencyPermission_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_AgenSysProfit_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_AgUsers_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Balance_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_CompulsoryInsurenceTB_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_CoNews_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_GivenSearils_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidcRes_GivenSearils_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidcRes_Seariles_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidcRes_SerialNums_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidcRes_UsersSearils_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Orange_Insurance_Policy_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_ResAgencies_Agency1" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Seariles_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_SerialNums_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_Balance_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_GivenSearils_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_InsuranceDocument_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_Seariles_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_SerialNums_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_UsersSearils_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_UsersSearils_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_AgenSysProfit_Systems" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.MidRes_InsuranceDocument" Width="1.5" PointX="14.375" PointY="43.25" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidRes_InsuranceDocument_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidRes_InsuranceDocument_MidcRes_Address" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidRes_InsuranceDocument_MidcRes_MaritalStatus" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidRes_InsuranceDocument_MidcRes_Nationality" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidRes_InsuranceDocument_MidRes_AcademicQualification" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Oran_GivenSearils" Width="1.5" PointX="9.75" PointY="92.125" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Oran_PrintedSerals" Width="1.5" PointX="17.75" PointY="113" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Oran_Seariles" Width="1.5" PointX="7.5" PointY="74.625" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Oran_UsersSearils" Width="1.5" PointX="17" PointY="105.625" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Oran_GivenSearils_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Oran_PrintedSerals_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Oran_Seariles_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Oran_UsersSearils_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Oran_PrintedSerals_AgUsers" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Oran_UsersSearils_AgUsers" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Oran_GivenSearils_Seariles" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Oran_UsersSearils_GivenSearils" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.monthlyAgenFinnce" Width="1.5" PointX="9.375" PointY="14.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.EquipmentCategories" Width="1.5" PointX="14.375" PointY="0.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.SerialHistory" Width="1.5" PointX="10.5" PointY="43.375" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.SerialStatuses" Width="1.5" PointX="8.25" PointY="40.625" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.StockInventory" Width="1.5" PointX="3" PointY="16.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.StockReceipts" Width="1.5" PointX="19" PointY="71.125" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.StockRequestDetails" Width="1.5" PointX="19" PointY="67" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.UnifiedRequests" Width="1.5" PointX="16.75" PointY="66.5" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_SerialHistory_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_UnifiedRequests_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_UnifiedRequests_AgUsers_RequestedBy" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_SerialHistory_SerialStatuses_New" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_SerialHistory_SerialStatuses_Previous" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_SerialHistory_Systems" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_SerialHistory_UnifiedRequests" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_UnifiedRequests_SerialStatuses_Current" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_UnifiedRequests_SerialStatuses_Requested" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_StockInventory_Systems" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_StockReceipts_UnifiedRequests" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_StockRequestDetails_UnifiedRequests" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_UnifiedRequests_Systems" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidRes_InsuranceDocument_MidcRes_Profession" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.AgencyEquipment" Width="1.5" PointX="16.625" PointY="46" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Comp_Agency" Width="1.5" PointX="13.5" PointY="85" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Oran_SearStatus" Width="1.5" PointX="9.75" PointY="131.375" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.RequestTypes" Width="1.5" PointX="14.5" PointY="68.25" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_AgencyEquipment_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Comp_Agency_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_AgencyEquipment_Category" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_CompID" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_UnifiedRequests_RequestTypes" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Oran_SerialNums" Width="1.5" PointX="12" PointY="105.25" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Oran_SerialNums_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Oran_SerialNums_AgUsers" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Oran_SerialNums_GivenSearils" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Oran_SerialNums_Seariles" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Oran_SerialNums_Oran_SearStatus" />
      </Diagram>
      <Diagram DiagramId="4ec3db39232a431eae758a4c77a21e03" Name="Diagram2" >
      </Diagram>
    </edmx:Diagrams>
  </edmx:Designer>
</edmx:Edmx>