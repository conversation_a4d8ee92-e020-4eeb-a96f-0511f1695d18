//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Compulsory_insuApp
{
    using System;
    using System.Collections.Generic;
    
    public partial class AgUsers
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public AgUsers()
        {
            this.MidcRes_SerialNums = new HashSet<MidcRes_SerialNums>();
            this.MidcRes_UsersSearils = new HashSet<MidcRes_UsersSearils>();
            this.Trav_SerialNums = new HashSet<Trav_SerialNums>();
            this.Trav_UsersSearils = new HashSet<Trav_UsersSearils>();
            this.Oran_PrintedSerals = new HashSet<Oran_PrintedSerals>();
            this.Oran_UsersSearils = new HashSet<Oran_UsersSearils>();
            this.UnifiedRequests = new HashSet<UnifiedRequests>();
            this.Oran_SerialNums = new HashSet<Oran_SerialNums>();
        }
    
        public System.Guid AgUserID { get; set; }
        public string AgUserName { get; set; }
        public Nullable<System.DateTime> InsertDate { get; set; }
        public string Passowrd { get; set; }
        public string InsertBy { get; set; }
        public string UpdateBy { get; set; }
        public Nullable<System.DateTime> UpdateDate { get; set; }
        public Nullable<System.Guid> AgentId { get; set; }
        public Nullable<bool> IsAdmin { get; set; }
        public Nullable<byte> Status { get; set; }
        public string FullName { get; set; }
        public Nullable<bool> ISReportUser { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<MidcRes_SerialNums> MidcRes_SerialNums { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<MidcRes_UsersSearils> MidcRes_UsersSearils { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Trav_SerialNums> Trav_SerialNums { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Trav_UsersSearils> Trav_UsersSearils { get; set; }
        public virtual Agency Agency { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Oran_PrintedSerals> Oran_PrintedSerals { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Oran_UsersSearils> Oran_UsersSearils { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<UnifiedRequests> UnifiedRequests { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Oran_SerialNums> Oran_SerialNums { get; set; }
    }
}
