//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Compulsory_insuApp
{
    using System;
    using System.Collections.Generic;
    
    public partial class Oran_SerialNums
    {
        public System.Guid SerialID { get; set; }
        public Nullable<int> SerialNumber { get; set; }
        public Nullable<System.Guid> SearilsID { get; set; }
        public Nullable<System.Guid> AgUserID { get; set; }
        public Nullable<System.Guid> AgencyID { get; set; }
        public Nullable<System.Guid> SerGivID { get; set; }
        public Nullable<byte> Status { get; set; }
        public Nullable<System.Guid> InsertedBy { get; set; }
        public Nullable<System.DateTime> InsertDate { get; set; }
        public Nullable<System.DateTime> UpdateDate { get; set; }
        public Nullable<System.Guid> UpdatedBy { get; set; }
        public Nullable<int> StatusID { get; set; }
    
        public virtual Agency Agency { get; set; }
        public virtual AgUsers AgUsers { get; set; }
        public virtual Oran_GivenSearils Oran_GivenSearils { get; set; }
        public virtual Oran_Seariles Oran_Seariles { get; set; }
        public virtual Oran_SearStatus Oran_SearStatus { get; set; }
    }
}
