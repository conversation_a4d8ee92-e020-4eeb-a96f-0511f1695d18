//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Compulsory_insuApp
{
    using System;
    using System.Collections.Generic;
    
    public partial class UnifiedRequests
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public UnifiedRequests()
        {
            this.SerialHistory = new HashSet<SerialHistory>();
            this.StockReceipts = new HashSet<StockReceipts>();
            this.StockRequestDetails = new HashSet<StockRequestDetails>();
        }
    
        public System.Guid RequestID { get; set; }
        public int RequestTypeID { get; set; }
        public System.Guid AgencyID { get; set; }
        public System.Guid RequestedBy { get; set; }
        public byte Status { get; set; }
        public System.DateTime RequestDate { get; set; }
        public Nullable<System.Guid> ReviewedBy { get; set; }
        public Nullable<System.DateTime> ReviewDate { get; set; }
        public string RejectionReason { get; set; }
        public Nullable<System.Guid> SysTypeID { get; set; }
        public Nullable<int> RequestedQuantity { get; set; }
        public Nullable<int> ApprovedQuantity { get; set; }
        public Nullable<System.Guid> AssignedBy { get; set; }
        public Nullable<System.DateTime> AssignedDate { get; set; }
        public string SerialNumber { get; set; }
        public Nullable<int> CurrentStatusID { get; set; }
        public Nullable<int> RequestedStatusID { get; set; }
        public string ChangeReason { get; set; }
        public Nullable<System.Guid> ExecutedBy { get; set; }
        public Nullable<System.DateTime> ExecutedDate { get; set; }
        public string ReceiptNumber { get; set; }
        public Nullable<System.DateTime> ReceiptDate { get; set; }
        public Nullable<System.Guid> ReceivedBy { get; set; }
        public Nullable<System.DateTime> ReceivedDate { get; set; }
        public string ReceivedNotes { get; set; }
        public string Notes { get; set; }
        public System.DateTime CreatedDate { get; set; }
        public Nullable<System.DateTime> UpdatedDate { get; set; }
        public Nullable<System.Guid> UpdatedBy { get; set; }
        public long RequestSeqNumber { get; set; }
    
        public virtual Agency Agency { get; set; }
        public virtual AgUsers AgUsers { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<SerialHistory> SerialHistory { get; set; }
        public virtual SerialStatuses SerialStatuses { get; set; }
        public virtual SerialStatuses SerialStatuses1 { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<StockReceipts> StockReceipts { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<StockRequestDetails> StockRequestDetails { get; set; }
        public virtual Systems Systems { get; set; }
        public virtual RequestTypes RequestTypes { get; set; }
    }
}
