﻿(function () {
    'use strict';
    var controllerId = 'a47';
    angular
        .module('App')
        .controller(controllerId, ["DataService", '$filter', "Notification", "blockUI", "$stateParams", "$state", '$timeout', '$log', func]);

    function func(DataService, $filter, Notification, blockUI, $stateParams, $state, $q, $timeout, $log) {
        var vm = this;

        vm.UserID = $stateParams.UserID;
        vm.UserName = $stateParams.UserName;
        vm.AgEncyID = $stateParams.AgEncyID;
        vm.AgenName = $stateParams.AgenName;
        vm.AgNum = $stateParams.AgNum;

        if (vm.UserID == undefined || vm.UserID == null || vm.UserID == '')
            $state.go('LoginPage');
        else {
            a47002();
            a470010();
            //a47009();
            vm.simulateQuery = false;
            vm.isDisabled = false;
            vm.ShowFlag = 0;
            vm.IsExistPayperNum = 0;
            vm.Intype = 0;
            vm.counters = 0;
            vm.currentPage = 1;
            vm.SearchFlag = 0;
            vm.printflag = 0
            vm.setPage = function (pageNo) {
                vm.currentPage = pageNo;
            };
            vm.CloseSreach = function () {
                if (vm.Stcount == 1) {

                    vm.SearchFlag = 0;
                    vm.InsDoc = '';
                    a470010();
                } else {
                    Notification.error({ message: "حدث خطأ قم بالحث اولا ", title: 'قم بالحث اولا' });
                }


            }
            vm.pageChanged = function () {
                a470010();
            };

            vm.maxSize = 10;
            vm.bigTotalItems = 0;
            vm.bigCurrentPage = 0;
            vm.searchResult = false;
        }

        vm.Parm1 = 0;
        vm.massge = '';
        vm.FullSearch = function () {
            vm.massge = 'البحث  المتقدم ';
            vm.Parm1 = 1;
            vm.SearchFlag = 1;
            $('#fullshow').modal('show');
            vm.Stcount = 1;
        }
        vm.Stcount = 0;
        vm.SelectedZoonChnaged = function () {
            vm.MainObj.Zid = vm.Zid.ZoonID;
            vm.ZoonTxt = vm.Zid.ZoonText;
        }
        vm.a47011 = function (x) {
            blockUI.start();

            DataService.a47011(x.InsID, vm.UserID, vm.AgEncyID).then(function (Response) {
                    if (Response.data.ErrorCode == 0) {
                        a470010();
                        blockUI.stop();
                    }
                    if (Response.data.ErrorCode == 1) {
                        Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
                        blockUI.stop();
                    }
                    if (Response.data.ErrorCode == 5) {
                        Notification.error({ message: Response.data.ErorrMessage, title: 'خطأ' });
                        blockUI.stop();
                    }
                    if (Response.data.ErrorCode == 3) {
                        $state.go('LoginPage');
                        blockUI.stop();
                    }
                }, function (error) {
                    Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
                    blockUI.stop();
                });
        }
        vm.a460030 = function () {
            blockUI.start();
            DataService.a460030(vm.AgEncyID, vm.UserID, vm.Search, vm.Parm1).then(function (Response) {
                if (Response.data.ErrorCode == 0) {

                    vm.InsDoc = Response.data.InsDoc;
                    $('#fullshow').modal('hide');
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 1) {
                    Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 5) {
                    Notification.error({ message: Response.data.ErorrMessage, title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 3) {
                    $state.go('LoginPage');
                    blockUI.stop();
                }
            }, function (error) {
                Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
                blockUI.stop();
            });
        }

      

        vm.a47004 = function () {
            blockUI.start();
            DataService.a47004(vm.MainObj, vm.AgEncyID).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
                    vm.Doc = Response.data.Doc;
                    vm.Tax = Response.data.Tax;
                    vm.var = 'https://www.aic-mang.com/#!/HomePage/' + vm.Doc.docnume + '/' ;
                    vm.IsExistPayperNum = 0;
                    vm.printflag = 1;
                    vm.UpdateFalg = 1;
                    $('#NewItem').modal('hide');
                    a470010();
                    setTimeout(function () {

                        window.focus();
                        window.print();
                    }, 1000);
                    
                }
                if (Response.data.ErrorCode == 1) {
                    Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 5) {
                    Notification.error({ message: Response.data.ErorrMessage, title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 4) {
                    vm.IsExistPayperNum = 1;
                    blockUI.stop();

                }
                if (Response.data.ErrorCode == 3) {
                    $state.go('LoginPage');
                    blockUI.stop();
                }
            }, function (error) {
                Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
                blockUI.stop();
            });
        }
        vm.a470013 = function (x) {
            vm.DlFalg = 1;
            vm.UpdateFalg = 0;
            vm.IsExistPayperNum = 0;
            vm.Intype = 0;
            vm.DlTitle = "تعديل وثيقة";
            vm.MainObj.InsID = x.InsID;
            vm.MainObj.Months = x.Months.toString();
            vm.MainObj.CuName = x.CuName;
            vm.MainObj.Cus_Phone = x.Cus_Phone;
            vm.MainObj.BDay = x.bDate;
            vm.MainObj.zid = x.zid;
            vm.MainObj.InsuNumber = '';
            vm.MainObj.Cus_Phone = x.Cus_Phone;
            vm.MainObj.SDate = new Date(x.SFullDate);
            vm.MainObj.Gender = x.Gender.toString();
            vm.MainObj.clintDate = new Date();
            $('#NewItem').modal('show');
        }
        vm.PrintCalInv = function () {
            window.print();
        }
        vm.a47007 = function () {
            blockUI.start();
            DataService.a47006(vm.CalObj, vm.AgEncyID, vm.DocTable).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
                    vm.DocTable = Response.data.DocCla;
                    vm.Summ = Response.data.Summ;
                    CalObjClear();

                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 1) {
                    Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 5) {
                    Notification.error({ message: Response.data.ErorrMessage, title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 3) {
                    $state.go('LoginPage');
                    blockUI.stop();
                }
            }, function (error) {
                Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
                blockUI.stop();
            });
        }


        vm.CalObj = {
            Count: '',
            NameWorkPlace:'',
            UsID: vm.UserID,
            BDay: '',
            Months: '',
            Zid: '',
            DocVal: '',
            YearOfBirth: '',
            AgeDesc: '',
            Duration: '',
            Totoal: '',
            Stamp: '',
            Eshraf: '',
            Tax: '',
            Esdar: '',
         
        }
        vm.CalcuLateDoc = function () {
            vm.DlFalg = 0;
            vm.xsss = 0;
            CalObjClear();
            vm.DocTable = [];;
            vm.TotoalCal = 0;
        }
        function CalObjClear() {
            vm.CalObj.Count = '';
            vm.CalObj.BDay = '';
            vm.CalObj.Months = "";
            vm.CalObj.Zid = "";
            vm.CalObj.NameWorkPlace = "";
        }
        vm.print = function() {
            vm.printflag = 1;
            vm.ShowFlag = 0;
            setTimeout(function () {
                window.focus();
                window.print();
            }, 1000);
        }
        vm.printTempor = function () {
            vm.printflag = 4;
            $('#CalDoc').modal('hide');
            setTimeout(function () {
                window.print();
            }, 1000);
            vm.ShowFlag = 0;
        }
        vm.a00047 = function (x) {
            vm.InsID = x.InsID;
            vm.DlTitle = "حالة الوثيقة";
            vm.DlMessage = "هل تريد حدف الوثيقة رقم: ";
            vm.DocNum = x.DucNum + " ؟";
            $('#Dl_Message').modal('show');
        }

        vm.a47008 = function (x) {
            blockUI.start();
            DataService.a47008(vm.AgEncyID, vm.UserID, x.InsID).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
                    vm.Doc = Response.data.Doc;
                    vm.Taxs = Response.data.Taxs;
                    vm.var = '' + vm.Doc.docnume + '/';
                    vm.ShowFlag = 1;
                  
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 1) {
                    Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 5) {
                    Notification.error({ message: Response.data.ErorrMessage, title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 3) {
                    $state.go('LoginPage');
                    blockUI.stop();
                }
            }, function (error) {
                Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
                blockUI.stop();
            });
        }
        vm.a47005 = function (x) {
            blockUI.start();
                DataService.a47005(x.InsID, vm.UserID, vm.AgEncyID).then(function (Response) {
                    if (Response.data.ErrorCode == 0) {
                        a470010();
                        blockUI.stop();
                    }
                    if (Response.data.ErrorCode == 1) {
                        Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
                        blockUI.stop();
                    }
                    if (Response.data.ErrorCode == 5) {
                        Notification.error({ message: Response.data.ErorrMessage, title: 'خطأ' });
                        blockUI.stop();
                    }
                    if (Response.data.ErrorCode == 3) {
                        $state.go('LoginPage');
                        blockUI.stop();
                    }
                }, function (error) {
                    Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
                    blockUI.stop();
                });
        }
     
        vm.a470011 = function () {

            blockUI.start();
            DataService.a470011(vm.MainObj, vm.AgEncyID).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
                    vm.Doc = Response.data.Doc;
                    vm.Taxs = Response.data.Taxs;
                    vm.var = 'https://www.aic-mang.com/#!/HomePage/' + vm.Doc.docnume + '/' ;
                    vm.IsExistPayperNum = 0;
                    a470010();
                    $('#NewItem').modal('hide');
                    vm.printflag = 1;
                    setTimeout(function () {
                        window.focus();
                        window.print();
                        $('#NewItem').modal('show');

                    }, 700);
                    
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 1) {
                    Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 5) {
                    Notification.error({ message: Response.data.ErorrMessage, title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 4) {
                    vm.IsExistPayperNum = 1;
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 3) {
                    $state.go('LoginPage');
                    blockUI.stop();
                }
            }, function (error) {
                Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
                blockUI.stop();
            });
        }



        //function  a47009(x) {
        //    blockUI.start();
        //    DataService.a47009(vm.AgEncyID, vm.UserID).then(function (Response) {
        //        if (Response.data.ErrorCode == 0) {
        //            vm.Doc = Response.data.Doc;
        //            vm.Taxs = Response.data.Taxs;
        //            vm.var = 'https://www.aic-mang.com/#!/HomePage/' + vm.Doc.docnume + '/' ;
        //            vm.ShowFlag = 1;
        //            blockUI.stop();
        //        }
        //        if (Response.data.ErrorCode == 1) {
        //            Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
        //            blockUI.stop();
        //        }
        //        if (Response.data.ErrorCode == 5) {
        //            Notification.error({ message: Response.data.ErorrMessage, title: 'خطأ' });
        //            blockUI.stop();
        //        }
        //        if (Response.data.ErrorCode == 3) {
        //            $state.go('LoginPage');
        //            blockUI.stop();
        //        }
        //    }, function (error) {
        //        Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
        //        blockUI.stop();
        //    });
        //}



      
        function a47002() {
            blockUI.start();
            DataService.a47002(vm.UserID, vm.AgEncyID).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
                    vm.Objzn = Response.data.Objzn;
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 1) {
                    Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 5) {
                    Notification.error({ message: Response.data.ErorrMessage, title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 3) {
                    $state.go('LoginPage');
                    blockUI.stop();
                }
            }, function (error) {
                Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
                blockUI.stop();
            });
        }

        function a470010() {
            blockUI.start();
            DataService.a470010(vm.AgEncyID, vm.UserID).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
                    vm.InsDoc = Response.data.InsDoc;
                    vm.totalItems = Response.data.total;
                    vm.searchResult = true;
                    vm.Users = Response.data.Users;
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 1) {
                    Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 5) {
                    Notification.error({ message: Response.data.ErorrMessage, title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 3) {
                    $state.go('LoginPage');
                    blockUI.stop();
                }
            }, function (error) {
                Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
                blockUI.stop();
            });
        }

        vm.AddNew = function (x) {
            vm.DlFalg = 0;
            vm.IsExistPayperNum = 0;

            if (x == 1) {
                vm.Intype = 0;
                vm.DlTitle = "إنشاء وثيقة جديدة";
                newForm();
                vm.Desabled = 0;
                vm.require = 1

            }
            else {
                vm.Intype = 0;
                vm.DlTitle = "إنشاء  جديدة";
                /*  Contenio();*/
                vm.Desabled = 1;
                vm.require = 2


            }



        }
        function newForm() {
            vm.MainObj.InsID = '';
            vm.MainObj.Zid = '';
            vm.MainObj.Months = '';
            vm.MainObj.Gender = '';
            vm.MainObj.CuName = '';
            vm.MainObj.Months = '';
            vm.MainObj.CuName = '';
            vm.MainObj.BDay = '';
            vm.MainObj.Cus_Phone = '';
            var Dt = new Date();
            Dt.setDate(Dt.getDate() + 1);
            vm.MainObj.SDate = Dt;
            vm.MainObj.UsID = vm.UserID;
            vm.MainObj.clintDate = new Date();
            vm.MainObj.InsuNumber = '';
            vm.MainObj.Cus_Phone = '';
            vm.IsExistPayperNum = 0;
        }
        vm.SDdateOptions = {
            formatYear: 'yy',
            minDate: new Date(),
            startingDay: 1
        };
        vm.EDdateOptions = {
            formatYear: 'yy',
            minDate: new Date() + 1,
            startingDay: 1
        };
        vm.bdateOptions = {
            maxDate: new Date(),
            minDate: new Date(1930, 12, 31),
            startingDay: 1
        };

        vm.MainObj = {
            InsID:'',
            Zid: '',
            Months: '',
            Gender: '',
            CuName: '',
            NameCustEN: '',
            PassportID: '',
            BDay: '',
            SDate: '',
            UsID: vm.UserID,
            clintDate: new Date(),
            Cus_Phone: '',
            InsuNumber: '',
        }


        //function a470014() {
        //    blockUI.start();
        //    DataService.a470014(vm.UserID, vm.AgEncyID).then(function (Response) {
        //        if (Response.data.ErrorCode == 0) {
        //            vm.Dtypes = Response.data.Dtypes;
        //            vm.sMinDate = Response.data.MinDate;
        //            vm.sMinDate = new Date();
        //            vm.eMinDate = Response.data.MinDate;
        //            vm.RptTypes = Response.data.RptTypes;
        //            vm.RptPrintType = vm.RptTypes[0].ID.toString();
        //            blockUI.stop();
        //        }
        //        if (Response.data.ErrorCode == 1) {
        //            Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
        //            blockUI.stop();
        //        }
        //        if (Response.data.ErrorCode == 5) {
        //            Notification.error({ message: Response.data.ErorrMessage, title: 'خطأ' });
        //            blockUI.stop();
        //        }
        //        if (Response.data.ErrorCode == 3) {
        //            $state.go('LoginPage');
        //            blockUI.stop();
        //        }
        //    }, function (error) {
        //        Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
        //        blockUI.stop();
        //    });
        //}

        vm.BtnHomePage = function () {
            $state.go('HomePage', { 'UserID': vm.UserID, 'UserName': vm.UserName, 'AgEncyID': vm.AgEncyID, 'AgenName': vm.AgenName, "AgNum": vm.AgNum })
        }
        


      
    }

})();
